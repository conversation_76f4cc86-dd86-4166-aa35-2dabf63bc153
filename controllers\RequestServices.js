require("dotenv").config();
const nodemailer = require("nodemailer");
const ProjectError = require("../helper/error");
const jwt = require("jsonwebtoken");

const {
    sendEmail
} = require("../utils/email");
const sendMailRequest = async (req, res) => {

    let token = req.cookies.user
    let name = ''



    const { email,last,first,jbt,country,phone,np,ne } = req.body
    const subject = 'Request services'
    const html = `<html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Request services</title>
    </head>
    <body style="font-family: 'Arial', sans-serif;">
    
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; text-align: center; border: 1px solid #ccc; border-radius: 10px;">
        <h2 style="color: #333;">Request services</h2>
         <p>${last} ${first} has request service</p>
         <p>following information</p>
         <p>last name: ${last}</p>
         <p>first name: ${first}</p>
         <p>email: ${email}</p>
         <p>job title: ${jbt}</p>
         <p>country name: ${country}</p>
         <p>phone: ${phone}</p>
         <p>number of people hiring: ${np}</p>
         <p>number of employee hiring: ${ne}</p>
    
      </div>
    
    </body>
    </html>`

    try {
        await sendEmail("<EMAIL>", subject, html)
        res.json({ message: "mail has been successfully sent" })

    } catch (err) {
        res.status(500).json({ error: "Error sending mail", details: err });

    }
};



module.exports = {
    sendMailRequest,
};