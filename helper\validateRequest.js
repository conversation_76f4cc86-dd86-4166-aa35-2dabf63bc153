const { validationResult } = require("express-validator");

// Import the ProjectError class from the error.js file
const ProjectError = require("./error");

// Define a middleware function to validate the request body
const validateRequest = (req, res, next) => {
  // Validate the request body
  try {
    const validationError = validationResult(req);
    if (!validationError.isEmpty()) {
      // If there is a validation error, create a new ProjectError instance with the error message
      const err = new ProjectError("Validation failed!");
      err.statusCode = 422;
      err.data = validationError.array();
      throw err; // throw the error to the next error handling middleware
    }
    // If validation passes, move to the next middleware
    next();
  } catch (error) {
    // If there is an error, pass it to the next error handling middleware
    next(error);
  }
};

// Export the validateRequest middleware function
module.exports = { validateRequest };
