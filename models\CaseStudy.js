const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const caseStudySchema = new Schema({
    image: {
        type: String,
    },

    title: {
        type: String,
    },
    text: {
        type: String,
    },
    description: {
        type: String,
    },
    subtopics: {
        type: [String],
    },
    date: {
        type: Date,
        default: Date.now(),
    },
});
const CaseStudy = mongoose.model("CaseStudy", caseStudySchema);
module.exports = CaseStudy;
