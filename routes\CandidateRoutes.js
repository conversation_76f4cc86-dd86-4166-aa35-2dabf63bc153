const express = require("express");
const router = express.Router();
const CandidateController = require("../controllers/CandidateController");
const { check } = require("express-validator");
const authentication = require("../middleware/authentication");
const upload = require("../middleware/imageUpload");

router.post(
  "/post",
  upload.single("Avatar"),
  [
    // check("First_name", "Field name is required").not().isEmpty(),
    // check("Email", "Field email is required").isEmail(),
    // check("password", "Field password is required").isLength({ min: 6 }),
  ],

  CandidateController.createCandidate
);
router.get("/candidateInfo", CandidateController.getCandidateInfo);
router.put("/updateCandidateStatus", CandidateController.updateCandidateStatus);
router.post("/getCandidateStatus", CandidateController.getCandidateStatus);

router.get("/talentData/:token", CandidateController.talentData);

router.post(
  "/switchStatus/:candidateId",
  authentication,
  CandidateController.switchStatus
);

router.post("/get-avatar", CandidateController.getAvatar);
router.get("/get-candidate/:id", CandidateController.getCandidate);

router.get("/all-candidates", CandidateController.getCandidates);
router.get("/get-email", CandidateController.getEmail);

router.delete("/:id", CandidateController.deleteCandidate);

router.put("/exit-tab/:id", CandidateController.exitTab);


router.get(
  "/candidateReport",
  // authentication,
  CandidateController.candidateReport
);

router.get(
  "/:candidateId",
  authentication,
  CandidateController.getCandidateWithCompany
);

router.get("/search", authentication, CandidateController.searchCandidates);

module.exports = router;
