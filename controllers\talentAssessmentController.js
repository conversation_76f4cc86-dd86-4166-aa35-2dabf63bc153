const allAssessments = require("../models/allAssessments");
const { verifyToken } = require("../helper/verifyToken.js");
const jwt = require("jsonwebtoken");
const Candidate = require("../models/Candidate");
const TalentScore = require("../models/talentScore");

const getAssessments = async (req, res) => {
  const token = req.cookies.candidate;
  const decoded = await verifyToken(token);
  console.log({ decoded, token });
  if (decoded === "Not Authorized") {
    res.status(401).json({ message: "Not authorized" });
  }

  try {
    const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
    const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page if not provided
    const category = req.query.category;
    const skip = (page - 1) * limit;
    const search = req.query.search;

    let assessments = await allAssessments
      .aggregate([
        {
          $match: {
            // Category filter
            category: !category
              ? { $in: ["Hard Skills", "Psychometrics", "Soft Skills"] }
              : { $eq: category },

            // Search filter (matches any text fields you specify)
            ...(search && {
              $or: [
                { name: { $regex: search, $options: "i" } }, // Case-insensitive match for "name" field
                { description: { $regex: search, $options: "i" } }, // Case-insensitive match for "description" field
              ],
            }),
          },
        },
      ])
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);
    let talent = await Candidate.findOne({ Email: decoded.email });
    await Promise.all(
      assessments.map(async (assessment) => {
        let talentScore = await TalentScore.findOne({
          talentId: talent._id,
          assessmentId: assessment._id,
        });
        assessment.score = talentScore ? talentScore.score : null;
        assessment.completed = talentScore ? talentScore.createdAt : null;
        console.log({ assessment });
      })
    );

    let totalAssessments = await allAssessments.aggregate([
      {
        $match: {
          category: !category
            ? { $in: ["Hard Skills", "Psychometrics", "Soft Skills"] }
            : { $eq: category },
          ...(search && {
            $or: [
              { name: { $regex: search, $options: "i" } }, // Case-insensitive match for "name" field
              { description: { $regex: search, $options: "i" } }, // Case-insensitive match for "description" field
            ],
          }),
        },
      },
      {
        $count: "totalDocuments",
      },
    ]);

    let totalPages = Math.ceil(totalAssessments[0].totalDocuments / limit);
    res.status(200).json({ skills: assessments, totalPages });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "Server Error" });
  }
};

const passAssessments = async (req, res) => {
  let token = req.cookies.candidate;
  let assessmentId = req.params.assessmentId;
  const decoded = await verifyToken(token);
  console.log({ decoded, assessmentId });
  if (decoded === "Not Authorized") {
    return res.status(401).json({ message: "Not authorized" });
  }
  const expirationTime = Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60; // 3 days

  const newToken = jwt.sign(
    {
      email: decoded.email,
      assessmentId: assessmentId,
      talent: true,
      exp: expirationTime,
    },
    process.env.SPECIAL_LINK_KEY
  );
  const fiveDaysInMilliseconds = 5 * 24 * 60 * 60 * 1000;
  let options = {};
  res.cookie("talent", {});
  if (process.env.NODE_ENV === "production") {
    options = {
      domain: "server.go-platform.com",
      sameSite: "none",
      secure: true,
      path: "/",
      maxAge: fiveDaysInMilliseconds, // Set an appropriate max age in milliseconds
      httpOnly: true, // Makes the cookie accessible only via HTTP (not JavaScript)
    };
    res.cookie("candidatecookie", newToken, options);
    res.redirect(`https://assessment.go-platform.com`);
  }
  if (process.env.NODE_ENV === "dev") {
    options = {
      maxAge: fiveDaysInMilliseconds, // Set an appropriate max age in milliseconds
    };
    res.cookie("candidatecookie", newToken, options);
    res.redirect(`http://localhost:8081`);
  }
};

const getAssessmentsForTalent = async (decoded) => {
  const assessment = await allAssessments.find({ _id: decoded.assessmentId });
  console.log({ assessment });
  if (
    assessment[0] &&
    assessment[0].questions_list &&
    assessment[0].questions_list.length > 25
  ) {
    // Shuffle the questions using the Fisher-Yates algorithm
    for (let i = assessment[0].questions_list.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [assessment[0].questions_list[i], assessment[0].questions_list[j]] = [
        assessment[0].questions_list[j],
        assessment[0].questions_list[i],
      ];
    }

    // Select the first 20 questions
    assessment[0].questions_list = assessment[0].questions_list.slice(0, 20);
  }

  return assessment;
};

const evaluateTalent = async (answers) => {
  let results = [];

  try {
    for (const assessmentName in answers.answers) {
      // answers.map(async (answer) => {
      const assessment = await allAssessments.findOne({
        name: assessmentName,
      });

      let totalPoints = 0;
      let rangesPoint = 0;
      let quesionsNbr = 0;
      let personalityResults = {};
      let customResults = [];

      if (assessment.category === "Personality") {
        const answersList = answers.answers[assessmentName];
        const traits = answers.traits[assessmentName];
        const traitScores = {};
        const categories = {
          "Very Low": 21,
          Low: 40,
          Medium: 60,
          High: 80,
        };
        // Calculate sum of scores for each trait
        for (const questionNumber in traits) {
          const trait = traits[questionNumber];
          const score = parseInt(answersList[questionNumber]);
          if (!traitScores[trait]) {
            traitScores[trait] = score;
          } else {
            traitScores[trait] += score;
          }
        }

        // Calculate total number of questions for each trait
        const traitQuestionCounts = {};
        for (const questionNumber in traits) {
          const trait = traits[questionNumber];
          if (!traitQuestionCounts[trait]) {
            traitQuestionCounts[trait] = 1;
          } else {
            traitQuestionCounts[trait]++;
          }
        }

        const finalScores = {};
        for (const trait in traitScores) {
          const totalQuestions = traitQuestionCounts[trait];
          const percentage = (traitScores[trait] / (totalQuestions * 5)) * 100; // Assuming each question has a maximum score of 5

          // Assign category based on percentage
          let category;
          for (const cat in categories) {
            if (percentage < categories[cat]) {
              category = cat;
              break;
            }
          }

          finalScores[trait] = category || "Very High"; // If percentage exceeds 80%
        }
        personalityResults = finalScores;
      }

      if (assessment.ranges) {
        for (const questionNumber in answers.answers[assessmentName]) {
          rangesPoint += parseInt(
            answers.answers[assessmentName][questionNumber]
          );
        }
      }
      if (!assessment.ranges && assessment.answers) {
        for (const questionNumber in answers.answers[assessmentName]) {
          if (
            parseInt(answers.answers[assessmentName][questionNumber]) ===
            assessment.answers[questionNumber]
          ) {
            totalPoints++;
          }
        }
      }
      if (assessment.questions_list.length > 25) {
        quesionsNbr = 20;
      } else {
        quesionsNbr = assessment.questions_list.length;
      }
      if (assessment.ranges) {
        results.push({
          assessmentName: assessmentName,
          rangesPoint,
          quesionsNbr,
        });
      } else if (assessment.category === "Personality") {
        results.push({
          assessmentName: assessmentName,
          personalityResults,
          quesionsNbr,
        });
      } else {
        results.push({
          assessmentName: assessmentName,
          totalPoints,
          quesionsNbr,
        });
      }
    }
    let score = 0;
    results.map((result) => {
      score += (result.totalPoints * 100) / result.quesionsNbr;
    });

    let talent = await Candidate.findOne({ Email: answers.candidate });
    let finalResults = {
      talentId: talent._id,
      assessmentId: answers.assessmentId,
      score: parseInt(score / results.length),
      // answers: answers.answers,
    };
    let resultsModel = new TalentScore(finalResults);
    await resultsModel.save();
    return true;
  } catch (error) {
    console.log(error);
    return false;
  }
};

module.exports = {
  getAssessments,
  passAssessments,
  getAssessmentsForTalent,
  evaluateTalent,
};
