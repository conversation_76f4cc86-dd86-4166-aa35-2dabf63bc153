const mongoose = require("mongoose");

const educationSchema = mongoose.Schema({
  candidate: {
    type: mongoose.Schema.Types.ObjectId,
    required: false,
    ref: "Candidate",
  },
  degree: {
    type: String,
    required: true
  },
  fieldOfStudy: {
    type: String,
    required: true
  },
  school: {
    type: String,
    required: true
  },
  degreeIssuanceCountry: {
    type: String,
    required: true
  },
  startingDate: {
    type: Date,
    required: true
  },
  endingDate: {
    type: Date,
    required: true
  },
  currentlyStudyHere: {
    type: String,
    required: true
  },
  fullDetails: {
    type: String
  },
});

module.exports = mongoose.model("Education", educationSchema);
