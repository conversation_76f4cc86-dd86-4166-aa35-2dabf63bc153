const jwt = require("jsonwebtoken");

module.exports = async (req, res, next) => {
    // const headerToken = req.header("token");
    let token = req.cookies.candidatecookie;

    jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
        if (err) {
            console.log({ err });
            return res.status(401).json({
                title: "unauthorized",
                error: err,
            });
        }
        //token is valid
        console.log(decoded);
        next();
    });
}