const mongoose = require("mongoose");

const certificationSchema = mongoose.Schema({
  candidate: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Candidate",
    required: true
  },
  title: {
    type: String,
    required: true
  },
  issuer: {
    type: String,
    required: true
  },
  credentials: {
    type: String
  },
  issuerCountry: {
    type: String,
    required: true
  },
  expire: {
    type: String,
    required: true
  },
  startingDate: {
    type: Date,
    required: true
  },
  endingDate: {
    type: Date
  },
  skillsAcquired: {
    type: [String]
  },
});

module.exports = mongoose.model("Certification", certificationSchema);
