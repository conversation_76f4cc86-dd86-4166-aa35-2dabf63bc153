[{"weights": [{"name": "dense0/conv0/filters", "shape": [3, 3, 3, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0057930146946626555, "min": -0.7125408074435067}}, {"name": "dense0/conv0/bias", "shape": [32], "dtype": "float32"}, {"name": "dense0/conv1/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006473719839956246, "min": -0.6408982641556684}}, {"name": "dense0/conv1/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010509579321917366, "min": -1.408283629136927}}, {"name": "dense0/conv1/bias", "shape": [32], "dtype": "float32"}, {"name": "dense0/conv2/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005666389652326995, "min": -0.7252978754978554}}, {"name": "dense0/conv2/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010316079270605948, "min": -1.1760330368490781}}, {"name": "dense0/conv2/bias", "shape": [32], "dtype": "float32"}, {"name": "dense0/conv3/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0063220320963392074, "min": -0.853474333005793}}, {"name": "dense0/conv3/pointwise_filter", "shape": [1, 1, 32, 32], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010322785377502442, "min": -1.4658355236053466}}, {"name": "dense0/conv3/bias", "shape": [32], "dtype": "float32"}, {"name": "dense1/conv0/depthwise_filter", "shape": [3, 3, 32, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0042531527724920535, "min": -0.5741756242864272}}, {"name": "dense1/conv0/pointwise_filter", "shape": [1, 1, 32, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010653339647779278, "min": -1.1825207009035}}, {"name": "dense1/conv0/bias", "shape": [64], "dtype": "float32"}, {"name": "dense1/conv1/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005166931012097527, "min": -0.6355325144879957}}, {"name": "dense1/conv1/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011478300188101974, "min": -1.3888743227603388}}, {"name": "dense1/conv1/bias", "shape": [64], "dtype": "float32"}, {"name": "dense1/conv2/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006144821410085641, "min": -0.8479853545918185}}, {"name": "dense1/conv2/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010541967317169788, "min": -1.3809977185492421}}, {"name": "dense1/conv2/bias", "shape": [64], "dtype": "float32"}, {"name": "dense1/conv3/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005769844849904378, "min": -0.686611537138621}}, {"name": "dense1/conv3/pointwise_filter", "shape": [1, 1, 64, 64], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010939095534530341, "min": -1.2689350820055196}}, {"name": "dense1/conv3/bias", "shape": [64], "dtype": "float32"}, {"name": "dense2/conv0/depthwise_filter", "shape": [3, 3, 64, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.0037769308277204924, "min": -0.40790852939381317}}, {"name": "dense2/conv0/pointwise_filter", "shape": [1, 1, 64, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01188667194516051, "min": -1.4382873053644218}}, {"name": "dense2/conv0/bias", "shape": [128], "dtype": "float32"}, {"name": "dense2/conv1/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006497045825509464, "min": -0.8381189114907208}}, {"name": "dense2/conv1/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011632198913424622, "min": -1.3377028750438316}}, {"name": "dense2/conv1/bias", "shape": [128], "dtype": "float32"}, {"name": "dense2/conv2/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.005947182225246056, "min": -0.7969224181829715}}, {"name": "dense2/conv2/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.011436844339557722, "min": -1.4524792311238306}}, {"name": "dense2/conv2/bias", "shape": [128], "dtype": "float32"}, {"name": "dense2/conv3/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006665432686899222, "min": -0.8998334127313949}}, {"name": "dense2/conv3/pointwise_filter", "shape": [1, 1, 128, 128], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01283421422920975, "min": -1.642779421338848}}, {"name": "dense2/conv3/bias", "shape": [128], "dtype": "float32"}, {"name": "dense3/conv0/depthwise_filter", "shape": [3, 3, 128, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004711699953266218, "min": -0.6737730933170692}}, {"name": "dense3/conv0/pointwise_filter", "shape": [1, 1, 128, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.010955964817720302, "min": -1.3914075318504784}}, {"name": "dense3/conv0/bias", "shape": [256], "dtype": "float32"}, {"name": "dense3/conv1/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.00554193468654857, "min": -0.7149095745647656}}, {"name": "dense3/conv1/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.016790372250126858, "min": -2.484975093018775}}, {"name": "dense3/conv1/bias", "shape": [256], "dtype": "float32"}, {"name": "dense3/conv2/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006361540626077091, "min": -0.8142772001378676}}, {"name": "dense3/conv2/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.01777329678628959, "min": -1.7062364914838006}}, {"name": "dense3/conv2/bias", "shape": [256], "dtype": "float32"}, {"name": "dense3/conv3/depthwise_filter", "shape": [3, 3, 256, 1], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.006900275922289082, "min": -0.8625344902861353}}, {"name": "dense3/conv3/pointwise_filter", "shape": [1, 1, 256, 256], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.015449936717164282, "min": -1.9003422162112067}}, {"name": "dense3/conv3/bias", "shape": [256], "dtype": "float32"}, {"name": "fc/weights", "shape": [256, 7], "dtype": "float32", "quantization": {"dtype": "uint8", "scale": 0.004834276554631252, "min": -0.7203072066400565}}, {"name": "fc/bias", "shape": [7], "dtype": "float32"}], "paths": ["face_expression_model-shard1"]}]