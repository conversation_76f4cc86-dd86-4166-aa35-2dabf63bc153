const PaymentCard = require("../models/PaymentCard.js");
const jwt = require("jsonwebtoken");


const updatePaymentCard = async (req, res) => {
    const { cardNumber } = req.params; // Assuming you're passing cardNumber in the URL params
    const updates = req.body;
  
    try {
      const updatedCard = await PaymentCard.findOneAndUpdate(
        { _id: updates.id },
        {cvv:updates.cvv,
          expirationDate:new Date(updates.date),
          cardNumber:updates.cardNumber,
          cardHolderName:updates.cardHolderName
        },
        { new: true, runValidators: true }
      );
  
      if (!updatedCard) {
        return res.status(404).json({ error: 'Payment card not found' });
      }
  
      return res.status(200).json(updatedCard);
    } catch (error) {
      console.error('Error updating payment card:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };

  const deletePaymentCard = async (req, res) => {
    const data  = req.body; // Assuming you're passing cardNumber in the URL params
  
    try {
      const deletedCard = await PaymentCard.findOneAndDelete({ _id: data.id });
  
      if (!deletedCard) {
        return res.status(404).json({ error: 'Payment card not found' });
      }
  
      return res.status(200).json({ message: 'Payment card deleted successfully' });
    } catch (error) {
      console.error('Error deleting payment card:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  };

  const addPaymentCard = async (req, res) => {
    const cardData = req.body; // Assuming you're passing card data in the request body
    let token = req.cookies.user;

    console.log({ token });
    jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
        if (err) {
            console.log({ err });
            return res.status(401).json({
                title: "unauthorized",
            });
        }

        try {

          const cardFound = await PaymentCard.findOne({ cardNumber: cardData.cardNumber});
          if(!cardFound){
            //const newCard = await PaymentCard.create({userId:decoded.id},cardData);
            const newCard=new PaymentCard({
              userId:decoded.id,
              cardNumber:cardData.cardNumber,
              cardHolderName:cardData.cardHolderName,
              expirationDate:new Date(cardData.date),
              cvv:cardData.cvv
            });

            const saveCard=await newCard.save();
      
          return res.status(201).json({message:'card created succesfuly', newCard:saveCard});
          }
      
          return res.status(201).json(cardFound);
        } catch (error) {
          console.error('Error adding payment card:', error);
          return res.status(500).json(error);
        }
       })

 
  };

  /*const getPaymentCard = async (req, res) => {
    try {
      const AllPaymentCard = await PaymentCard.find();
  
      res.status(200).json(AllPaymentCard);
    } catch (error) {
      res.status(404).json({ message: error.message });
    }
  };*/


  module.exports = {
    updatePaymentCard,
    deletePaymentCard,
    addPaymentCard,
   // getPaymentCard,
    
  }