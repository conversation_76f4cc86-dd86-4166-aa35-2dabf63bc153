// routes/certificationRoutes.js
const express = require("express");
const router = express.Router();
const {
    addCertification,
    getCertificationsByCandidate,
    getCertificationById,
    updateCertificationById,
    deleteCertificationById,
} = require("../controllers/CertificationController");


// Create a new certification
router.post("/", addCertification);

// Get all certifications for a user
router.get("/", getCertificationsByCandidate);

// Get certification by ID
router.get("/:id", getCertificationById);

// Update a certification by ID
router.put("/:id", updateCertificationById);

// Delete a certification by ID
router.delete("/:id", deleteCertificationById);

module.exports = router;
