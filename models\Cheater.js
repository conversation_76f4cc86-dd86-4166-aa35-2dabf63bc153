const mongoose = require("mongoose");
const CheaterSchema = mongoose.Schema({
    candidate_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Candidate',
        required: true
    },
    project_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'companyProjects',
        required: true
    },
    status: {
        type: String,
        default: "potential-cheater",
        enum: ['cheater', 'potential-cheater'],
    }
});
const Cheater = mongoose.model('Cheater', CheaterSchema);
module.exports = Cheater