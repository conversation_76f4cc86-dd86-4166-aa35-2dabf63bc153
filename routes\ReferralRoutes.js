const express = require('express');
const router = express.Router();
const referralController = require('../controllers/ReferralController');

// Create a new referral
router.post('/', referralController.createReferral);

// Get all referrals
router.get('/', referralController.getReferrals);

// Get a single referral by ID
router.get('/:id', referralController.getReferralById);

// Update a referral by ID
router.put('/:id', referralController.updateReferral);

// Delete a referral by ID
router.delete('/:id', referralController.deleteReferral);

module.exports = router;
