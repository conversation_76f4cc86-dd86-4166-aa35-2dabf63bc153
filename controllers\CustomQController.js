const CustomQ = require("../models/CustomQ.js");
const jwt = require("jsonwebtoken");
const mongoose = require('mongoose');




const addCustomQ = async (req, res) => {
  const data = req.body; // Assuming you're passing card data in the request body
  let user_token = req.cookies.user;
  //let admin_token = req.cookies.admin;

  if (user_token) {
    jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {


        //const newCard = await PaymentCard.create({userId:decoded.id},cardData);
        const newQ = new CustomQ({
          company: decoded.company_name,
          name: data.name,
          category: data.category,
          description: data.description,
          question: data.question,
          time: data.time,
          options: data.options
        });

        const saveQ = await newQ.save();

        return res.status(201).json({ message: 'Custom Question created succesfuly', newQ: saveQ });

      } catch (error) {
        console.error('Error adding Custom Question created:', error);
        return res.status(500).json(error);
      }
    })
  } else {


    try {


      //const newCard = await PaymentCard.create({userId:decoded.id},cardData);
      const newQ = new CustomQ({
        company: null,
        name: data.name,
        category: data.category,
        description: data.description,
        question: data.question,
        time: data.time,
        options: data.options
      });

      const saveQ = await newQ.save();

      return res.status(201).json({ message: 'Custom Question created succesfuly', newQ: saveQ });

    } catch (error) {
      console.error('Error adding Custom Question created:', error);
      return res.status(500).json(error);
    }

  }


};


const getCustomQ = async (req, res) => {
  try {
    const AllCustomQ = await CustomQ.find({ company: undefined });

    res.status(200).json(AllCustomQ);
  } catch (error) {
    res.status(404).json({ message: error.message });
  }
};

const getCustomQByUserId = async (req, res) => {
  try {
    let token = req.cookies.user;

    jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      const Custom = await CustomQ.find({ company: decoded.company_name });

      if (!Custom || Custom.length === 0) {
        return res.status(204).json({ message: "No Custom Question found." });
      }

      res.json(Custom);
    })
  } catch (error) {
    res.status(500).json({ error: error });
  }
};

const getCustomQById = async (req, res) => {
  try {
    const CustomQId = req.params.id;

    const Custom = await CustomQ.findOne({ _id: CustomQId });

    if (!Custom) return res.status(204).json({ message: "No Custom Question found." });
    console.log(Custom);
    res.json(Custom);
  } catch (error) {
    res.status(500).json({ error: error });
  }


};

const getCustomQByCategory = async (req, res) => {
  try {
    const category = req.params.cat;

    const Custom = await CustomQ.find({ category: category });

    if (!Custom) return res.status(204).json({ message: "No Custom Question found." });
    res.json(Custom);
  } catch (error) {
    res.status(500).json({ error: error });
  }


};


const deleteCustomQById = async (req, res) => {
  try {
    const Custom = await CustomQ.findByIdAndDelete(req.params.id);
    if (!Custom) return res.status(204).json({ message: "No Custom Question found." });
    res.json(Custom);
  } catch (error) {
    res.status(500).json({ error: error });
  }
};


const updateCustomQById = async (req, res) => {
  const questionId = req.params.id; // Assuming you are passing the question ID in the request parameters

  try {
    if (req.body.category == 'Multiple-choice') {
      const { name, question, category, time, options } = req.body;

      // Find the question by ID
      const customQuestion = await CustomQ.findById(questionId);

      if (!customQuestion) {
        return res.status(404).json({ message: "Question not found" });
      }

      // Update the question fields
      if (name) customQuestion.name = name;
      if (question) customQuestion.question = question;
      if (category) customQuestion.category = category;
      if (time) customQuestion.time = time;
      if (options) customQuestion.options = options;
      const updatedQuestion = await customQuestion.save();
      res.send({ message: "Question updated successfully", updatedQuestion });
    } else {
      const { name, question, category, time } = req.body;

      // Find the question by ID
      const customQuestion = await CustomQ.findById(questionId);

      if (!customQuestion) {
        return res.status(404).json({ message: "Question not found" });
      }

      // Update the question fields
      if (name) customQuestion.name = name;
      if (question) customQuestion.question = question;
      if (category) customQuestion.category = category;
      if (time) customQuestion.time = time;
      const updatedQuestion = await customQuestion.save();
      res.send({ message: "Question updated successfully", updatedQuestion });
    }


    // Save the updated question

  } catch (error) {
    console.error(error);
    res.status(500).json({ message: error });
  }
};



module.exports = {
  addCustomQ,
  getCustomQ,
  getCustomQById,
  getCustomQByCategory,
  deleteCustomQById,
  updateCustomQById,
  getCustomQByUserId
}