#!/usr/bin/env node
const fs = require("fs");
const glob = require("glob");
const path = require("path");

/**
 * @typedef {Object} QuizQuestion
 * @property {string} text the body of the question
 * @property {number} options Total number of possible answers / options
 * @property {number} answer Index of the CORRECT answer
 */

/**
 * @param {string} quiz_path path to the markdown file
 * @returns {{ title:string, questions:QuizQuestion[] }}
 */
function parseQuiz(quiz_path) {
  //
  // extract the text
  //
  let markdown = fs.readFileSync(path.join(process.cwd(), quiz_path), {
    encoding: "utf-8",
  });

  let title = "Undefined...";

  //
  // parse...
  //
  markdown = markdown

    //
    // for each line...
    //
    .split(/\n/)

    .reduce((questions, line) => {
      //
      // title...
      //
      if (line.match(/^\s*##\s/)) {
        title = line;
      }

      //
      // Question starts...
      //
      else if (line.match(/^\s*####\s/)) {
        questions.push({
          text: line + "\n",
          options: -1,
          answer: -1,
        });
      }

      //
      // ... asume body of a question.
      //
      else {
        if (questions.length) {
          let question = questions[questions.length - 1];
          let m;

          //
          // ANSWER OPTION!
          //
          if ((m = line.match(/^\s*\-\s*\\?\[\s*(x)?\s*\]/))) {
            question.options++;

            //
            // it is the correct one?
            //
            if (m[1]) {
              question.answer = question.options;
            }

            //
            // adding a flag so we can recognize the start of a quiz option.
            //
            line = line.replace(m[0], "- %OPTION%");
          }

          question.text += line + "\n";
        }
      }

      return questions;
    }, []);

  return {
    questions: markdown,
    title,
  };
}

/**
 * @typedef {Object} Quiz
 * @property {string} folder the folder inside data of this quiz
 * @property {Object} pathByLang Language id. E.g: en, es, it
 * @property {string} path the path to the `.md` file
 * @property {string} title title of the quiz
 * @property {QuizQuestion[]} questions questions array
 */

/**
 * Scan the `data` folder searching for all the `.md` quiz files.
 *
 * @param {string} justThisOne
 * @returns { Quiz[] }
 */
function getAllQuizzes(justThisOne) {
  return glob
    .sync("data/**/*quiz*.md")

    .reduce((out, filePath) => {
      const m = filePath.match(
        /data\/(?<folder>[^/]+)\/.*quiz(?:-(?<lang>\w{2}))?\.md/
      );

      if (m) {
        let current = out.length ? out[out.length - 1] : null;
        const folder = m.groups.folder;

        const lang = m.groups.lang || "en";

        if (justThisOne && justThisOne !== folder) {
          return out;
        }

        if ((!current || current.folder !== folder) && lang === "en") {
          current = {
            name: folder,
            pathByLang: {},
          };

          out.push(current);
        }

        current.pathByLang[lang] = filePath;
      }

      return out;
    }, []);
}

// Function to parse command-line arguments
// function parseCommandLineArgs() {
//   const args = process.argv.slice(2);

//   if (args.length === 0) {
//     console.error("Usage: quizParser.js <command> [options]");
//     process.exit(1);
//   }

//   const command = args[0];
//   const options = args.slice(1);

//   return { command, options };
// }

// Main function
// function main() {
//   const { command, options } = parseCommandLineArgs();

//   if (command === "parse") {
//     // Implement code to parse quizzes here
//     const quizPath = options[0]; // Path to the quiz markdown file
//     const quizData = parseQuiz(quizPath);
//     console.log(quizData);
//   } else if (command === "list") {
//     // Implement code to list quizzes here
//     const justThisOne = options[0]; // Optionally, specify a specific quiz folder
//     const quizzes = getAllQuizzes(justThisOne);
//     console.log({ quizzes: quizzes[0].pathByLang });
//   } else {
//     console.error("Invalid command:", command);
//     process.exit(1);
//   }
// }

// main();

module.exports = {
  getAllQuizzes,
  parseQuiz,
};
