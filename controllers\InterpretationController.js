const Interpretation = require('../models/InterpretationPersonality');
const allAssessments = require("../models/allAssessments");


// Controller to create a new interpretation
const createInterpretation = async (req, res) => {
    try {
        const interpretation = new Interpretation(req.body);
        await interpretation.save();
        res.status(201).json({ message: 'Interpretation created successfully!' });
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

// Controller to get all interpretations
const getAllInterpretations = async (req, res) => {
    try {
        const interpretations = await Interpretation.find();
        res.json(interpretations);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Controller to get a single interpretation by ID
const getInterpretationById = async (req, res) => {
    try {
        const interpretation = await Interpretation.findById(req.params.id);
        if (!interpretation) {
            return res.status(404).json({ message: 'Interpretation not found' });
        }
        res.json(interpretation);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

// Controller to update an interpretation by ID
const updateInterpretation = async (req, res) => {
    try {
        const interpretation = await Interpretation.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!interpretation) {
            return res.status(404).json({ message: 'Interpretation not found' });
        }
        res.json({ message: 'Interpretation updated successfully', interpretation });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
// Controller to retrieve scores for each trait based on the provided trait names and scores
const getScoresForTraits = async (req, res) => {
    const { traitScores } = req.body;

    try {
        const interpretationName = await allAssessments.findOne({ name: req.params.assessmentName });
        // Fetch interpretation object based on assessment_id
        const interpretation = await Interpretation.findOne({ assessment_id: interpretationName._id });

        if (!interpretation) {
            return res.status(404).json({ message: 'Interpretation not found' });
        }

        // Initialize an object to store scores for each trait
        const traitScoresData = {};

        // Iterate over each trait score in the provided object
        for (const traitName in traitScores) {
            const score = traitScores[traitName];

            // Find the trait in the interpretation object
            const trait = interpretation.traits.find(t => t.trait_name === traitName);

            if (trait) {
                // Find the score data for the specified score
                const scoreData = trait.scores.find(s => s.status.toLowerCase() === score.toLowerCase());

                if (scoreData) {
                    // Add score data to the result object
                    traitScoresData[traitName] = {
                        definition: trait.definition,
                        scoreData: scoreData
                    };
                } else {
                    traitScoresData[traitName] = { message: 'Score not found for the specified trait' };
                }
            } else {
                traitScoresData[traitName] = { message: 'Trait not found in the interpretation object' };
            }

        }

        res.json(traitScoresData);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};



// Controller to delete an interpretation by ID
const deleteInterpretation = async (req, res) => {
    try {
        const interpretation = await Interpretation.findByIdAndDelete(req.params.id);
        if (!interpretation) {
            return res.status(404).json({ message: 'Interpretation not found' });
        }
        res.json({ message: 'Interpretation deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

module.exports = {
    createInterpretation,
    getAllInterpretations,
    getInterpretationById,
    updateInterpretation,
    deleteInterpretation,
    getScoresForTraits

};
