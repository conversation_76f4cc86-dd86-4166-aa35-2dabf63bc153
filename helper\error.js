// This is a custom error class that extends the built-in Error class
// It allows us to create custom errors with additional properties, such as a status code and error data
class ProjectError extends Error {
  // Private properties that can only be accessed within this class
  // _status holds the status code of the error (defaults to 0)
  // _data holds any additional error data (defaults to an empty object)
  constructor(message) {
    super(message);
    this._status = 0;
    this._data = {};
  }

  // Getter for the _status property, which returns the current status code
  get statusCode() {
    return this._status;
  }

  // Setter for the _status property, which allows us to set a new status code
  set statusCode(code) {
    this._status = code;
  }

  // Getter for the _data property, which returns the current error data
  get data() {
    return this._data;
  }

  // Setter for the _data property, which allows us to set new error data
  set data(errorData) {
    this._data = errorData;
  }
}

// Export the ProjectError class as the default export of this module
module.exports = ProjectError;
