const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const InvitedCandidateSchema = new Schema({
    UserId: {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
        ref: "User",
    },
    first_name: { type: String },
    last_name: { type: String },
    email: { type: String },
    projectId: {
        type: String,
        required: true
    },
    invitedCounter: {
        type: Number,
        default: 0
    }
});
const InvitedCandidate = mongoose.model("InvitedCandidate", InvitedCandidateSchema);
module.exports = InvitedCandidate;
