const mongoose = require("mongoose");

//schema
const CompanySchema = mongoose.Schema(
  {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },

    isInfoComplete: {
      type: <PERSON><PERSON>an,
      default: false,
    },

    email: {
      type: String,
      required: true, // to store the email used at creation
    },
    
    name: {
      type: String,
      required: false,
    },
    industry: {
      type: String,
      //required: true,
      // unique: true,
      index: true,
    },
    description: {
      type: String,
    },

    logo: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    cover: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    company_photos: {
      type: [String],
      default: "",
      trim: true,
      // required: true,
    },
    overview: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    tagline: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    location: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    employees_count: {
      type: String,
      default: "",
      trim: true,
    },
    // work_Type: {
    //   type: String,
    //   default: "",
    //   trim: true,
    //   required: true,
    // },
    // category: {
    //   type: String,
    //   default: "",
    //   trim: true,
    //   required: true,
    // },
    website: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    facebook: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    linkedIn: {
      type: String,
      default: "",
      trim: true,
      // required: true,
    },
    twitter: {
      type: String,
      default: "",
      trim: true,
    },
    instagram: {
      type: String,
      default: "",
      trim: true,
    },
    company_Vision: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CompanyVision ",
    },
    company_Benefits: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CompanyBenefits",
    },
    isDeactivated: {
      type: Boolean,
      default: false,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    plan: {
      type: String,
      default: "free",
    },
    credit: {
      type: Number,
      default: 2,
    },
    freeCredit: {
      type: Number,
      default: 0,
    },
    discounts: {
      type: Array,
    },
    recruiters_count: {
      type: Number,
      default: 1,
    },
    upgradeDate: {
      type: Date,
    },
    endOfSubscription: {
      type: Date,
    },
    apiKey: {
      type: String,
      default: "",
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Company", CompanySchema);
