const qs = require("querystring");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const { verifyToken } = require("../../helper/verifyToken");
const companyProject = require("../../models/companyProject");

const authorization = (req, res) => {
  return encodeURI(
    `https://www.linkedin.com/oauth/v2/authorization?client_id=${process.env.CLIENT_ID}&response_type=code&scope=${process.env.SCOPE}&redirect_uri=${process.env.JOB_SHARE_REDIRECT_URI}`
  );
};

const redirect = async (req, res) => {
  try {
    const code = req.query.code;
    let token = req.cookies.user;

    const decoded = await verifyToken(token);
    if (decoded === "Not Authorized") {
      return { success: false, message: "Unauthorized" };
    }

    const rawInvitationLink = req.cookies.invitationLink;
    const urlObj = new URL(rawInvitationLink); // Create a URL object

    // Encode only the query parameters
    urlObj.search = new URLSearchParams(urlObj.search).toString();

    const invitationLink = urlObj.toString();

    const urlParams = new URLSearchParams(new URL(rawInvitationLink).search);
    const projectId = urlParams.get("project");
    const project = await companyProject.findById(projectId);

    const payload = qs.stringify({
      client_id: process.env.CLIENT_ID,
      client_secret: process.env.CLIENT_SECRET,
      redirect_uri: process.env.JOB_SHARE_REDIRECT_URI,
      grant_type: "authorization_code",
      code: code,
    });

    const tokenResponse = await axios.post(
      "https://www.linkedin.com/oauth/v2/accessToken",
      payload,
      { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
    );

    const accessToken = tokenResponse.data.access_token;

    const userInfo = await axios.get("https://api.linkedin.com/v2/userinfo", {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    let text = `🚀 **Exciting Opportunity at ${decoded.company_name}!** 🚀

      Are you a ** ${project.jobTitle} with ${project.seniority} level of seniority** looking for your next challenge? 
      
      Click the link below to apply now! ⬇️  
  
      🔗 ${invitationLink}`;

    const postData = {
      author: `urn:li:person:${userInfo.data.sub}`,
      lifecycleState: "PUBLISHED",
      specificContent: {
        "com.linkedin.ugc.ShareContent": {
          shareCommentary: { text: text },
          shareMediaCategory: "NONE",
        },
      },
      visibility: { "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC" },
    };

    const config = {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    };

    await axios.post("https://api.linkedin.com/v2/ugcPosts", postData, config);

    res.setHeader("Content-Type", "text/html");
    res.send(`<h2>✅ Post shared successfully! ✅</h2>`);
  } catch (error) {
    console.log({ error });
    res.setHeader("Content-Type", "text/html");

    res.send(
      `<h2>❌ You've already shared this job position on your linkedin!!</h2>`
    );
  }
};

module.exports = {
  authorization,
  redirect,
};
