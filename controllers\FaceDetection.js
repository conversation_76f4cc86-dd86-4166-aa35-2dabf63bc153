const path = require('path');
const faceapi = require('face-api.js');
const canvas = require('canvas');
const multer = require('multer');

// Setup for face-api.js to work with Node.js and canvas
const { Canvas, Image, ImageData } = canvas;
faceapi.env.monkeyPatch({ Canvas, Image, ImageData });

// Load face-api.js models
const loadModels = async () => {
    const modelPath = path.join(__dirname, '../ai-models');
    await faceapi.nets.ssdMobilenetv1.loadFromDisk(modelPath);
    await faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath);
    await faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath);
};

loadModels();

// Multer setup to handle file uploads in memory
const storage = multer.memoryStorage();
const upload = multer({ storage });

exports.uploadAvatar = upload.single('avatar');

exports.checkAvatar = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const mimeType = req.file.mimetype;
        if (mimeType !== 'image/png' && mimeType !== 'image/jpeg') {
            return res.status(400).json({ error: 'Unsupported image type' });
        }

        // Load the image from the uploaded buffer
        const img = await canvas.loadImage(req.file.buffer);

        // Create a canvas from the loaded image
        const c = canvas.createCanvas(img.width, img.height);
        const ctx = c.getContext('2d');
        ctx.drawImage(img, 0, 0, img.width, img.height);

        // Detect face in the image using the canvas
        const detection = await faceapi
            .detectSingleFace(c)
            .withFaceLandmarks()
            .withFaceDescriptor();

        if (detection) {
            res.json({ message: 'Face detected', detected: true });
        } else {
            res.json({ message: 'No face detected in the provided image', detected: false });
        }
    } catch (error) {
        console.error('Error during face detection:', error);
        res.status(500).json({ error: 'Error during face detection', error });
    }
};

