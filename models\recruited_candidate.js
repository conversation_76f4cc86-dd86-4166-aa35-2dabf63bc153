const mongoose = require("mongoose");
const RecruitedCandidateSchema = mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    company: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Company',
            required: true
        },

        company: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Job',
            required: true
        },
    
    date: {
        type: Date,
        default: Date.now(),
      },
    
});
const RecruitedCandidate = mongoose.model('Comment', RecruitedCandidateSchema);
module.exports = RecruitedCandidate