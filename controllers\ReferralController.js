const Referral = require('../models/Referral');

// Create a new referral
exports.createReferral = async (req, res) => {
    try {
        const referralsData = req.body; // Expecting an array of referrals in the request body
        const savedReferrals = await Referral.insertMany(referralsData);
        res.status(201).json(savedReferrals);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Get all referrals
exports.getReferrals = async (req, res) => {
    try {
        const referrals = await Referral.find().populate('preference');
        res.status(200).json(referrals);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Get a single referral by ID
exports.getReferralById = async (req, res) => {
    try {
        const referral = await Referral.findById(req.params.id).populate('preference');
        if (!referral) return res.status(404).json({ error: "Referral not found" });
        res.status(200).json(referral);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Update a referral entry
exports.updateReferral = async (req, res) => {
    try {
        const referrals = req.body; // Expecting an array of referral updates in the request body

        const updatePromises = referrals.map(referral =>
            Referral.findByIdAndUpdate(referral.id, referral, { new: true })
        );

        const updatedReferrals = await Promise.all(updatePromises);
        res.status(200).json(updatedReferrals);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Delete a referral entry
exports.deleteReferral = async (req, res) => {
    try {
        const deletedReferral = await Referral.findByIdAndDelete(req.params.id);
        if (!deletedReferral) return res.status(404).json({ error: "Referral not found" });
        res.status(200).json({ message: "Referral deleted successfully" });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};
