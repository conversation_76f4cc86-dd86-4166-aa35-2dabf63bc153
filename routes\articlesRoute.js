const express = require("express");
const {
  getArticles,
  postArticle,
  getArticlesByCategory,
  updateArticle,
  deleteArticle,
  getArticlesById,
  getArticlesAdmin
} = require("../controllers/articlesController");
const upload = require("../middleware/imageUpload");
const router = express.Router();


router.get("/data", getArticles);
router.get("/data/admin", getArticlesAdmin);
router.get("/data/:articleId", getArticlesById);
router.post("/data-category", getArticlesByCategory);
router.post("/post", upload.single("image"), postArticle);
router.put("/update/:articleId", upload.single("image"), updateArticle);
router.delete("/delete/:articleId", deleteArticle);

module.exports = router;
