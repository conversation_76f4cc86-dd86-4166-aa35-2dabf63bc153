const mongoose = require("mongoose");


const preferenceSchema = new mongoose.Schema({
    candidate: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Candidate",
        required: true
    },
    jobSeekingStatus: {
        type: String,
        required: true
    },
    companySize: {
        type: Number,
        required: true
    }, // Company size as an integer
    workStyle: {
        type: String,
        required: true
    }, // e.g., Remote, On-site, Hybrid
    location: {
        type: String,
        required: true
    }, // e.g., specific city or state
    minBaseSalary: {
        type: Number,
        required: true
    },
    maxBaseSalary: {
        type: Number,
        required: true
    },

});

module.exports = mongoose.model("Preference", preferenceSchema);
