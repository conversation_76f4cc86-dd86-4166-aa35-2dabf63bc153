const mongoose = require("mongoose");

const CandidateSchema = mongoose.Schema({
  First_name: {
    type: String,
    default: "",
    trim: true,
    //  required: true,
  },
  Last_name: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Email: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  GenerateToken: {
    type: String,
    default: "",
    trim: true,
  },
  Phone: {
    type: Number,
    default: "",
    trim: true,
    //  required: true,
  },
  Heading: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Info: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Password: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Experience: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Education: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Avatar: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Cover_Image: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Country: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  City: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Date_of_birthday: {
    type: Date,
    default: "",
    trim: true,
    //   required: true,
  },

  Status: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Bio: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  linkedIn: {
    type: String,
    default: "",
    trim: true,
  },
  Github: {
    type: String,
    default: "",
    trim: true,
  },
  Citizenship: {
    type: String,
    default: "",
    trim: true,
    //  required: true,
  },
  Summary: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Gender: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Grade: {
    type: String,
    default: "",
    trim: true,
    //   required: true,
  },
  Language: {
    type: Array,
    default: "",
    trim: true,
    //    required: true,
  },
  Score: {
    type: Number,
    default: "",
    trim: true,
    //   required: true,
  },
  Test_time: {
    type: Number,
    default: "",
    trim: true,
    //  required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Company",
  },
  resizedWindow: {
    type: Boolean,
    default: false,
  },
  Location: {
    type: String,
    default: "",
    trim: true,
  },
  job_position: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "JobPosition",
  },
});

const Candidate = mongoose.model("Candidate", CandidateSchema);
module.exports = Candidate;
