const express = require("express");
const {
  getJobs,
  postJob,
  postJobByCompanyName,
  getJobsByCompanyId,
  postNewJob,
} = require("../controllers/jobsController");
const upload = require("../middleware/imageUpload");
const router = express.Router();

router.get("/data", getJobs);
router.get("/myJobs", getJobsByCompanyId);
router.post("/postJob", postNewJob);
// router.post("/post/:companyName", postJobByCompanyName);
router.post("/post", upload.single("imgFileId"), postJob);
module.exports = router;
