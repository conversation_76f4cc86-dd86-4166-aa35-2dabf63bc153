require("dotenv").config();
const express = require("express");
const path = require("path");
const cors = require("cors");
const corsOptions = require("./config/corsOptions");
// const domainRedirect = require('./middleware/domainRedirect');



const { logger } = require("./middleware/logEvent");
const { errorHandler } = require("./middleware/errorHandler");
const bodyParser = require("body-parser");
const morgan = require("morgan");
const cookieParser = require("cookie-parser");

// process.env.TF_CPP_MIN_LOG_LEVEL = "2"; // 0 = all logs, 1 = info, 2 = warnings, 3 = errors only
// const tf = require("@tensorflow/tfjs-node");
// const tf = require("./config/tfInstance.js");
const app = express();

// Cross Origin Resource Sharing
app.use(cors(corsOptions));
// Domain redirect middleware
// app.use(domainRedirect);

// Custom Middleware logger
app.use(logger);
app.set("trust proxy", "loopback");

// built-in middleware for json
app.use(cookieParser());

// serve static files
app.use(express.static(path.join(__dirname, "/public")));

if (process.env.NODE_ENV === "development") {
  // morgan dev
  app.use(morgan("dev"));
}

app.set("view engine", "ejs");

app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ limit: "50mb", extended: true }));

// Additional CORS configuration for all routes
// app.use((req, res, next) => {
//   res.header("Access-Control-Allow-Origin", "*");
//   res.header(
//     "Access-Control-Allow-Headers",
//     "Origin, X-Requested-With, Content-Type, Accept"
//   );
//   res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
//   next();
// });
app.use((req, res, next) => {
  req.candidateIPAddress = req.ip; // Store the IP address in the request object
  next();
});
// Quiz

// routes
app.use("/", require("./routes/root"));

// contact us email
app.use("/contact-us", require("./routes/ContactUsRoute"));

// tooltip
app.use("/tooltip", require("./routes/TooltipRoutes"));
//generate link and send email to candidate
app.use("/inviteCandidate", require("./routes/inviteCandidateRoute"));
app.use("/shareJobOnLinkedin", require("./app/shareJobOnLinkedin/route.js"));
app.use("/invite", require("./routes/inviteFriendsRoutes"));

// request plan activation
app.use("/requestPlan", require("./routes/RequestPlanRoute"));
app.use("/Notif", require("./routes/notificationsRoute"));

app.use("/authAdmin", require("./routes/AdminRoutes"));

//company
app.use("/company", require("./routes/CompanyRoutes"));
//request-service
app.use("/request-service", require("./routes/RequestServiceRoutes"));
//anticheat
app.use("/anticheat", require("./routes/CheaterRoutes"));

//recruiter
app.use("/users", require("./routes/recruitersRoute"));
app.use("/user", require("./routes/recruitersRoute"));

//candidates
app.use("/candidates", require("./routes/CandidateRoutes"));
app.use("/portfolio", require("./routes/PortfolioRoutes"));
app.use("/job-board", require("./routes/JobBoardRoutes"));
app.use("/referrals", require("./routes/ReferralRoutes"));

//assessments
app.use("/projects", require("./routes/ProjectRoutes"));
app.use("/AssessmentTest", require("./routes/AssessmentTestRoutes"));
app.use("/certificate", require("./routes/CertificateRoutes"));
app.use("/jobPositions", require("./routes/JobPositionRoutes"));

// new routes
app.use("/jobs", require("./routes/jobsRoutes"));
app.use("/articles", require("./routes/articlesRoute"));
app.use("/case-studies", require("./routes/CaseStudyRoutes"));
app.use("/categories", require("./routes/CategoryRoutes"));

app.use("/invited-candidates", require("./routes/InvitedCandidateRoutes"));

//upload assessments and interceptions to db
app.use("/uploadAssessment", require("./routes/uploadQuizeRoute"));
app.use("/interpretations", require("./routes/InterpretationRoutes"));
app.use("/interceptions", require("./routes/interceptionsRoute"));

app.use("/CustomQuestion", require("./routes/CustomQRoutes"));

//Payment
app.use("/payment", require("./routes/PaymentRoutes"));

// Use preference routes
app.use("/preferences", require("./routes/preferenceRoutes"));

// Use experience routes
app.use("/experiences", require("./routes/experienceRoutes"));

// Use certification routes
app.use("/certifications", require("./routes/certificationRoutes"));

// Use education routes
app.use("/education", require("./routes/educationRoutes"));

app.use("/face-detection", require("./routes/FaceDetectionRoutes"));

//const linkedInRoutes = require('./routes/linkedInAuthRoute3');
const config = require("./config");
app.use(express.urlencoded({ extended: true }));

//linkedIn login
app.use("/linkedIn", require("./routes/linkedInAuthRoute"));
app.use("/linkedInShare", require("./routes/linkedInAuthRoute2"));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use("/facebook", require("./routes/facebookAuthRoute"));
//description images for the assessments
app.use("/images", express.static("images"));
// plans
app.use("/views", express.static("views/index.html"));
app.use("/plans", require("./routes/plansRoute"));

app.use("/image-upload", require("./routes/AssessmentImageRoute"));

app.use("/jobsPages", require("./routes/jobsPagesRoute"));

// app.use("/generateCodes", require("./routes/GenerateCodesRoute"));

app.use("/fetchAll", require("./routes/fetchAllRoute"));

// MarketPlace routes

app.use("/talents", require("./routes/talentsRoute"));
app.use("/talentAssessments", require("./routes/talentAssessmentsRoute.js"));

app.use("/data", require("./routes/talentsRoute"));

app.use("/api", require("./app/api/route"));
app.use("/api/geolocation", require("./routes/geolocation"));

app.use("/evaluate", require("./routes/EvaluationRoute.js")); 

// WEBSUMMIT SPECIAL
app.use("/websummit", require("./app/websummitSpecial/route.js"));
// error handle 404 Not Found Page
app.all("*", (req, res) => {
  res.status(404);
  if (req.accepts("html")) {
    res.sendFile(path.join(__dirname, "views", "404.html"));
  } else if (req.accepts("json")) {
    req.json({ error: "404 Not Found" });
  } else {
    res.type("txt").send("404 Not Found");
  }
});

app.use(errorHandler);

module.exports = app;
