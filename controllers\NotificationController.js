const Notification = require("../models/notifications.js");

exports.createNotification = async (req, res) => {
  let { title, text, reciever } = req.body;

  const newNotif = new Notification(title, text, reciever);
  
  try {
    await newNotif.save();
    // Emit the notification to the specific receiver
    io.to(reciever).emit('new-notification', newNotif);
    res.send("Notification created");

  } catch (error) {
    console.error(error);
    
  }
};


exports.getAllNotification = async (req, res) => {
  try {
    const { companyID } = req.query;
    const notifications = await Notification.find({reciever:companyID}).sort({ date: -1 }); // Latest first
    res.json(notifications);
  
  } catch (error) {
    res.status(500).json({ error: 'Error fetching notifications' });
  }
};


exports.getNotificationCount = async (req, res) => {
  try {
    const { companyID } = req.query;    
    const unseenCount = await Notification.countDocuments({reciever:companyID, seen: false});
    // Calculate the date 2 weeks ago
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
    const mounth = new Date();
    mounth.setDate(mounth.getDate() - 30);
    // Delete notifications
    const result = await Notification.deleteMany({
      seen: true,
      date: { $lt: twoWeeksAgo }, // $lt means "less than"
    });

    const result1 = await Notification.deleteMany({
      seen: false,
      date: { $lt: mounth }, // $lt means "less than"
    });

    res.json(unseenCount);
    
  } catch (error) {
    res.status(500).json({ error: 'Error fetching notifications' });
  }
};

exports.markNotificationAsSeen = async (req, res) => {
  try {
    const { notifID } = req.query;
    const notification = await Notification.findByIdAndUpdate(
      notifID,
      { seen: true },
      { new: true }
    );
    if (notification) {
      console.log("Notifications been updated");
    } else {
      res.status(404).json({ error: 'Notification not found' });
    }
  } catch (error) {
    res.status(500).json({ error: 'Error updating notification' });
  }
};





exports.markAllNotificationAsSeen = async (req, res) => {
  try {
    const { companyID } = req.query;

    const result = await Notification.updateMany(
      { reciever: companyID, seen: false }, // Filter: unseen notifications for the specific user
      { $set: { seen: true } } // Update: set `seen` field to `true`
    );

    if (result) {
      console.log("Notifications been updated");
    }
    
  
  } catch (error) {
    res.status(500).json({ error: 'Error updating notifications' });
  }
};