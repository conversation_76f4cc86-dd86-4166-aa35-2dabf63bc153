const express = require("express");
//const { createNotification } = require("../controllers/NotificationController.js");
const notificationController = require('../controllers/NotificationController.js');
//const { getAllNotification } = require("../controllers/getAllNotification.js");
const root = express.Router();
// Create a new notification (for testing or manual creation)
root.post('/notifications', async (req, res) => {
    try {
      const { title, text, receiver } = req.body;
      const notification = await notificationController.createNotification(title, text, receiver);
      res.status(201).json(notification);
    } catch (error) {
      res.status(500).json({ error: 'Error creating notification' });
    }
  });

  root.get("/getNotifications", notificationController.getAllNotification);
  root.get("/getNotificationCount", notificationController.getNotificationCount);
  root.post("/updateNotifications",notificationController.markNotificationAsSeen);
  root.post("/updateAllNotifications",notificationController.markAllNotificationAsSeen);

  

  // Mark a notification as seen
root.post('/notifications/:id/mark-as-seen', async (req, res) => {
    try {
      const { id } = req.params;
      const notification = await notificationController.markNotificationAsSeen(id);
      if (notification) {
        res.status(200).json(notification);
      } else {
        res.status(404).json({ error: 'Notification not found' });
      }
    } catch (error) {
      res.status(500).json({ error: 'Error updating notification' });
    }
  });

//root.post("/create", createNotification);
//root.get("/getNotif", getAllNotification);
module.exports = root;

