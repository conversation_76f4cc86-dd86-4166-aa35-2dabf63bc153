const jwt = require("jsonwebtoken");
const User = require("../models/user");
const Comment = require("../models/comment");
const Notification = require("../models/notifications.js");
const CandidatesLocation = require("../models/Candidate.js"); // Adjust path if needed
const companyProject = require("../models/companyProject");
const mongoose = require("mongoose");
const CandidateEvaluation = require("../models/CandidateEvaluation.js");
const CandidateEvaluationCode = require("../models/CandidateEvaluationCode.js");
const Candidate = require("../models/Candidate");
const Invitation = require("../models/Invitations");
const validator = require("validator");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");
const Company = require("../models/Company");
const allAssessments = require("../models/allAssessments.js");
const Challenge = require('../models/Challenge');
const getComments = async (userid, res) => {
  try {
    const comments = await Comment.find({ user: Object.keys(userid.body)[0] });
    console.log("Comments for Project:", comments);
    res.send({ comments: comments });
  } catch (error) {
    console.error("Error fetching comments:", error);
  }
};

const likeComment = async (commentId) => {
  try {
    const updatedComment = await Comment.findByIdAndUpdate(
      commentId,
      { $inc: { likes: 1 } }, // Increment the likes count by 1
      { new: true } // Return the updated document
    );
    console.log("Updated Comment:", updatedComment);
  } catch (error) {
    console.error("Error liking comment:", error);
  }
};

const addComment = async (comment) => {
  try {
    const newComment = new Comment({
      user: comment.body[0].userId,
      name: comment.body[0].author,
      text: comment.body[0].text,
    });
    await newComment.save();
    console.log("Comment added successfully");
  } catch (error) {
    console.error("Error adding comment:", error);
  }
};

const createProject = async (req, res) => {
  let token = req.cookies.user;
  const project = req.body.project;
  console.log(req.body);
  console.log({ project: JSON.stringify(project) });

  // Filter out "Programming Skills" assessments
  const filteredProjects = {
    ...project,
    assessments: project.assessments.filter(a => a.category !== "Programming Skills")
  };
  console.log({ filteredProjects: JSON.stringify(filteredProjects) });

  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    // if (err) {
    //   console.log({ err });
    //   return res.status(401).json({
    //     title: "unauthorized",
    //   });
    // }
    //token is valid

    let company = await Company.findOne({
      name: decoded.company_name,
    });
    if (company.plan === "free") {
      filteredProjects.assessments.map(async (assessment) => {
        console.log({ RATINGG: assessment.rating, name: assessment.name });
        const assess = await allAssessments.findOne({ name: assessment.name });
        if (assess.rating !== "essential") {
          return res.status(401).json({
            title: "unauthorized",
          });
        }
      });
    }

    
    // let projectOBJ = project.toObject();
    filteredProjects.company_name = decoded.company_name;
    const projectToSave = await companyProject(filteredProjects);
    const savedProject = await projectToSave.save();


    if (req.body.project.is_coding_required) {
    console.log("is_coding_required is true, creating coding challenge..."); 
      // Use the first assessment as the coding challenge source
  const assessments = req.body.project.assessments;
  const programmingAssessments = assessments.filter(assessment => assessment.category == "Programming Skills");
  console.log(programmingAssessments);

  // Extract just the IDs from the assessments array
    const assessmentIds = programmingAssessments.map(assessment => assessment._id);

  // Extract fields for the Challenge model
  const challenge = new Challenge({
    title: project.name, // e.g., "Python coding"
    idProject: savedProject._id, // Reference to the project
    assessments: assessmentIds,  
    
  });

  await challenge.save();
  console.log(challenge);

// Update the project with the new challenge ID
    if (savedProject._id) {
      await companyProject.findByIdAndUpdate(
        savedProject._id,
        { 
          $set: { 
            idChallenge: challenge._id,
            
          } 
        },
        { new: true }
      );
    }

    }

     

    const recruiters = await User.find({
      company_name: { $in: [decoded.company_name] },
    });
    for (const rec of recruiters) {
      //     const notification = await notificationController.createNotification("NEW PROJECT: "+project.name,"JOB TITLE: "+project.jobTitle+" AND SENIORITY: "+project.seniority , rec._id);
      const newNotif = new Notification({
        title: "New project: " + project.name,
        text: "JOB : " + project.jobTitle + ", SENIORITY: " + project.seniority,
        reciever: rec._id,
        link: "/boards?id=" + savedProject._id,
      });
      await newNotif.save();
    }
    res.send(savedProject);
  });
};
const getProjects = async (req, res) => {
  let token = req.cookies.user;
  console.log({ QUERYYY: req.query });
  const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
  const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page if not provided
  const status = req.query.status;
  const search = req.query.search;
  const skip = (page - 1) * limit;
  console.log({ page, limit, status, search });
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    //token is valid
    try {
      const query = {
        company_name: decoded.company_name,
      };

      // Add the `project_status` condition only if it's not "All"
      if (status !== "All") {
        query.project_status = status;
      }
      if (search !== "") {
        query.name = { $regex: search, $options: "i" }; // Case-insensitive search
      }

      const projects = await companyProject
        .find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      let myProjects = [];
      await Promise.all(
        projects.map(async (project) => {
          project.invitationsNbr = await Invitation.countDocuments({
            projectId: project._id,
          });
          project.passedCandidatesNbr =
            await CandidateEvaluation.countDocuments({
              projectId: project._id,
            });
        })
      );
      const totalCount = await companyProject.countDocuments(query);
      console.log({ totalCount });
      const totalPages = Math.ceil(totalCount / limit);

      const totalProjects = await companyProject.countDocuments({
        company_name: decoded.company_name,
      });
      const activeProjects = await companyProject.countDocuments({
        company_name: decoded.company_name,
        project_status: "Active",
      });
      const archivedProjects = await companyProject.countDocuments({
        company_name: decoded.company_name,
        project_status: "Archived",
      });
      // myProjects.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      console.log("done fetching...;", activeProjects, archivedProjects);
      res.send({
        myProjects: projects,
        totalPages,
        projectsData: {
          totalProjects,
          activeProjects,
          archivedProjects,
        },
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const updateProject = async (req, res) => {
  //   const { id } = req.params;
  const { project, customAssessment,codingChallenge } = req.body;
  if (!mongoose.Types.ObjectId.isValid(project._id))
    return res.status(404).send(`No project with id: ${project._id}`);
  // const updatedProject = { name, jobTitle, seniority, assessments, project_description, project_priority, project_status, _id: id };
  try {
    // Removed undefined 'coding' variable check
    console.log({
      customAssessment,
      project,
      projectASSESS: project.assessments,
    });

    if (customAssessment) {
      let updatedCustom = {};
      let customAssessId = customAssessment._id;
      console.log({
        customAssessmentFROMINSIDE: customAssessment.questions_list,
      });
      delete customAssessment._id;
      updatedCustom = await allAssessments.findOne({
        _id: customAssessId,
      });
      updatedCustom = await allAssessments.findByIdAndUpdate(
        customAssessId,
        { $set: { ...customAssessment } },
        { new: true }
      );
    }
    let updatedProject = {};
    let projectID = project._id;
    delete project._id;
    updatedProject = await companyProject.findByIdAndUpdate(
      projectID,
      { $set: { ...project } },
      { new: true }
    );

    res.status(200).json({ message: "Project updated successfully" });
  } catch (error) {
    console.log({ error });
    console.error("Error updating project:", error);
  }
  // res.json(updatedProject);
};

const switchProjectStatus = async (req, res) => {
  const { id } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);

    let updatedProject = {};
    if (project.project_status === "Archived") {
      updatedProject = await companyProject.findByIdAndUpdate(
        id,
        { project_status: "Active" },
        { new: true }
      );
    } else {
      updatedProject = await companyProject.findByIdAndUpdate(
        id,
        { project_status: "Archived" },
        { new: true }
      );
    }

    res.json(updatedProject);
  } catch (error) {
    console.error("Error updating project:", error);
  }
};

const deleteProject = async (req, res) => {
  const { id } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    // Assuming 'Project' is your Mongoose model for projects
    const deletedProject = await companyProject.findByIdAndDelete(id);

    if (!deletedProject) {
      return res.status(404).send(`No project found with id: ${id}`);
    }

    return res.status(200).json({ message: "Project deleted successfully" });
  } catch (error) {
    console.error("Error deleting project:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

const getProject = async (req, res) => {
  const id = validator.escape(req.query.id);

  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);

    res.send({ project: project });
  } catch (error) {
    console.log(error);
  }
};

const duplicateProject = async (req, res) => {
  const { id } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);
    const OBJProject = project.toObject();

    let newProject = {
      ...OBJProject,
      name: OBJProject.name + " (duplicated)",
      _id: mongoose.Types.ObjectId(),
      project_status: "Active",
    };
    const projectToSave = await companyProject(newProject);
    const savedProject = await projectToSave.save();

    return res.status(200).json({ message: "Project duplicated successfully" });
  } catch (error) {
    console.log(error);
  }
};

const getProjectName = async (req, res) => {
  const { id } = req.body;
  console.log(req.body);
  
  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);
    console.log("this is the project you asking");
    console.log(project);
    console.log("this is the project you asking");


    res.status(200).json({ project });
  }
  catch (error) {
    console.log(error);
    res.status(500).json({ message: error.message });
  }


};


const getProjectData = async (req, res) => {
  const { id } = req.query;
  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);

  
    const candidatesEvaluation = await CandidateEvaluation.find({
      projectId: project._id,
      $or: [{ passed: true }, { passed: { $exists: false } }],
    });

    /*const candidatesEvaluationCode = await CandidateEvaluationCode.find({
      idProject: project._id,
      
    });*/


    let candidates = [];


    const enrichedEvaluations = await Promise.all(
      candidatesEvaluation.map(async (evaluation) => {
        // Find candidate by email (email in evaluation is lowercase)
        const candidate1 = await CandidatesLocation.findOne({ Email: evaluation.candidate });
        console.log('Found candidate:', candidate1);
        console.log('Location field:', candidate1?.Location);
        console.log('Country field:', candidate1?.Country);

        return {
          ...evaluation,
          location: candidate1 ? candidate1.Location || candidate1.Country ||"Unknown" : "Candidate not found",
        };
      })
    );
    console.log({ enrichedEvaluations });
const countryCounts = {};
    enrichedEvaluations.forEach(entry => {
      if (entry.location) {
        const parts = entry.location.split(',').map(part => part.trim());
        const country = parts[parts.length - 1].toUpperCase(); // Capitalize
        countryCounts[country] = (countryCounts[country] || 0) + 1;
      }
    });
    
    // Step 2: Display result
    console.log(countryCounts);
    

    candidatesEvaluation.map(async (candidate) => {
      const candidateObject = candidate.toObject();
      const candidateScoreExists = candidates.filter((existingCandidate) => {
        return (
          existingCandidate.candidate == candidateObject.candidate &&
          existingCandidate.projectId == candidateObject.projectId
        );
      });

      if (candidateScoreExists.length === 0) {
        candidates.push(candidateObject);
      }
    });
    let chartData = [];
    await Promise.all(
      candidates.map(async (candidate) => {
        let score = 0;
        const candidateInfo = await Candidate.findOne({
          Email: candidate.candidate,
        });

        candidate.name = `${candidateInfo && candidateInfo.First_name || ''} ${candidateInfo && candidateInfo.Last_name || ''}`;
        //candidate.name = `${candidateInfo.First_name} ${candidateInfo.Last_name}`;

        let result = calculateScore(candidate, project);

        let Avatar = "";
        let candidateImg = "";
        if (
            candidateInfo && candidateInfo.Avatar &&
          !candidateInfo.Avatar.toLowerCase().includes(".jpg")
        ) {
          Avatar = await Photo_Files.findOne({
            _id: candidateInfo.Avatar,
          });
        }

        if (Avatar) {
          let imgChunks = await Photo_chunks.find({
            files_id: Avatar._id,
          });

          imgChunks.map((chunk) => {
            candidateImg += chunk.data.toString("base64");
          });
        }

        if(candidateInfo){
            chartData.push({
                name: candidateInfo.First_name + " " + candidateInfo.Last_name,
                initials:
                    candidateInfo.First_name[0].toUpperCase() +
                    candidateInfo.Last_name[0].toUpperCase(),
                img: candidateImg,
                score: parseFloat(result.score),
                time: parseFloat(result.time),
            });
        }

      })
    );

    const invited = await Invitation.find({ projectId: project._id });

    res.status(200).json({ project, invited, candidates, chartData, countryCounts });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: error.message });
  }
};

const calculateOverAllScore = (results, project) => {
  if (!Array.isArray(results) || results.length === 0) return 0;

  // Filter out results with 'personalityResults'
  const filteredResults = results.filter(
    (result) => !result.personalityResults && !result.customResults
  );
  let score = 0;
  filteredResults.forEach((result) => {
    const assessment = project.assessments.find(
      (assessment) => assessment.name === result.assessmentName
    );

    if (result.totalPoints && result.quesionsNbr && assessment) {
      // score = result.totalPoints;
      // possibleScore = result.quesionsNbr; // Max possible score is the number of questions
      score +=
        ((result.totalPoints * 100) / result.quesionsNbr) *
        (assessment.weight || 1);
    } else if (result.rangesPoint && result.quesionsNbr && assessment) {
      score +=
        (result.rangesPoint / (result.quesionsNbr * 5)) *
        100 *
        (assessment.weight ? assessment.weight : 1);
    }
  });
  let totalWeights = 0;
  project.assessments
    .filter(
      (assess) =>
        assess.category != "Custom" && assess.category != "Personality"
    )
    .forEach((assess) => (totalWeights += 1 * (assess.weight || 1)));

  // Avoid division by zero
  let finalScore = score / totalWeights;

  return finalScore;
};

const calculateScore = (candidate, project) => {
  let score = 0;

  const averageScore = calculateOverAllScore(candidate.results, project);
  const roundedScore = parseFloat(averageScore.toFixed());

  // points.forEach((element) => {
  //   score += (element.totalPoints * 100) / element.quesionsNbr;
  // });
  let time = 0;

  let minutes = Math.floor(candidate.candidateTime / (60 * 1000));
  if (minutes > 59) minutes = 59;

  let seconds = candidate.candidateTime % (60 * 1000);
  if (seconds > 59) seconds = 59;

  // minutes = minutes.toString().padStart(2, "0");

  // if (seconds < 10) {
  //   seconds = "0" + seconds.toString();
  // } else {
  //   seconds = seconds.toString();
  // }

  // time = `${minutes}:${seconds}`;
  time = parseFloat(minutes + "." + seconds).toFixed(2);
  // score = parseFloat(score / points.length).toFixed(2);
  return { score: roundedScore, time };
};

const getProjectQsts = async (req, res) => {
  const { id } = req.query;

  if (!mongoose.Types.ObjectId.isValid(id))
    return res.status(404).send(`No project with id: ${id}`);
  try {
    const project = await companyProject.findById(id);
    let questions = [];

    await Promise.all(
      project.assessments?.map(async (assessment) => {
        let assess = await allAssessments.findOne({ _id: assessment._id });
        let sampleQst = {
          assessmentName: assessment.name,
          description: assess.description_test,
          questions: assess.questions_list.slice(0, 5),
        };
        questions.push(sampleQst);
      })
    );
    res.status(200).json({ questions });
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: error.message });
  }
};

const updateWeightning = async (req, res) => {
  const { projectId, assessments } = req.body;

  if (!mongoose.Types.ObjectId.isValid(projectId)) {
    return res
      .status(400)
      .json({ message: `Invalid project ID: ${projectId}` });
  }

  try {
    const project = await companyProject.findById(projectId);
    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }
    console.log({ project });
    project.assessments = assessments;

    await project.save();

    res.status(200).json({ message: "Project updated successfully", project });
  } catch (error) {
    console.error("Error updating project:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

module.exports = updateWeightning;

module.exports = {
  addComment,
  likeComment,
  getComments,
  createProject,
  updateProject,
  getProjects,
  getProjectName,
  //getProjectsNeed,
  switchProjectStatus,
  deleteProject,
  getProject,
  duplicateProject,
  getProjectData,
  getProjectQsts,
  updateWeightning,
};
