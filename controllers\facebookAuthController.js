const qs = require("querystring");
const axios = require("axios");



// 1. Authorization Function
const facebookAuthorization = (req, res) => {
    const state = Buffer.from(Math.random().toString()).toString('hex');
    const scope = 'public_profile,email,pages_show_list,publish_to_groups,publish_pages';
    console.error("VERIFICATION1");

    // Generate the Facebook authorization URL
    return encodeURI(
        `https://www.facebook.com/v11.0/dialog/oauth?client_id=${process.env.FACEBOOK_APP_ID}&redirect_uri=${process.env.FACEBOOK_REDIRECT_URI}&state=${state}&scope=${scope}`
    );
    

    // Redirect user to Facebook's OAuth page for authorization
 //   res.redirect(authUrl);
};

// 2. Redirect and Share Function (Post on Facebook)
const facebookRedirectAndShare = async (req, res) => {
    try {
        onsole.error("VERIFICATION2");
        // Step 1: Get authorization code from query
        const code = req.query.code;

        // Step 2: Exchange code for access token
        const tokenResponse = await getFacebookAccessToken(code);
        const accessToken = tokenResponse.access_token;

        // Step 3: Define the content to post
        const content = {
            text: "Hey, check out this amazing invite link to pass assessments",
            shareUrl: "https://go-platform.com/"  // This is the URL you want to share
        };

        // Step 4: Post content to Facebook using access token
        await postToFacebook(accessToken, content);
        onsole.error("VERIFICATION3");
        // Step 5: Redirect user to dashboard/home after successful post
        res.redirect("/");
    } catch (error) {
        console.error("Error during Facebook post:", error);
        res.redirect("/error");
    }
};

// Function to get Facebook access token
const getFacebookAccessToken = async (code) => {
    const params = qs.stringify({
        client_id: process.env.FACEBOOK_CLIENT_ID,
        redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
        client_secret: process.env.FACEBOOK_APP_SECRET,
        code: code,
    });

    const url = `https://graph.facebook.com/v14.0/oauth/access_token?${params}`;

    try {
        const response = await axios.get(url);
        return response.data;
    } catch (error) {
        console.error('Error getting access token:', error);
        throw new Error('Failed to get access token from Facebook');
    }
};

// Function to post content to Facebook
const postToFacebook = async (accessToken, content) => {
    const postUrl = 'https://graph.facebook.com/me/feed';

    const data = {
        message: content.text,  // Message text for the post
        link: content.shareUrl,  // URL link you want to share
        access_token: accessToken  // Access token from OAuth
    };

    try {
        const response = await axios.post(postUrl, data);
        console.log('Post successful:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error posting to Facebook:', error);
        throw new Error('Failed to post content to Facebook');
    }
};

module.exports = {
    facebookAuthorization,
    facebookRedirectAndShare
};



/*const qs = require("querystring");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const request = require('request');
const { clientId, clientSecret, authorizationURL, redirectURI, accessTokenURL } = require('../config');
var link;
const authorization = (params) => {
    const { title, text, thumb } = params;
    console.log(`Title: ${title}, Text: ${text}, Thumbnail: ${thumb}`);

    console.log(`HERE IS THE LINK: ${thumb}`);
    link = thumb;
    const state = Buffer.from(Math.random().toString()).toString('hex');
    const scope = 'public_profile,email,pages_show_list,publish_to_groups,publish_pages';
    

  //  const state = Buffer.from(Math.round(Math.random() * Date.now()).toString()).toString('hex');
        //const scope = encodeURIComponent('r_liteprofile r_emailaddress w_member_social');
//        const url = `${authorizationURL}?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectURI)}&state=${state}&scope=${scope}`;

        //return encodeURI(
          //  `https://www.linkedin.com/oauth/v2/authorization?client_id=${process.env.LINKEDIN_CLIENT_ID}&response_type=code&scope=${process.env.SCOPE}&redirect_uri=${process.env.REDIRECT_URI_SHARES}`
        //);

        return encodeURI(
            `https://www.facebook.com/v11.0/dialog/oauth?client_id=${process.env.FACEBOOK_CLIENT_ID}&redirect_uri=${process.env.FACEBOOK_REDIRECT_URI}&state=${state}&scope=${scope}`
        );

};


const redirect = async (req) => {
    const code = req.query.code;
    let token = req.cookies.candidatecookie;
    console.log({ TTOOOOOOOOKKEEEENNNNNNN: token });
    const payload = qs.stringify({
        client_id: process.env.FACEBOOK_CLIENT_ID,
        client_secret: process.env.FACEBOOK_APP_SECRET,
        redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
        grant_type: "authorization_code",
        code: code,

    });
    console.log({ payload });
    const url = `https://graph.facebook.com/v14.0/oauth/access_token?${payload}`;
    try {
        const response = await axios.get(url);
        return response.data.access_token;
    } catch (error) {
        console.error('Error getting access token:', error);
        throw new Error('Failed to get access token from Facebook');
    }

    
    

    const data = await axios({
        method: "POST",
        url: "https://www.linkedin.com/oauth/v2/accessToken",
        data: payload,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    })
        .then(async (res) => {
            const userInfo = await axios.get("https://api.linkedin.com/v2/userinfo", {
                headers: {
                    Authorization: `Bearer ${res.data.access_token}`,
                    "Content-Type": "application/json",
                },
            });

            console.log("User Info:", userInfo.data);


            const postData = {
                author: `urn:li:person:${userInfo.data.sub}`,
                lifecycleState: "PUBLISHED",
                specificContent: {
                    "com.linkedin.ugc.ShareContent": {
                        shareCommentary: {
                            text: "Hey, check out this amazing invite link to pass assessments",
                        },
                        shareMediaCategory: "ARTICLE",
                        mediaDescription: {
                            text: "Description for the article",
                        },
                        media: [
                            {
                                originalUrl: link, 
                                thumbnailUrl: "https://i.imgur.com/Ow3HzAr.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500", // Direct link to the image
                                title: {
                                    text: "Assessment Link",
                                },
                                description: {
                                    text: "Description for your platform",
                                },
                                status: "READY",
                            },
                        ],
                        mediaTitle: {
                            text: "Title of the platform showcase",
                        },
                    },
                },
                visibility: {
                    "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
                },
            };

            const config = {
                headers: {
                    Authorization: `Bearer ${res.data.access_token}`,
                    "Content-Type": "application/json",
                },
            };

            axios
                .post("https://api.linkedin.com/v2/ugcPosts", postData, config)
                .then((response) => {
                    console.log("UGC Post created successfully:", response.data);
                           
                })
                .catch((error) => {
                    console.error("Error creating UGC Post:", error);
                });
          
            return res;
        })
        .catch((err) => {
            console.error({ err });
            return err;
        });
    console.log({ data: data });
    return data;
};

module.exports = {
    authorization,
        redirect,
};


/*
const axios = require('axios');
const qs = require('qs');
const jwt = require("jsonwebtoken");

require('dotenv').config();

const CLIENT_ID = process.env.FACEBOOK_APP_ID;
const CLIENT_SECRET = process.env.FACEBOOK_APP_SECRET;
const REDIRECT_URI = process.env.FACEBOOK_REDIRECT_URI;
const AUTH_URL = 'https://www.facebook.com/v17.0/dialog/oauth';
const TOKEN_URL = 'https://graph.facebook.com/v17.0/oauth/access_token';
const SHARE_URL = 'https://graph.facebook.com/v17.0/me/feed';

const authorization = (req, res) => {
    const authURL = encodeURI(
        `https://www.facebook.com/v11.0/dialog/oauth?client_id=${process.env.FACEBOOK_CLIENT_ID}&redirect_uri=${process.env.FACEBOOK_REDIRECT_URI}&scope=publish_to_groups,manage_pages,pages_manage_posts`
    );
    res.redirect(authURL);
};




const redirect = async (req, res) => {
    const code = req.query.code;

    if (!code) {
        return res.status(400).json({ success: false, message: 'Authorization code is missing from Facebook.' });
    }

    try {
        const tokenResponse = await axios.get(
            `https://graph.facebook.com/v11.0/oauth/access_token`, {
            params: {
                client_id: process.env.FACEBOOK_CLIENT_ID,
                client_secret: process.env.FACEBOOK_CLIENT_SECRET,
                redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
                code: code
            }
        });

        const accessToken = tokenResponse.data.access_token;

        // Store the access token in a cookie or session
        res.cookie('facebook_oauth_token', accessToken, { httpOnly: true });

        // Redirect to a page where the user can share content
        res.redirect("http://localhost:3000/share");
    } catch (error) {
        console.error('Error exchanging code for token:', error.response ? error.response.data : error.message);
        res.status(500).json({ success: false, message: 'Failed to exchange code for access token.' });
    }
};

const manualAccessToken = 'EAAQSt06OhZB0BO8XSJXJMKhQ0vW7AZAU8TFnZAPjVZCo4wZBSqmqsu99Bp5CpCTts0CDp49Oqw5T9SKkpoVRa6Tb8pjE5BQk76EKHXb6RY2vITsZCajTMuaru5ZAenvQgobJVNLWoag8VZAuifVb0JJBjhCRSFADbSpQwWnd2ZBVFa4xsaDMKQxjT2rJiZAgrk7ZC95CO0fZBVSGFJarw6SrdT2iLm3ZAdZBKcdM1T1rrR2kO9O6wPWtaIhUWcFUx9lGVuVLIZD';

const shareContent = async (req, res) => {
    const { link, message } = req.body;

    try {
        const response = await axios.post(
            `https://graph.facebook.com/v20.0/me/feed`, {
            message: message,
            link: link,
            access_token: manualAccessToken // Use the manual access token here
        });

        res.json({ success: true, message: "Content shared successfully on Facebook", data: response.data });
    } catch (error) {
        console.error('Error sharing content on Facebook:', error.response ? error.response.data : error.message);
        res.status(500).json({ success: false, message: "Failed to share content" });
    }
};


module.exports = {
    authorization,
    redirect,
    shareContent,
};
*/