const handleError = require("../helper/handleError");
const candidateService = require("../services/candidateService");
const candidateAuthService = require("../services/candidateAuthService");
const CandidateEvaluation = require("../models/CandidateEvaluation.js");
const Invitation = require("../models/Invitations");
const validator = require("validator");
const mongoose = require("mongoose");

const crypto = require("crypto");
const jwt = require("jsonwebtoken");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");
const Interpretation = require("../models/interpretation");
const Candidate = require("../models/Candidate");
const CandidateRating = require("../models/candidateRating");
const express = require("express");
const pdf = require("html-pdf");
const ejs = require("ejs");
const path = require("path");
const fs = require("fs");
const puppeteer = require("puppeteer");
const companyProject = require("../models/companyProject");
const Company = require("../models/Company");
const axios = require("axios");

const generateToken = async (length = 20) => {
  const characters =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789/*+-ù*%&é#ç@1234567890§$£µ";
  let token = "";

  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, characters.length);
    token += characters[randomIndex];
  }

  return token;
};

exports.generateToken = generateToken;

exports.createCandidate = async (req, res) => {
  let { state, errors } = handleError.withErrorRequest(req);
  let candidateIPAddress = req.ip;

  const locationData = await axios.get(
    `https://ipapi.co/${candidateIPAddress}/json/`
  );

  // Extract location information from the API response
  const { city, region, country_name } = locationData.data;

  // Construct the location string
  const location = `${city}, ${region}, ${country_name}`;

  // Assign the determined location to the Location field

  if (state) return res.status(400).json({ errors: errors.array() });
  const candidateInfo = JSON.parse(req.body.candidate);
  // const { First_name, Last_name, Email, Password } = req.body;
  const generatetoken = await generateToken(20); // Generate a unique identifier token

  try {
    let candidateFound = await candidateService.findCandidate(
      candidateInfo.Email
    );
    if (candidateFound) {
      const candidateEvaluation = await CandidateEvaluation.findOne({
        candidate: validator.normalizeEmail(candidateInfo.Email, {
          gmail_remove_dots: false,
        }),
        projectId: validator.escape(candidateInfo.projectId),
      });
      if (candidateEvaluation) {
        return res.status(401).send("You have already passed this test");
      }

      let Avatar = "";
      if (
        candidateFound.Avatar &&
        !candidateFound.Avatar.toLowerCase().includes(".jpg")
      ) {
        Avatar = await Photo_Files.findOne({
          _id: candidateFound.Avatar,
        });
      }

      if (Avatar) {
        candidateFound.Avatar = "";

        await Photo_chunks.deleteMany({
          files_id: Avatar._id,
        });
        await Avatar.deleteOne();
      }

      if (req.file) {
        candidateFound.Avatar = req.file.id;
      }
      candidateFound.Location = location;
      candidateFound.save();
      return res.json({
        // Token: token,
        candidateEmail: candidateFound.Email,
        GenerateToken: generatetoken,
        logged: true,
      });
      // return res.json({ msg: "Candidate already exists" });
    }
    const candidateData = {
      First_name: candidateInfo.First_name,
      Last_name: candidateInfo.Last_name,
      Email: candidateInfo.Email,
      Phone: candidateInfo.Phone,
      Location: location,
      GenerateToken: generatetoken, // Save the generated token in the candidate data
    };
    if (req.file) {
      candidateData.Avatar = req.file.id;
    }

    const savedCandidate = await candidateService.saveCandidate(candidateData);

    const token = await candidateAuthService.generatingJWT(candidateInfo.Email);
    if (token) {
      const candidateEmail = savedCandidate.Email;

      return res.json({
        Token: token,
        candidateEmail: candidateEmail,
        GenerateToken: generatetoken,
        logged: true,
      });
    }
  } catch (error) {
    res.status(400).send("There was an error", error);
  }
};

exports.getAvatar = async (req, res) => {
  const { candidateEmail } = req.body;
  console.log({ candidateEmail });
  try {
    const candidate = await candidateService.findCandidate(candidateEmail);

    if (!candidate) {
      console.log("Candidate not found");
      return null;
    }

    let Avatar = "";
    if (candidate.Avatar && !candidate.Avatar.toLowerCase().includes(".jpg")) {
      Avatar = await Photo_Files.findOne({
        _id: candidate.Avatar,
      });
    }

    if (Avatar) {
      candidate.Avatar = "";

      let imgChunks = await Photo_chunks.find({
        files_id: Avatar._id,
      });

      imgChunks.map((chunk) => {
        candidate.Avatar += chunk.data.toString("base64");
      });
    }

    res.json({
      avatar: candidate.Avatar,
      first_name: candidate.First_name,
      second_name: candidate.Last_name,
      candidateId: candidate._id,
      status: candidate.status,
      email: candidate.Email,
      phone: candidate.Phone,
    });
  } catch (error) {
    console.error("Error fetching candidate:", error);
    throw error; // You may want to handle or log the error accordingly
  }
};

exports.getEmail = async (req, res) => {
  let token = req.cookies.candidatecookie;

  jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }

    try {
      res.status(201).json({ email: decoded.email });
    } catch (err) {
      res.status(500).json({ error: "Internal Server Error" });
    }
  });
};

exports.getCandidate = async (req, res) => {
  try {
    const candidate = await Candidate.findById(req.params.id).populate(
      "job_position"
    );

    if (!candidate) {
      console.log("Candidate not found");
      return null;
    }

    let Avatar = "";
    if (candidate.Avatar && !candidate.Avatar.toLowerCase().includes(".jpg")) {
      Avatar = await Photo_Files.findOne({
        _id: candidate.Avatar,
      });
    }

    if (Avatar) {
      candidate.Avatar = "";

      let imgChunks = await Photo_chunks.find({
        files_id: Avatar._id,
      });

      imgChunks.map((chunk) => {
        candidate.Avatar += chunk.data.toString("base64");
      });
    }

    res.json({
      avatar: candidate.Avatar,
      first_name: candidate.First_name,
      second_name: candidate.Last_name,
      candidateId: candidate._id,
      status: candidate.status,
      email: candidate.Email,
      phone: candidate.Phone,
      location: candidate.location,
      job_position: candidate.job_position,
    });
  } catch (error) {
    console.error("Error fetching candidate:", error);
    throw error; // You may want to handle or log the error accordingly
  }
};

exports.talentData = (req, res) => {
  const token = req.params.token;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      const candidate = await candidateService.findCandidate(decoded.email);
      const candidateEvaluations = await CandidateEvaluation.findOne({
        candidate: decoded.email,
        projectId: decoded.projectId,
      });
      if (candidate) {
        // const candidateObject = candidate.toObject();
        if (candidate.Avatar) {
          let avatar = await Photo_Files.findOne({
            _id: candidate.Avatar,
          });
          candidate.Avatar = "";

          if (avatar) {
            let imgChunks = await Photo_chunks.find({
              files_id: avatar._id,
            });
            await Promise.all(
              imgChunks.map((chunk) => {
                candidate.Avatar += chunk.data.toString("base64");
              })
            );
          }
        }

        const year = candidateEvaluations.createdAt
          .getFullYear()
          .toString()
          .slice(-2);
        const lastThreeChars = candidateEvaluations._id.toString().slice(-3);
        const verificationNbr = "#" + year + "-" + lastThreeChars;

        return res.status(200).json({
          candidate: candidate,
          candidateEvaluations,
          verificationNbr,
          imgPath: decoded.imgPath,
          message: "Candidate found",
        });
      }
    } catch (error) {
      console.log({ error });
    }
  });
};

exports.searchCandidates = async (req, res) => {
  try {
    let candidatesFound = await candidateService.searchCandidates(
      validator.escape(req.query.firstLetter)
    );
    res.json({ candidatesFound });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};
exports.getCandidates = async (req, res) => {
  try {
    let candidates = await Candidate.find({}).populate("company");
    res.json({ candidates });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};
exports.deleteCandidate = async (req, res) => {
  try {
    let candidate = await Candidate.findByIdAndDelete(req.params.id);
    res.json({ candidate });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};

exports.getCandidateWithCompany = async (req, res) => {
  try {
    let candidateWithCompany = await candidateService.getCandidateWithCompany(
      validator.escape(req.params.candidateId)
    );
    res.json({ candidateWithCompany });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};

exports.switchStatus = async (req, res) => {
  const candidateId = req.params.candidateId;

  try {
    let candidate = await CandidateEvaluation.findOne({ _id: candidateId });

    if (candidate.status === "active") {
      candidate.status = "archived";
    } else {
      candidate.status = "active";
    }
    await candidate.save();

    res.json({ candidate });
  } catch (error) {
    res.status(400).send("There Was an error");
  }
};

const getCandidateInterpretations = async (email, projectId) => {
  const candidateEvaluation = await CandidateEvaluation.findOne({
    candidate: email,
    projectId: projectId,
  });
  // let score = 0;
  let candidateInterpretations = [];

  await Promise.all(
    candidateEvaluation?.results.map(async (result) => {
      let score = 0;
      score = result.rangesPoint
        ? (result.rangesPoint * 100) / (result.quesionsNbr * 5)
        : (result.totalPoints * 100) / result.quesionsNbr;
      const interpretation = await Interpretation.findOne({
        assessmentName: result.assessmentName,
      });

      if (interpretation !== null) {
        interpretation.interpretations.map((intercept) => {
          if (score >= intercept.range[0] && score <= intercept.range[1]) {
            candidateInterpretations.push({
              assessmentName: result.assessmentName,
              title: intercept.title,
              description: intercept.description,
            });
          }
        });
      }
    })
  );
  return candidateInterpretations;
};

exports.getCandidateInfo = async (req, res) => {
  const { email, projectId } = req.query;
  try {
    let interpretations = [];
    const CandidateInvitation = await Invitation.findOne({
      projectId: projectId,
      Email: email,
    });
    interpretations = await getCandidateInterpretations(email, projectId);
    const candidateScore = await CandidateEvaluation.findOne({
      candidate: email,
      projectId: projectId,
    });

    let project = await companyProject.findOne({ _id: projectId });
    let projectName = `${project?.jobTitle} - ${project?.seniority} - ${project?.name}`;
    console.log({ project });
    console.log("CANDIDATES SCORES ARE HERE1");
    console.log(candidateScore);
    console.log("CANDIDATES SCORS ARE HERE1");
    const candidateInfo = await Candidate.findOne({ Email: email });

    const candidateRating = await CandidateRating.findOne({
      candidateEmail: email,
      projectId: projectId,
    });
    res.status(200).send({
      CandidateInvitation,
      interpretations,
      candidateInfo,
      candidateScore,
      candidateRating,
      projectName,
    });
  } catch (error) {
    res.status(400).send("There was an error");
  }
};
calculateScore = (results) => {
  let score = 0;
  results?.forEach((element) => {
    if (element.rangesPoint || element.totalPoints) {
      score += element.rangesPoint
        ? (element.rangesPoint * 100) / (element.quesionsNbr * 5)
        : (element.totalPoints * 100) / element.quesionsNbr;
    }
  });

  const averageScore = score / results?.length;
  const roundedScore = averageScore.toFixed();
  return roundedScore;
};

calculateTime = (candidateScore) => {
  let minutes = Math.floor(candidateScore.candidateTime / (60 * 1000));
  if (minutes > 59) minutes = 59;

  let seconds = candidateScore.candidateTime % (60 * 1000);
  if (seconds > 59) seconds = 59;

  minutes = minutes.toString().padStart(2, "0");

  if (seconds < 10) {
    seconds = "0" + seconds.toString();
  } else {
    seconds = seconds.toString();
  }

  return `${minutes} min ${seconds} sec`;
};

exports.candidateReport = async (req, res) => {
  const { email, projectId } = req.query;

  try {
    // Read the EJS template file
    const candidateInfo = await Candidate.findOne({ Email: email });
    const project = await companyProject.findOne({ _id: projectId });

    const company = await Company.findOne({ name: project.company_name });

    if (company.logo) {
      let logo = await Photo_Files.findOne({
        _id: company.logo,
      });
      company.logo="";
      if (logo) {
        let imgChunks = await Photo_chunks.find({
          files_id: logo._id,
        });
        await Promise.all(
          imgChunks.map((chunk) => {
            company.logo += chunk.data.toString("base64");
          })
        );
      }else{
        //default logo
      company.logo="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";
     
      }
    }

    const candidateScore = await CandidateEvaluation.findOne({
      candidate: email,
      projectId: projectId,
    });

    const score = await calculateScore(candidateScore.results);

    const time = await calculateTime(candidateScore);

    const dateObject = new Date(candidateScore.createdAt);

    // Get the date components
    const year = dateObject.getFullYear();
    const month = dateObject.getMonth() + 1; // Month is zero-based, so add 1
    const day = dateObject.getDate();

    // Format the date
    const formattedDate = `${year} - ${month < 10 ? "0" + month : month} - ${
      day < 10 ? "0" + day : day
    }`;

    let interpretations = await getCandidateInterpretations(email, projectId);

    const ejsTemplate = fs.readFileSync(
      path.join(__dirname, "../views/CandidateReport.ejs"),
      "utf-8"
    );

    const customResults = candidateScore.results
      .filter((result) => result.customResults) // Filter only results with customResults
      .flatMap((result) => result.customResults); // Flatten the array of custom results

    const results = candidateScore.results.filter(
      (result) => !result.customResults
    ); // Filter only results with customResults

    // Render the EJS template to HTML
    const htmlContent = ejs.render(ejsTemplate, {
      data: {
        fullName: candidateInfo.First_name + " " + candidateInfo.Last_name,
        email: candidateInfo.Email,
        interpretations,
        candidateScore,
        results,
        score,
        customResults,
        time,
        logo: company.logo,
        company: company.name,
        datePassed: formattedDate,
        project: `${project?.jobTitle} - ${project?.seniority} - ${project?.name}`,
      },
      /* data to pass to the template */
    });

    // Launch Puppeteer
    const browser = await puppeteer.launch({
      headless: "new",
     // timeout: 60000, // increase timeout
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();

    // Set the HTML content of the page
    await page.setContent(htmlContent);

    // Generate PDF
    const pdfBuffer = await page.pdf({ format: "A4", printBackground: true });

    // Send PDF as response
    res.contentType("application/pdf");
    res.send(pdfBuffer);

    // Close the browser
    await browser.close();
  } catch (error) {
    console.error("Error generating PDF:", error);
    res.status(500).send("Internal Server Error");
  }
};

exports.getCandidateStatus = async (req, res) => {
  const { projectID, candidateID } = req.body;
  console.log(req.body);

  try {
    const Allcandidates = await CandidateEvaluation.findOne({
      candidate: candidateID,
      projectId: projectID,
    }); // Fetch all candidates
    console.log(Allcandidates);
    res.status(200).send({
      Allcandidates,
    });
  } catch (error) {
    console.error("Error updating candidate status:", error);
    res.status(500).json({ message: "Server error", error });
  }
};

exports.updateCandidateStatus = async (req, res) => {
  const { status, projectID, candidateID } = req.body;
  console.log(req.body);
  /*
  if (!mongoose.Types.ObjectId.isValid(prjectID))
    return res.status(404).send(`No project with id: ${prjectID}`);*/
  /*
  if (!mongoose.Types.ObjectId.isValid(candidateID))
    return res.status(404).send(`No candidate with id: ${candidateID}`);*/
  // Validate status
  const allowedStatuses = ["pending", "accepted", "rejected"];
  if (!allowedStatuses.includes(status)) {
    return res.status(400).json({ message: "Invalid status value." });
  }
  try {
    // const Allcandidates = await CandidateEvaluation.find({candidate:candidateID, projectId:projectID}); // Fetch all candidates
    // console.log(Allcandidates);

    //    const candidate = await CandidateEvaluation.findOne({candidate:candidateID,projectId:prjectID});
    const updatedCandidate = await CandidateEvaluation.findOneAndUpdate(
      { candidate: candidateID, projectId: projectID }, // Ensure correct filtering
      { status },
      { new: true }
    );

    if (!updatedCandidate) {
      // Check if updatedCandidate is null
      return res
        .status(404)
        .json({ message: "Candidate not found for the given project." });
    } else {
      console.log(updatedCandidate);
      console.log("Update successful");
      return res.status(200).json({
        message: "Candidate status updated successfully",
        updatedCandidate,
      });
    }
  } catch (error) {
    console.error("Error updating candidate status:", error);
    res.status(500).json({ message: "Server error", error });
  }
};

exports.exitTab = async (req, res) => {
  const { id } = req.params;

  try {
    const candidate = await CandidateEvaluation.findById(id);
    candidate.exitCount++;

    if (!candidate.exitedTab) {
      candidate.exitedTab = true;
    }

    const updatedCandidate = await candidate.save(); // Use save() on the instance
    res.json(updatedCandidate);
  } catch (error) {
    console.error("Error generating change window:", error);
    res.status(500).send("Internal Server Error");
  }
};
