const mongoose = require("mongoose");

const Score = mongoose.Schema({
  talentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Candidate",
  },
  assessmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "AllAssessments",
  },
  score: {
    type: Number,
    default: 0,
  },
  //   answers: {
  //     type: Array,
  //   },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const TalentScore = mongoose.model("TalentScore", Score);

module.exports = TalentScore;
