const mongoose = require("mongoose");

const interpretationSchema = new mongoose.Schema({
  assessmentName: {
    type: String,
    required: true,
  },
  interpretations: [
    {
      range: [0, 33],
      title: {
        type: String,
        // required: true,
      },
      description: {
        type: String,
        // required: true,
      },
    },
    {
      range: [34, 66],
      title: {
        type: String,
        // required: true,
      },
      description: {
        type: String,
        // required: true,
      },
    },
    {
      range: [67, 100],
      title: {
        type: String,
        // required: true,
      },
      description: {
        type: String,
        // required: true,
      },
    },
  ],
});

const interpretation = mongoose.model("interpretation", interpretationSchema);

module.exports = interpretation;
