// routes/experienceRoutes.js
const express = require("express");
const router = express.Router();
const {
    addEducation,
    getEducationByCandidate,
    getEducationById,
    updateEducationById,
    deleteEducationById,
} = require("../controllers/EducationController");

// Create a new education
router.post("/", addEducation);

// Get all education for a user
router.get("/", getEducationByCandidate);

// Get education by ID
router.get("/:id", getEducationById);

// Update an education by ID
router.put("/:id", updateEducationById);

// Delete an education by ID
router.delete("/:id", deleteEducationById);

module.exports = router;
