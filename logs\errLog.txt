20232009	00:20:56	0dc052a0-e0de-4093-9a78-b27c5efc922d	Error: Not allowed by CORS
2023/21/09	00:21:34	160f592c-d14b-4d54-a380-653bb0babc1a	Error: Not allowed by CORS
2023/21/09	00:21:36	871ca20e-a9b9-4697-a7d7-2d2f9a7cec15	Error: Not allowed by CORS
2023-22-09	00:22:48	3552ae72-b467-4ca4-9492-b0399f84bf96	Error: Not allowed by CORS
2023-03-09	00:24:07	c2d4a87a-1fe2-449e-8f1b-6dc097f714b2	Error: Not allowed by CORS
2023-03-09	03:03:57	8e16683d-6061-442f-a42b-907dd0e2a3df	SyntaxError: Unexpected token } in JSON at position 18
2023-03-09	14:05:30	dc7611e1-1f49-437b-8193-9a3d749ef869	SyntaxError: Unexpected token , in JSON at position 58
2023-03-09	14:14:26	0a1a8555-64e0-4a23-be13-38cf81911ce0	SyntaxError: Unexpected token , in JSON at position 58
2023-03-09	19:05:49	9073c8d7-f2d9-4333-8b79-1569ace7bea0	Error: Not allowed by CORS
2023-03-09	19:05:59	7ba7ad23-0b8b-4457-aea9-03439fc9f8da	Error: Not allowed by CORS
2023-03-09	19:06:25	f5e57e41-8923-4563-9928-11f299170560	Error: Not allowed by CORS
2023-03-09	19:06:45	afb8e190-8986-4aa8-b528-224d87b271bb	Error: Not allowed by CORS
2023-03-09	19:06:45	f394a92f-524e-4fa9-b981-22a03dba1b54	Error: Not allowed by CORS
2023-03-09	19:06:45	7e9efca7-d02a-450e-b183-f504773aadc7	Error: Not allowed by CORS
2023-03-09	19:14:14	76230d9c-a1f4-40e5-8a41-48909c7f9803	Error: Not allowed by CORS
2023-03-09	19:34:27	43f19cec-d148-4789-89de-2e543d529a1b	Error: Not allowed by CORS
2023-03-09	19:35:37	fd9127fb-8cb7-416c-9eca-1e8ff174c08b	Error: Not allowed by CORS
2023-03-09	19:35:43	ce127a2a-b880-4635-83fc-96fdddb1ed6d	Error: Not allowed by CORS
2023-03-09	19:35:43	a6716dc5-f186-4f1c-9882-843c2fa9d79b	Error: Not allowed by CORS
2023-03-09	19:35:54	bb5c3706-888a-489f-b986-d92a6d9fbec0	Error: Not allowed by CORS
2023-03-09	19:36:02	049f658a-9166-4f2e-94ef-5a88c0f87918	Error: Not allowed by CORS
2023-03-09	19:40:34	f84773ff-f35c-4a1f-9fdc-ae10b7b39672	Error: Not allowed by CORS
2023-03-09	19:40:44	07631324-dc95-4923-a2be-90bdb2a68903	Error: Not allowed by CORS
2023-03-09	19:40:44	b2e969b2-34c6-4730-beab-84c9636ea37c	Error: Not allowed by CORS
2023-03-09	19:46:11	4d59f5a9-b58b-4b8b-99c1-c54ff09bd529	Error: Not allowed by CORS
2023-03-09	19:46:11	87b40881-591b-411b-99d0-054958d15726	Error: Not allowed by CORS
2023-03-09	19:46:11	f040621a-0959-4e2c-b262-d03a6cf7717f	Error: Not allowed by CORS
2023-03-09	19:46:11	4262ade6-7110-4523-9b8d-fdfc2a82ebbe	Error: Not allowed by CORS
2023-03-09	19:46:15	1a912326-e783-4b12-af6d-1742b91166bc	Error: Not allowed by CORS
2023-03-09	19:46:15	8dbb4504-4c8d-4453-bbb8-027d68735f50	Error: Not allowed by CORS
2023-03-09	19:46:44	0f588d3f-441b-425e-ad42-ae7fa9a94b0f	Error: Not allowed by CORS
2023-03-09	19:46:48	30ad3c4d-8ee4-4866-b285-a3b3839c8f09	Error: Not allowed by CORS
2023-03-09	19:57:05	bcfe30ac-65b4-4062-bdf3-786be57c4c1b	Error: Not allowed by CORS
2023-03-09	19:57:21	efe6740b-2210-4400-96bf-03e49df50d0b	Error: Not allowed by CORS
2023-03-09	19:58:41	8fcc288b-380f-407a-9c7a-0ed12565488a	Error: Not allowed by CORS
2023-03-09	19:58:58	df11589c-e203-4005-a296-b65ba05180eb	Error: Not allowed by CORS
2023-03-09	19:59:05	60ebc758-6cd3-4699-93dd-11f07a4dd6a0	Error: Not allowed by CORS
2023-03-09	20:00:54	5c164735-4512-476e-a15e-f8a287dc2ccd	Error: Not allowed by CORS
2023-03-09	20:01:05	c04266cd-0c73-4bf5-8aa0-27ca2c3a4420	Error: Not allowed by CORS
2023-03-09	20:01:09	1b36c6c0-ee99-4317-9b96-918b1604a1eb	Error: Not allowed by CORS
2023-03-09	20:01:17	8135532e-5542-43a1-a379-aac235d11853	Error: Not allowed by CORS
2023-03-09	20:06:47	f2f918d9-9860-43da-b15b-9a8df9912960	Error: Not allowed by CORS
2023-03-09	20:07:21	efb38930-1f81-4480-a14c-7a16157709dd	Error: Not allowed by CORS
2023-03-09	20:09:57	40663c0c-2bb8-44a5-bf0c-94598ec19479	Error: Not allowed by CORS
2023-03-09	20:10:01	bbc40c7e-e1ef-4be1-bcf0-295d89b21098	Error: Not allowed by CORS
2023-03-09	20:11:00	70c60747-5499-45d8-bb3f-05cbe178ef65	Error: Not allowed by CORS
2023-03-09	20:11:00	0753a8e2-2bb5-41ac-b760-0a8c8c07d635	Error: Not allowed by CORS
2023-03-09	20:11:03	23ce2b38-f745-4624-be9b-999e20282084	Error: Not allowed by CORS
2023-03-09	20:11:03	f1dd95b7-be27-4a8e-8424-ecce87ff98dd	Error: Not allowed by CORS
2023-03-09	20:11:40	a9430143-9135-4b4c-abc9-89e05e9c98f8	Error: Not allowed by CORS
2023-03-09	20:20:29	af453ae0-e30e-497f-b63d-effed7f528a3	Error: Not allowed by CORS
2023-03-09	20:21:29	2258607a-b368-4f24-891a-69b86a944d4d	Error: Not allowed by CORS
2023-03-09	20:21:36	7c6d3c9e-2728-4ce9-8841-cc28baf18b14	Error: Not allowed by CORS
2023-03-09	20:21:42	0dd8ff11-22f3-4d96-8c3d-b53ec19a4058	Error: Not allowed by CORS
2023-03-09	20:23:00	dbc28577-b436-4f94-b952-2916ca56ae82	Error: Not allowed by CORS
2023-03-09	20:23:13	501b712e-4a0d-4cf5-9394-286edccc439a	Error: Not allowed by CORS
2023-03-09	20:26:39	70cd5ca0-f86a-4075-96ca-513731cbb101	Error: Not allowed by CORS
2023-03-09	20:26:52	ac8f3c3d-b765-49c4-a35d-44bf7ead2ca3	Error: Not allowed by CORS
2023-03-09	20:26:57	9d8a05d1-7523-4002-9750-eaccd58213af	Error: Not allowed by CORS
2023-03-09	20:29:51	b86c30ba-6602-4f99-96ac-c751540c526d	Error: Not allowed by CORS
2023-03-09	20:29:55	e77517cf-3e90-4d0e-816a-466206e34be1	Error: Not allowed by CORS
2023-03-09	21:43:11	c7e40a67-6e84-49b0-bfa2-9840e4e06031	Error: Not allowed by CORS
2023-03-09	21:43:11	c362eab4-dea9-4319-b3de-d88280d5c1a5	Error: Not allowed by CORS
2023-03-23	12:38:24	54e68311-088f-4c19-85fc-0f2bf333c111	SyntaxError: Unexpected token { in JSON at position 16
2023-03-23	16:05:49	9fc9d67f-09ca-475a-b599-9fafc6e0c227	SyntaxError: Unexpected token } in JSON at position 763
2023-04-03	10:00:24	d5ff6ddc-1e25-4bb7-b1a0-47a538cf26c8	Error: Not authenticated
2023-04-03	10:17:02	c2dda202-6511-4595-8318-0e4b03c174e4	Error: Validation failed!
2023-04-03	10:17:46	8a6977a9-3b9e-4618-bd7e-8fa89eb48c4c	Error: Validation failed!
2023-04-03	10:35:36	3fe8ae7b-27cf-4c5f-9f6e-c7119321ceae	TypeError: ReturnResponse is not a function
2023-04-03	10:37:14	752dad60-4466-448e-bbdd-dd7a716bb1bc	Error: Validation failed!
2023-04-03	10:37:17	e4ec6a69-b29c-40cf-b82e-a6b95b4cc7af	Error: Validation failed!
2023-04-03	10:49:47	a8f52752-a578-40ae-ba41-3b6f1860f819	Error: Validation failed!
2023-04-03	10:50:48	765070a5-965a-4d1b-8a0f-8b3de268b7f4	Error: Validation failed!
2023-04-03	11:25:50	98f6fad1-7123-4ad3-9ac3-782a056d2fa3	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:29:14	0c70f31c-084e-42ae-be00-094f2bbfc87d	Error: Validation failed!
2023-04-03	11:31:16	d66412be-0d07-4226-bb4e-0a1c3306848a	Error: Validation failed!
2023-04-03	11:31:58	f76ced7a-6d9b-497d-843d-70886ec5d667	Error: Validation failed!
2023-04-03	11:32:00	a39190e8-a75d-4257-894a-7c879d684287	Error: Validation failed!
2023-04-03	11:33:20	afc72769-26e9-434b-a8d0-d3634661f6fb	Error: Validation failed!
2023-04-03	11:33:39	2f1153ec-0051-40de-aeab-3fd68db03577	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:33:42	b8169491-6d70-4019-aba9-923f68b06722	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:34:18	de9878c4-a442-4970-a692-df2d36c02fad	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:37:53	70918d6b-4561-4475-8349-ae5c8879aeb9	SyntaxError: Unexpected token } in JSON at position 109
2023-04-03	11:38:00	6c243ec2-9eb4-4e8e-beb1-0d68b746a831	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:39:02	267cab7e-1ef9-4ff3-ba2a-d0d28be5aab3	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:41:59	f68a31c7-525a-4677-a271-8526c6de05f0	Error: data and salt arguments required
2023-04-03	11:43:27	321447e0-6ef6-4ab7-8ab3-d2110765b0d7	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	11:49:56	556a3fa1-7773-452e-bc3b-0f3793be4825	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	12:00:02	d810dfc1-ac41-455a-8253-0db37d3fd3aa	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	12:00:04	4bac4ed9-eb86-462b-8f4f-8428f29da1e5	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-03	12:05:04	9c172e66-45a4-47d9-a4de-950aec0bfa91	TypeError: Cannot read properties of undefined (reading 'split')
2023-04-03	12:31:50	722b86a6-54bd-4d0d-9001-8a96ae81ac67	Error: Cannot set headers after they are sent to the client
2023-04-13	02:54:19	5bf46a6a-e14e-4096-9c4a-b4437c5298b8	ValidationError: Company validation failed: name: Path `name` is required.
2023-04-13	02:54:53	93538b9a-854b-4532-a683-35bcfb06bef6	MongoServerError: E11000 duplicate key error collection: Go_Platform.companies index: email_1 dup key: { email: "<EMAIL>" }
2023-04-13	03:22:53	b6eff9ad-8f1a-4368-9c9d-9de5651a7cae	Error: data and hash arguments required
2023-04-13	03:23:06	3c483c5f-f0be-4bbf-9a86-3bd4f9485358	Error: data and hash arguments required
2023-04-13	04:56:19	755d6869-e7fe-45ad-bf20-8ee0305ff63e	Error: Cannot set headers after they are sent to the client
2023-04-13	04:56:24	956bf39a-ec3a-4e21-9aba-c62d2a0c7e95	Error: Cannot set headers after they are sent to the client
2023-04-13	04:56:25	78b746ac-bf91-4f3a-9510-d86142104787	Error: Cannot set headers after they are sent to the client
2023-04-13	04:59:57	bccaca70-9b92-4189-870b-ae087cae33f3	CastError: Cast to ObjectId failed for value "all-Companies" (type string) at path "_id" for model "Company"
2023-04-13	09:52:16	44c09903-870f-4b1f-89de-823b17cc83b3	ValidationError: Quiz validation failed: created_by: Path `created_by` is required.
2023-04-13	09:54:04	e371275f-5409-4d4d-b980-64ed67b0559f	ValidationError: Quiz validation failed: created_by: Path `created_by` is required.
2023-04-13	09:54:17	bc511cdf-8fea-40f7-9d65-e7be09d414ae	SyntaxError: Unexpected token d in JSON at position 2227
2023-04-13	09:57:11	f1e6d912-caf1-4c48-9185-08b27768ee20	SyntaxError: Unexpected token O in JSON at position 2219
2023-04-13	09:57:26	84659d8e-6758-4483-8c19-aa55fefa3d12	ValidationError: Quiz validation failed: created_by: Path `created_by` is required.
2023-04-13	10:08:01	a073244d-fa72-4493-95db-2ae4057fa28c	TypeError: Cannot read properties of undefined (reading 'toString')
2023-04-13	10:18:40	8fd9e0f3-e957-478d-ba2b-ea9cc82f10b9	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:20:53	c0bd9647-f0f4-4090-92e0-e712a38d4d8a	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:21:01	741c80ec-7f10-41cc-a644-890b4d7486c8	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:23:19	4d9bd1d5-a24c-4d0a-9f2e-c5e95c952bc6	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:23:21	95661a85-cab2-47ac-9f5f-aca850652abb	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:25:31	e82e868e-517f-431e-9614-91e8ed720f5f	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:07	f10834b5-8be8-40d4-bf9f-a8e25d539f36	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:09	3a7f485c-44cf-46f0-a59d-f82f2140b2e8	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:09	c7836e9f-d888-41ea-ba5d-f7652fdad222	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:10	1ce65fdc-adac-4e97-9f5a-2430b81dad5e	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:10	d1c33b57-93db-4538-bd00-532aadab94bc	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:28:58	46c51153-3fce-42e4-882d-b4ec54745a5a	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:29:00	a14ed4e0-07da-48e3-8e9d-c55a70070fa8	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:40:23	a31ec6a9-44d6-4fc2-82b3-228c03629595	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:41:13	f5b273a3-ab17-4f00-973b-78640aacd72d	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:41:45	7abb5c77-69e9-44e1-80fa-3f4cc566fafc	TypeError: Cannot read properties of null (reading 'created_by')
2023-04-13	10:48:09	c326c0e1-f931-4ebc-ab1b-badc71bc8aea	Error: Cannot set headers after they are sent to the client
2023-04-13	13:01:58	737688e9-349c-4931-99fc-366e077f5ad7	ValidationError: Report validation failed: candidateId: Path `candidateId` is required.
2023-04-13	23:19:34	3398c706-3f06-4421-8abd-27eeb05f692d	MongoServerError: E11000 duplicate key error collection: Go_Platform.companies index: email_1 dup key: { email: "<EMAIL>" }
2023-04-13	23:32:45	d62e4693-63d2-48ee-9752-ffba18bdd585	Error: Cannot set headers after they are sent to the client
2023-04-13	23:36:18	96767b92-c21f-4203-8d3e-316b945f3f8f	Error: Cannot set headers after they are sent to the client
2023-04-24	21:46:43	0a1841e3-0c07-4871-817f-9ec6a0f88ef1	SyntaxError: Unexpected token { in JSON at position 4
2023-04-27	17:51:52	c3ff93cb-83af-4799-b178-93ab925e10c2	SyntaxError: Unexpected token , in JSON at position 34
2023-04-27	17:56:12	8d4a4909-2499-498d-9131-6c1850f64b20	SyntaxError: Unexpected token 
 in JSON at position 75
2023-04-27	18:45:36	8dfe24a8-dcd4-48de-9229-24b82e4bcc40	SyntaxError: Unexpected token } in JSON at position 64
2023-05-01	10:34:29	92c8dee4-e523-4cf6-bc82-c1ecceac6b9a	SyntaxError: Unexpected string in JSON at position 45
2023-05-11	16:51:17	a077f746-5c8a-4ac6-a584-e1039e48f4d4	Error: Cannot set headers after they are sent to the client
2023-05-11	16:52:07	b54e348b-9efd-421e-9cfa-4b8b81b1f250	Error: Cannot set headers after they are sent to the client
2023-05-11	16:53:55	89befc9c-1938-412f-9190-fb9bc527e696	SyntaxError: Unexpected end of JSON input
2023-05-11	16:54:28	f337caab-d45d-471a-b314-730338286080	TypeError: Cannot read properties of null (reading 'answers')
2023-05-11	16:54:58	6d2b7c86-2c3f-4b23-b122-1fe1bec527b6	TypeError: Cannot read properties of null (reading 'answers')
2023-05-11	18:53:41	60f69691-7821-44ee-b77f-b3d7c2d96ad4	SyntaxError: Unexpected token } in JSON at position 2185
2023-05-11	18:54:06	57a3e2b9-43e2-4318-a240-7609aaae12c4	ValidationError: Quiz validation failed: created_by: Path `created_by` is required.
2023-05-11	18:58:28	749add97-07cb-4c34-9c02-a8feaabfe917	SyntaxError: Unexpected token ] in JSON at position 1831
2023-05-11	19:24:14	37a57ee2-35a1-4f4c-9b5d-37fc973c7315	MongoServerError: E11000 duplicate key error collection: Go_Platform.quizzes index: name_1 dup key: { name: "Communication Skills Quiz from postman created_by test3" }
2023-05-11	19:37:08	a0c82d91-8d61-4b6d-b45a-427974e11932	MongoServerError: E11000 duplicate key error collection: Go_Platform.quizzes index: name_1 dup key: { name: "Communication Skills Quiz from postman created_by test 12" }
2023-05-11	19:46:34	4e537eb3-bc22-4cd0-a6f7-cf57d8af00b7	MongoServerError: E11000 duplicate key error collection: Go_Platform.quizzes index: name_1 dup key: { name: "Communication Skills Quiz from postman created_by test 14" }
2023-05-18	15:35:14	13815d3a-d1c8-4917-8c80-24ea5c58540e	Error: Not allowed by CORS
2023-05-18	15:35:46	87c8fd80-1977-4ecb-a40d-074dcbff2111	Error: Not allowed by CORS
2023-05-18	15:36:35	89765d06-f7b8-44e1-a780-f630b7579e90	Error: Not allowed by CORS
2023-05-18	15:36:43	728f8f49-5225-4e89-bb6a-c3f7c529ad40	Error: Not allowed by CORS
2023-05-18	15:37:26	2d258ae7-6993-4a09-aed6-f260149d9bb9	Error: Not allowed by CORS
2023-05-18	15:37:32	e6d7b045-ebc6-4d58-93ed-ac3b48dcd59f	Error: Not allowed by CORS
2023-05-18	15:38:32	76a0b517-3035-43fd-afc3-6ff79d78d978	Error: Not allowed by CORS
2023-05-18	15:38:38	8c858d71-d59e-47ba-950c-090a3cba8447	Error: Not allowed by CORS
2023-05-18	15:44:14	4cffc383-5d49-4fc5-91a3-92c412f7c86c	Error: Not allowed by CORS
2023-05-18	15:44:15	332e7b98-f713-4702-b43c-65111058b382	Error: Not allowed by CORS
2023-05-18	15:44:21	800d09e6-8c9c-4af1-80c8-9da3cdf52a69	Error: Not allowed by CORS
2023-05-18	15:45:06	7cdc3f4b-dcd3-459a-a0ab-79591a60ede0	Error: Not allowed by CORS
2023-05-18	15:45:13	d74c4953-e793-49b0-950c-eaeba42c2dea	Error: Not allowed by CORS
2023-05-18	15:51:54	c6728da8-d35e-4375-b6e8-5ed687fa8759	Error: Not allowed by CORS
2023-05-18	15:57:14	7927f802-2873-4791-8bc4-414d22d34d11	Error: Not allowed by CORS
2023-05-18	15:59:30	3e718f59-9921-4a5b-b933-bbf676a58658	Error: Not allowed by CORS
2023-05-18	16:03:16	0c91ca90-24cd-485a-b0bf-b83e6f63e707	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-18	16:58:42	b0266c2f-d76d-4afa-8351-ca81ef34c373	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-18	16:58:47	4d6bed59-cc9a-4753-8ef3-b6a6cdd11319	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-18	17:01:16	4c72d731-c2fd-43c8-b21c-67e9af5b899e	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-18	17:01:22	f72e26ea-7e12-41b7-9f6c-384c80e2cf4f	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-18	17:03:04	428adfcc-85fa-442b-8eb7-dc80c0f96e94	TypeError: Cannot read properties of null (reading 'created_by')
2023-05-18	17:03:34	9b6f7405-e13a-48e6-92e5-8a3cd211b71f	Error: Cannot set headers after they are sent to the client
2023-05-18	17:04:16	2ea1e074-7d93-45e2-9a64-3fd4cb64a24a	Error: Cannot set headers after they are sent to the client
2023-05-18	17:05:44	9e90e037-c737-42ca-8a60-958802aac15b	Error: Cannot set headers after they are sent to the client
2023-05-18	17:05:46	4a509a3c-47dc-450f-a025-7c4707c58dc1	Error: Cannot set headers after they are sent to the client
2023-05-18	17:10:44	b0c835a1-b616-4363-9b3a-051dff06aa2c	CastError: Cast to ObjectId failed for value "${quizId}" (type string) at path "_id" for model "Quiz"
2023-05-20	06:57:46	3a027b3f-3066-4780-ad10-e35b87610f57	ValidationError: Report validation failed: adminId: Path `adminId` is required., companyId: Path `companyId` is required., candidateId: Path `candidateId` is required.
2023-05-20	06:58:22	e8f19182-4250-49c5-8e2a-0f8abba0cbab	TypeError: Cannot read properties of null (reading 'is_published')
2023-05-20	06:58:44	bdcaae01-0747-434a-a65d-03cfda74522b	Error: Cannot set headers after they are sent to the client
2023-05-20	06:58:55	998d8053-bf74-4d0c-8559-06a0953c99ab	Error: Cannot set headers after they are sent to the client
2023-05-20	06:59:17	b8480eb2-9418-4e86-9f4a-ce555835e694	MongoServerError: E11000 duplicate key error collection: Go_Platform.admins index: email_1 dup key: { email: "<EMAIL>" }
2023-05-20	06:59:58	8a20d712-ffee-4b69-a836-7f4126739c91	Error: Cannot set headers after they are sent to the client
2023-05-20	07:00:01	f41d73c0-7a53-4294-8789-f02e51441e69	Error: Cannot set headers after they are sent to the client
2023-05-20	07:02:57	de6fa9ac-2c97-492b-a1f3-6c584fc8d6a7	Error: Cannot set headers after they are sent to the client
2023-05-20	07:03:38	d2cd0238-0e4f-4d9b-ab68-90a0f6d3b8a2	ValidationError: Report validation failed: adminId: Path `adminId` is required., companyId: Path `companyId` is required., candidateId: Path `candidateId` is required.
2023-05-20	07:05:45	4f977563-f92e-4dc8-acea-7d5392d1386d	ValidationError: Report validation failed: adminId: Path `adminId` is required., companyId: Path `companyId` is required., candidateId: Path `candidateId` is required.
2023-05-25	06:44:06	94b167a0-f0f8-4f82-ad9a-2293346106d9	SyntaxError: Unexpected token a in JSON at position 21
2023-05-25	20:28:09	05e26a08-780a-4233-8370-7c2688af10ba	SyntaxError: Unexpected token h in JSON at position 46
2023-05-27	20:05:46	1a070bdf-4003-403d-8993-710d48fd4a9f	ValidationError: Report validation failed: candidateId: Cast to ObjectId failed for value "" (type string) at path "candidateId", companyId: Cast to ObjectId failed for value "" (type string) at path "companyId"
2023-05-27	20:06:04	1d9c32ff-21e7-4271-b023-70a549d35afb	ValidationError: Report validation failed: candidateId: Cast to ObjectId failed for value "646a529e00ec2f2c" (type string) at path "candidateId", companyId: Cast to ObjectId failed for value "646a529e00ec2f2c" (type string) at path "companyId"
2023-05-27	20:08:27	1aa777a8-3ee4-4c75-8b32-75d28861e5c8	SyntaxError: Unexpected string in JSON at position 183
2023-05-27	20:42:11	de4c2b25-f61e-44aa-b8ab-1e9e666bc606	TypeError: Cannot read properties of null (reading 'adminId')
2023-05-27	20:42:54	a9ac4402-5541-4232-9b3a-e959cb9b1c75	TypeError: Cannot read properties of null (reading 'adminId')
2023-05-27	20:50:17	ffb6d370-e932-4c9c-ba2c-f75a8c225657	Error: Cannot set headers after they are sent to the client
2023-05-27	21:10:07	907c4265-4a6b-457e-ac91-65b5250b6f82	CastError: Cast to ObjectId failed for value "Params" (type string) at path "_id" for model "Report"
2023-05-27	21:58:34	f715bc9f-c835-4584-9f07-ca5c619106b7	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:00:06	495be1fd-f0b4-4a0c-a882-014202ca0e78	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:13:33	bb52dce6-1968-49e0-b050-9d0c4fcf9991	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:53:00	db5b2f01-971f-485a-879a-ff7bdd25a449	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:53:09	64167e62-e486-488f-9b39-fca36a98a2c3	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:55:30	94dcf615-a6c3-4e17-96f7-65c4e9f95cfc	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:55:32	d61d6743-c326-478e-a7aa-c5943c143d14	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-27	22:55:58	5ea49a13-0ab7-49e1-b546-75cf6288574b	CastError: Cast to ObjectId failed for value "params" (type string) at path "_id" for model "Report"
2023-05-29	06:42:33	17084afa-9ec7-40b7-ad0c-24a687c86daf	ValidationError: Report validation failed: candidateEmail: Cast to ObjectId failed for value "<EMAIL>" (type string) at path "candidateEmail"
2023-05-29	07:02:58	b9506cf6-0420-40f2-8552-d65f70cda461	ValidationError: Report validation failed: candidateEmail: Cast to ObjectId failed for value "<EMAIL>" (type string) at path "candidateEmail"
2023-05-29	15:29:04	9162f2ae-8cc5-4fc3-a05a-e8e311afeace	ReferenceError: candidateId is not defined
2023-05-29	15:29:04	76c9948c-12db-407b-8a48-4823b212f60b	ReferenceError: candidateId is not defined
2023-05-29	15:30:53	0fe0059e-0e6a-48ff-bc37-461a15dcbd40	ReferenceError: candidateId is not defined
2023-05-29	15:32:02	4dedb5b7-919b-474e-8659-0f79b6f11a51	ReferenceError: candidateId is not defined
2023-05-29	15:32:04	4698ddd8-0370-40ca-bf40-56bbc1bb27a4	ReferenceError: candidateId is not defined
2023-05-29	15:32:22	1e3c355f-0c53-45a3-bf11-9ad78f22128d	ReferenceError: candidateId is not defined
2023-05-29	15:32:24	c066ca18-f580-4ca5-b3ef-06a5c40f640e	ReferenceError: candidateId is not defined
2023-05-29	17:15:17	d6ab5d64-965f-49a2-b873-d5fa3a0fb8fa	TypeError: Cannot read properties of null (reading 'candidateEmail')
2023-05-29	17:15:55	5df823f6-3390-4ec3-8d5c-eafa1880c616	ValidationError: Report validation failed: candidateEmail: Path `candidateEmail` is required.
2023-05-29	17:25:53	33a29bb5-3cb4-4c7f-8198-c16571b062ec	TypeError: Cannot read properties of null (reading 'candidateEmail')
2023-05-29	18:01:33	196ad0d1-d2ab-48aa-ac40-dd7937dfb936	TypeError: Cannot read properties of null (reading 'candidateEmail')
2023-05-29	18:01:59	1715e13e-84da-4a3b-9cf9-4a0e56862c61	ValidationError: Report validation failed: candidateEmail: Path `candidateEmail` is required.
2023-05-29	18:05:32	94a429f0-5df6-4deb-95bb-99fb52dff7f9	TypeError: Cannot read properties of null (reading 'candidateEmail')
2023-05-29	18:06:05	ce03c22a-c55c-4a68-bb24-0f841f8dd59b	ValidationError: Report validation failed: candidateEmail: Path `candidateEmail` is required.
2023-05-29	18:09:34	18976639-a7df-43d4-9058-028a8c70ce44	ValidationError: Report validation failed: candidateEmail: Path `candidateEmail` is required.
2023-05-29	18:22:16	62514c21-fe8d-4705-ae81-7e079e5e7d80	ValidationError: Report validation failed: candidateEmail: Path `candidateEmail` is required.
2023-05-29	20:17:54	d1b6cf90-85fc-41f7-ae14-3b97b6d5affa	SyntaxError: Unexpected token " in JSON at position 0
2023-05-29	20:18:55	dc3e2e29-d57e-4be7-9599-822d37701423	SyntaxError: Unexpected token } in JSON at position 189
2023-05-29	20:33:38	feed1e6a-ddb3-451c-94e7-b67d51833a7b	SyntaxError: Unexpected token } in JSON at position 128
2023-05-29	20:52:28	7231a00b-4d1c-4cf0-9a77-1e0aa890d218	SyntaxError: Unexpected string in JSON at position 188
2023-08-03	13:56:41	9759c611-91ec-4d2d-b45e-548592ccc721	Error: ENOENT: no such file or directory, open '../uploads/jobImgs/1691067401299.png'
2023-08-03	13:57:31	6caa6387-61d7-4b49-a78f-15a5d13efc4f	Error: ENOENT: no such file or directory, open '../uploads/jobImgs/1691067451042.png'
2023-08-03	13:57:50	fce3b8b7-ac51-4f22-8084-06af8f1ebff1	Error: ENOENT: no such file or directory, open '/uploads/jobImgs/1691067470640.png'
2023-08-05	06:37:38	7a29f946-1837-48da-b3b4-9b5325234604	MulterError: Unexpected field
2023-08-17	10:37:19	9d2590c7-dfb1-4893-afdd-6a8a31112717	TypeError: req.headers is not a function
2023-08-17	10:37:19	d14e09b5-a4a2-48d1-9aaa-72421143ed62	TypeError: req.headers is not a function
2023-08-23	13:48:33	ac910bdc-2c8d-412f-8b2c-d962bd415f5a	Error: Not allowed by CORS
2023-09-30	18:25:17	199fed96-9870-443a-bdf4-95d430aefe37	MulterError: Unexpected field
2023-10-12	13:04:55	e1612992-3c74-47da-ab96-a089cf914b9d	SyntaxError: Unexpected token , in JSON at position 4293
2023-10-20	06:10:33	e7743394-a8d6-4b72-bd3c-8bad004d38c1	SyntaxError: Unexpected token } in JSON at position 665
2023-10-20	06:18:44	82a11d99-f41f-40a3-a09e-ab364b6cf04c	SyntaxError: Unexpected token } in JSON at position 198
2023-10-23	15:37:22	410d96f2-afe9-4996-b6aa-e93d6a0ed2e8	SyntaxError: Unexpected token " in JSON at position 0
2023-10-23	15:38:38	af7e21c4-a755-4b97-aa5d-1f9ff14e7d04	SyntaxError: Unexpected token " in JSON at position 0
2023-10-23	16:40:37	28902d60-8d33-448e-9a0e-a77b3c042452	SyntaxError: Unexpected token : in JSON at position 32
2023-10-24	12:54:58	8fd111f0-aef3-43d5-9764-b98b0fc15103	SyntaxError: Unexpected token { in JSON at position 72
2023-10-24	19:45:11	96918450-684f-4ba7-bad2-9f681c50798a	PayloadTooLargeError: request entity too large
2023-12-17	16:07:43	4a81b522-c7fb-4bb6-9dac-f98df9df7300	ReferenceError: jwt is not defined
2023-12-23	23:09:31	d11a86a0-fec0-4c33-a002-e422d1f79b12	PayloadTooLargeError: request entity too large
2023-12-24	19:59:21	c9366a30-66dc-4586-8588-0f9fbadae16a	PayloadTooLargeError: request entity too large
2023-12-30	17:24:38	03ba228f-5006-4849-b379-4a714a9912fb	TypeError: res.redirectInvitation is not a function
2023-12-31	20:09:34	71506a12-1e8d-4293-b4ce-98e773a3bf26	SyntaxError: Unexpected token 
 in JSON at position 78
2023-12-31	20:49:07	43a1fdb5-02a0-4692-9eaf-4853c0b99e77	TypeError: Cannot read properties of undefined (reading 'role')
2023-12-31	20:49:07	fce1802a-379f-4ff9-afa1-ec17a2cf1c84	TypeError: Cannot read properties of undefined (reading 'role')
2023-12-31	20:49:53	acd1ee08-ca63-4219-a41e-89012ce635c9	TypeError: Cannot read properties of undefined (reading 'role')
2023-12-31	20:49:53	950617f2-5d97-45e5-83d5-b19a8a5673f0	TypeError: Cannot read properties of undefined (reading 'role')
2023-12-31	20:49:53	a1f1eac4-5ea4-4550-9018-650d5e3cc633	TypeError: Cannot read properties of undefined (reading 'role')
2023-12-31	20:53:44	d37d8b34-0608-43de-a713-040da8a5538a	TypeError: Cannot read properties of undefined (reading 'role')
2024-01-02	17:42:12	bc410fc1-cb05-4ea4-b069-cb29c0f02bef	MongoNetworkTimeoutError: connection timed out
2024-01-02	17:44:02	d60ac18a-b0fb-4a9f-9d19-c3e7d8585be5	MongoNetworkTimeoutError: connection timed out
2024-01-02	18:03:21	ab0f5e2b-0190-421f-bfcf-80fcce7fb721	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	1b64d923-eee6-4509-9265-3e8cf4ed2d15	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	9d00ff59-5d9f-416f-b117-654e8859385f	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	269203b2-4524-405a-a103-60b79d90a596	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	f7110f32-42df-4ebd-919b-6e7f256f30b7	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	ce9bf075-632d-481f-a865-f0b9f03ae159	MongoNetworkTimeoutError: connection timed out
2024-01-02	18:03:21	4a1fb623-4b53-4a60-9ec9-ee3cdcf61c06	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	253a5431-f1f9-48fb-ae5e-d50b9826080f	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 118 to 13.37.66.215:27017 closed"
2024-01-02	18:03:21	c3411fb0-95f7-4d07-97e8-2d5550a7de86	MongoNetworkError: connection 131 to 13.37.66.215:27017 closed
2024-01-02	18:19:52	4a9751a9-7ee7-4093-bd7e-627ee62085ec	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection 152 to 13.37.66.215:27017 timed out"
2024-01-02	18:36:59	b7f044e5-5775-4712-bc7a-2e7bc78640e1	MongoNetworkTimeoutError: connection timed out
2024-01-02	18:36:59	33a9ed99-26c5-473c-80e5-e57a96cda7d8	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection <monitor> to 13.37.66.215:27017 timed out"
2024-01-02	18:36:59	cb6da08a-e3cf-410f-9409-5414ace3b333	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection <monitor> to 13.37.66.215:27017 timed out"
2024-01-02	18:36:59	f1300430-8529-4558-8f94-9b8ed8490d51	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection <monitor> to 13.37.66.215:27017 timed out"
2024-01-02	18:36:59	60e18d6d-5468-42ca-a861-2b6ba5dd61b7	MongoPoolClearedError: Connection pool for ac-azc0gxp-shard-00-01.cbjmgau.mongodb.net:27017 was cleared because another operation failed with: "connection <monitor> to 13.37.66.215:27017 timed out"
2024-01-02	20:06:32	c9fe9ff0-8dae-48c8-9af5-67395480c96a	MongoNetworkTimeoutError: connection timed out
2024-01-02	20:29:10	47c08473-451d-4430-b2d6-41e1746af94a	MongoNetworkTimeoutError: connection 68 to 13.37.66.215:27017 timed out
2024-01-02	20:34:55	184817c6-1e97-4484-9e0a-06efe7d03a8c	MongoNetworkTimeoutError: connection timed out
2024-01-03	09:36:21	9af74e75-2c5c-49cd-ba94-fd0d9ff7af4e	MongoNetworkTimeoutError: connection timed out
2024-01-11	19:19:33	4f0974a1-383f-49dd-b970-04166390ee41	PayloadTooLargeError: request entity too large
2024-02-01	10:42:22	f360f979-324a-464f-8594-fc266441b8e3	PayloadTooLargeError: request entity too large
2024-02-15	14:37:55	069f2af1-42d0-4177-a8ad-de59ddfd6cbf	Error: Not allowed by CORS
2024-02-15	14:38:00	3e5980ec-347e-4362-a802-558751f54cdf	Error: Not allowed by CORS
2024-02-15	14:38:56	2f7def35-081f-41dc-9963-e06a6e9b1094	Error: Not allowed by CORS
2024-02-15	14:45:15	927773ff-891d-4237-ac44-917070d4f3b3	Error: Not allowed by CORS
2024-02-27	10:32:39	64e6a033-fab8-4eeb-9e74-1173a7f36508	MongoServerSelectionError: connection timed out
2024-02-27	10:32:47	b5aa4118-18cb-40fe-b7f2-f1f06307e8d0	MongoServerSelectionError: connection timed out
2024-02-27	10:33:03	57f757c1-b594-4e58-9305-1304c0ea1a4e	MongoServerSelectionError: connection timed out
2024-03-14	12:06:17	56f40042-d760-495c-8420-15bdb4ab3df7	SyntaxError: Unexpected token ] in JSON at position 1081
2024-03-14	12:47:47	e893a777-63b6-4fcf-a2c0-2edb878e9876	SyntaxError: Unexpected token ] in JSON at position 2835
2024-03-12	11:49:38	4eb16071-21d8-4ded-9751-3e78e858f9df	SyntaxError: Unexpected token ] in JSON at position 149
2024-03-31	15:44:11	e1d53915-2686-4f1f-9608-ef7d71af05f4	ReferenceError: bcrypt is not defined
2024-03-28	18:19:45	085083a6-48cf-444e-82fd-fee3d899c87a	TypeError: verifyToken is not a function
2024-04-03	17:35:57	aa8eaa65-5322-4473-aa3c-9ed064240d96	ReferenceError: jwt is not defined
2024-04-07	15:50:57	a0fc3940-d21e-43d0-9971-33173b528dfd	SyntaxError: Unexpected token } in JSON at position 496
2024-04-16	17:58:49	402104a2-664c-4513-942e-88cb505cd8cd	PayloadTooLargeError: request entity too large
2024-04-16	18:07:45	493a866b-a365-4d81-b3ec-d0f4a54bfc57	PayloadTooLargeError: request entity too large
2024-04-16	18:17:23	9f3aaf5a-831e-49e8-b466-0a67e7c02353	PayloadTooLargeError: request entity too large
2024-04-16	18:30:38	50dc70ed-fe64-4cfc-ac7c-bec8a3bc38e9	PayloadTooLargeError: request entity too large
2024-04-22	21:36:40	8d41a77f-1caf-4d96-972e-9874639bd8af	MongoServerError: E11000 duplicate key error collection: recruitable.admins index: email_1 dup key: { email: "<EMAIL>" }
2024-04-23	17:32:05	a6b8c3d2-96c1-400d-a679-0b3e76ec5bfd	ReferenceError: filename is not defined
2024-04-23	18:19:01	b6a61842-0476-4c8f-958b-32604964a52a	ReferenceError: updatedJSON is not defined
2024-05-16	20:21:55	dc402acb-64dd-4593-86d8-11fa032d1505	ReferenceError: jwt is not defined
2024-05-16	20:21:55	c14a0e18-0d36-46cb-8533-4dbdf882bf7c	ReferenceError: jwt is not defined
2024-05-16	20:22:03	f9689e9c-415f-4ba1-b93c-fef9031274f8	ReferenceError: jwt is not defined
2024-05-16	20:22:03	9bf54d6a-a70d-41c6-b86b-c7de923e2f4f	ReferenceError: jwt is not defined
2024-05-16	20:22:50	fd504da6-6c40-47b0-b5dc-1346da047dda	ReferenceError: jwt is not defined
2024-05-16	20:22:50	7c6fc96c-fb9f-44ee-b001-a460f0825d36	ReferenceError: jwt is not defined
2024-05-16	20:25:02	49b62852-6f6d-417c-a5b3-0a35361b45fe	ReferenceError: jwt is not defined
2024-05-16	20:25:02	8fd6502f-a97b-4432-b1b4-529d34bcb3a9	ReferenceError: jwt is not defined
2024-05-16	20:25:16	8e6a7c86-e47b-4e21-a306-00520e143c0f	ReferenceError: jwt is not defined
2024-05-16	20:25:16	8ec896bc-3b8f-456e-bf13-eb9f20fb19b9	ReferenceError: jwt is not defined
2024-05-16	20:25:20	839400bb-0a72-46d3-8381-979d4b31a124	ReferenceError: jwt is not defined
2024-05-16	20:25:25	cbb83519-2a70-45b6-95d8-6faed64c0808	ReferenceError: jwt is not defined
2024-05-16	20:25:25	3f1e6c6d-c00e-492c-bf45-dd4037ad11a1	ReferenceError: jwt is not defined
2024-05-16	20:25:25	835cbec7-3ec3-4b94-bef8-4e1892893d44	ReferenceError: jwt is not defined
2024-05-16	20:25:25	1b5f4249-6726-42cb-a306-83583b67fbf2	ReferenceError: jwt is not defined
2024-05-16	20:25:25	ac9c18d5-c53a-474f-9e7d-a833ce6a3b26	ReferenceError: jwt is not defined
2024-05-16	20:26:17	883394d2-91b6-4c31-b87b-4d26e356a985	ReferenceError: jwt is not defined
2024-05-16	20:26:17	c26c4b27-0bb3-4036-b481-cf31b8cc60cf	ReferenceError: jwt is not defined
2024-05-22	20:49:39	188c98fb-cf70-4e41-9e08-9a2f189e3fd3	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:39	6d6bd7d9-2cbe-438a-b14c-677514b7b0e3	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:40	8d6886b8-7d1f-47f3-873e-1fcb4a8a6eec	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:40	c9820470-62f9-4fb4-865d-6e35af5ae2b8	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:48	f35ca2f2-c26c-4ff5-8330-b1f36d3cf967	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:48	3d09d011-bb0f-4523-9930-7e434a1647d6	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:54	54c9b377-c694-45e7-a6bd-fdfab93471c9	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:49:54	67bc8b09-4414-4495-b24f-7105a9d589e7	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:50:07	dab4089c-e448-4d70-845a-f2d08a606d80	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:50:08	efb6e4dd-4233-45ff-993d-f6001a93d636	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:50:10	a280c587-7e16-4717-a599-452a47e00b99	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:52:22	3c45a8cd-03ef-4937-a6b2-e746292df69f	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:52:22	7c5d95ce-59a1-4947-a2c2-c08537f29b18	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:54:13	2a4730bd-412a-4fb9-9460-e16be1cdf286	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:54:13	2d2d1d46-ff93-42ea-9c0b-a67be12e6be0	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:55:36	f7ada0fe-74b6-4e60-b917-cb4ef3e08914	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:55:36	a6512c2c-2095-4c83-91b9-e4a0dc2d781a	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:57:26	29a5be42-5784-4087-8922-12fa80ac6e06	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	20:57:26	87e224e8-d7ac-4fef-83a5-0a4e9de2e225	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:14:42	984dd03b-721a-4ab2-98eb-deac858f0c91	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:14:42	fbec6451-e669-4737-9447-7eb64d720b59	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:28:05	101b374b-0057-450c-ada0-63571eca98de	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:28:06	78c71172-0c06-4997-a63c-7eb72f4668ff	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:28:06	d8b2b88e-81ff-4d2f-91c4-451bc1f704b0	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-22	22:28:07	851e7f54-2288-4478-b939-90a200eec6ab	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:07:49	203290b3-e3ec-4146-8e4f-1f4e0fb96699	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:07:49	01f6521a-5f1d-4d51-af1c-957e18cfd52e	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:07:50	614b4f80-a8a7-4a71-a400-a36988fd74e2	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:07:50	a9964b43-3005-4a72-9f41-21ae824cbe54	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:59:12	6be4481e-fae2-468e-a3dc-42abe44905b4	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-24	23:59:12	b6bd7ea6-9045-42e6-8661-6fd7f1be8e17	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-25	15:35:39	8bd26bd3-1b50-43a3-981d-208e4dcdadbf	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-25	15:35:40	ee5c0b65-45f0-4d90-8411-1eae8aa7beb5	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-25	15:35:43	0c8b8c98-a915-440a-836e-d9c8516178da	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:29:57	78af05de-4fb1-48b1-8e24-198402e512ce	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:29:57	b4a91fb3-c1e7-4604-a2e9-fdf1d755610a	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:29:58	527082dc-89bb-4b38-ac11-e3d19b9771e8	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:29:58	bdd05f30-2537-471a-a803-940ebc3c1066	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:30:42	32a10f10-2307-4479-b20c-f8b125af4e00	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:48	c71368a2-464f-4262-a27e-484ab92d8c25	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:49	8adc6704-5542-4ea4-bb62-6600ea01bafc	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:51	860d9014-9476-4df2-969b-f606ce68191a	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:54	b37796cd-85b0-46ba-bec5-f25b4876795c	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:54	33985f95-b58c-4f2b-bc3e-485760a8e940	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-26	18:37:57	18679a51-8c30-4513-864b-8e3ba60bc07e	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:17:16	847c7fdd-4194-4004-bfa3-37e13f534294	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:17:16	05f8bea5-cd79-47e2-89bf-20ed44d754dc	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:17:17	28a124ed-5d8c-4a5d-9ca1-0cf3e0b54e55	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:20:49	5a607825-3cf1-4a16-b954-26ba58412f4c	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:20:50	1bc69cdd-2f8c-4262-a45c-ce32e4f6c9b4	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:21:01	9dbde4ab-b87d-4171-9c0a-5ac33d8d97ef	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:21:01	cc1eec0c-de08-4b50-8862-b94453fa31a3	TypeError: Cannot read properties of undefined (reading 'id')
2024-05-27	19:21:13	49de9989-bd0c-494c-afc6-4181693ab3be	TypeError: Cannot read properties of undefined (reading 'id')
2024-06-10	17:47:32	949eebe1-85f8-49d2-9765-b5208472b634	TypeError: Cannot read properties of undefined (reading 'id')
2024-06-10	17:47:32	f1847985-ffa3-4526-9a9f-198973fa3c49	TypeError: Cannot read properties of undefined (reading 'id')
2024-06-10	17:47:33	df1a9754-b274-49f5-b15f-44a692746530	TypeError: Cannot read properties of undefined (reading 'id')
2024-06-10	17:47:33	5e9de83c-a292-482b-b002-2a4e8e123a55	TypeError: Cannot read properties of undefined (reading 'id')
2024-06-10	17:47:37	5d0ea9e1-82dc-4e5e-ac12-eb564c0567b0	TypeError: Cannot read properties of undefined (reading 'id')
2024-07-26	15:05:32	de41708c-ad61-42bc-9fa0-f903daf33aee	SyntaxError: Unexpected token , in JSON at position 4571
2024-07-27	11:53:27	c52dfe83-dbcd-4bfc-947f-a924c19f90ec	SyntaxError: Unexpected token ] in JSON at position 9833
2024-07-27	12:02:02	71df35a0-4674-4137-ac21-bbc19a574602	SyntaxError: Unexpected token ] in JSON at position 6381
2024-07-27	12:04:18	79bd0bf5-a9a5-4e0b-8c6d-b9c468c1883d	SyntaxError: Unexpected token ] in JSON at position 10326
2024-08-03	13:27:47	c8fd4d97-a1ef-4328-bf3b-f7299856f8c5	PayloadTooLargeError: request entity too large
2024-08-03	14:06:07	52af6857-54e9-4a00-933c-5d328a20fd92	SyntaxError: Unexpected token , in JSON at position 40219
2024-08-03	14:06:23	e8343dbd-2805-4b97-906b-8715cf3a14eb	SyntaxError: Unexpected token , in JSON at position 19058
2024-08-03	14:07:02	77968b7c-0653-45eb-a3bb-4640eeba0373	SyntaxError: Unexpected token , in JSON at position 24757
2024-08-03	14:07:13	1ab66d12-d547-431b-a924-38bb1d652a29	SyntaxError: Unexpected token , in JSON at position 25790
2024-08-20	13:19:37	69409454-fcbd-406a-a37d-9d49aec3bf6d	PayloadTooLargeError: request entity too large
2024-08-20	20:51:27	f0479ae0-a333-44ea-91bb-f0befb9884db	SyntaxError: Unexpected token , in JSON at position 46063
2024-08-20	20:54:07	2636d9a5-0ff1-4ee3-9bd7-fcc54ea61125	SyntaxError: Unexpected token , in JSON at position 23072
2024-08-20	20:58:08	e3d0e34c-dcb2-4f66-959f-d91888be7465	SyntaxError: Unexpected token , in JSON at position 12031
2024-08-22	18:01:13	34723a3a-fa62-4e70-8ee9-14101eda2660	PayloadTooLargeError: request entity too large
2024-08-24	11:48:30	7b1c42bd-4562-418d-8755-2d306f410f05	PayloadTooLargeError: request entity too large
2024-08-24	11:48:31	c6f8d6dd-0b22-4df4-9b27-8288f9ac5db2	PayloadTooLargeError: request entity too large
2024-08-24	11:48:37	d4b705ad-03dc-4a9a-b694-03390010d7a9	PayloadTooLargeError: request entity too large
2024-08-24	11:49:04	a3ca0949-36fe-4f8f-941c-57fcf0f9afb4	PayloadTooLargeError: request entity too large
2024-08-24	11:51:17	ca754ab3-b430-4e55-a99d-b77386ef3476	PayloadTooLargeError: request entity too large
2024-08-24	11:51:49	11c5dcb4-4c13-415a-b270-4d4f8c3ae68a	PayloadTooLargeError: request entity too large
2024-08-24	11:55:13	eccd796b-d18a-4277-8b88-d0b72a5ab4b1	PayloadTooLargeError: request entity too large
2024-08-24	11:59:12	cc5ae0b3-3fbe-490f-8617-8bd70c365d0f	PayloadTooLargeError: request entity too large
2024-08-24	11:59:22	4f516c73-84d5-44a8-b6ed-7d6d3d2c7d43	PayloadTooLargeError: request entity too large
2024-08-24	12:01:35	02387862-6d6a-48da-be2d-401981bbf13a	PayloadTooLargeError: request entity too large
2024-08-24	12:02:49	f0127c9b-459e-4c67-ab39-1c3d4a5b2246	PayloadTooLargeError: request entity too large
2024-08-24	12:06:57	86902d00-1c9d-40cd-a870-5a0ce74de075	PayloadTooLargeError: request entity too large
2024-08-24	12:07:18	7955b30b-8783-49ab-a7a7-aeb8e48b989d	PayloadTooLargeError: request entity too large
2024-08-24	12:07:51	6c1807bf-9922-4004-a5ff-91906293d889	PayloadTooLargeError: request entity too large
2024-08-24	12:08:09	af8fdaf7-149b-49a6-bb78-08586659a386	PayloadTooLargeError: request entity too large
2024-08-24	12:09:15	95126a41-61ec-4de5-b411-27b97db0e06a	PayloadTooLargeError: request entity too large
2024-08-24	12:09:47	a8747c91-5399-43db-960f-2c4b2af3ad7c	PayloadTooLargeError: request entity too large
2024-08-24	12:16:03	d17cb60f-7484-44ba-bc66-3a01dd4ab552	PayloadTooLargeError: request entity too large
2024-08-24	12:22:13	5887025b-ed95-4d83-a37b-7275905883e1	PayloadTooLargeError: request entity too large
2024-08-24	12:22:38	266407cb-3bba-4988-8452-c8829978a063	PayloadTooLargeError: request entity too large
2024-08-24	12:22:56	3d03cc87-9861-4f93-a03a-7bd598ee7c77	PayloadTooLargeError: request entity too large
2024-08-24	12:35:04	9edaecb0-7f06-4106-b271-6adbf5d43332	PayloadTooLargeError: request entity too large
2024-08-24	12:37:15	80df4753-976f-4a58-a1bd-c36cb4c764fe	PayloadTooLargeError: request entity too large
2024-08-24	12:37:16	b4ed54a9-b322-4f5c-be23-0447b3f85869	PayloadTooLargeError: request entity too large
2024-08-24	21:42:47	c48b64ee-45f0-4a71-af0c-bbf9ed9d08c6	SyntaxError: Unexpected end of JSON input
2024-08-26	17:46:51	3a893a2a-5c21-4b41-8cf5-9c88ee09ea23	PayloadTooLargeError: request entity too large
2024-08-26	17:46:56	8962a0c8-2d0e-4afc-b971-2248457b4909	PayloadTooLargeError: request entity too large
2024-08-26	17:48:24	76e90cf0-2a11-4986-a3c5-c316cc6a3451	PayloadTooLargeError: request entity too large
2024-08-26	17:49:21	b37c712b-a399-4768-ae18-dcc7de24ded3	PayloadTooLargeError: request entity too large
2024-08-26	17:49:42	8b4caafd-5552-4140-aec0-6592f9c179e1	PayloadTooLargeError: request entity too large
2024-08-26	17:49:59	4d7cc0ad-f16e-462a-8193-cc521e1140ab	PayloadTooLargeError: request entity too large
2024-08-26	17:50:30	a6e2f9f8-9d70-4b9d-a5fc-adac7c63db3d	PayloadTooLargeError: request entity too large
2024-08-26	17:52:25	7849185f-5324-4509-93c0-daefe14fa8b9	PayloadTooLargeError: request entity too large
2024-08-26	17:55:06	e96a0605-7130-4ae6-a823-bb7627ef7464	PayloadTooLargeError: request entity too large
2024-08-26	17:55:34	ec0b4b74-7be4-49ec-bf0f-1ee01bcc2ab0	PayloadTooLargeError: request entity too large
2024-08-26	17:56:56	8bca21d7-c287-4b0b-a2d9-a8a4697b1a76	PayloadTooLargeError: request entity too large
2024-08-26	17:57:46	3e67ac65-c708-4d25-9548-f80ab68b74f0	PayloadTooLargeError: request entity too large
2024-08-26	18:01:46	97e7f369-3386-4d96-894b-28b50045c326	PayloadTooLargeError: request entity too large
2024-08-26	18:06:08	d9c05d5f-82b4-4c3b-a763-b5c4e1960b0e	PayloadTooLargeError: request entity too large
2024-08-29	22:04:34	aae1e92a-643c-41fb-b442-2d57263142fe	SyntaxError: Unexpected token < in JSON at position 776288
2024-09-22	18:27:43	3363f2e2-be2d-402a-93d5-9abe5d9c7f57	TypeError: upload is not a function
2024-09-22	18:36:56	8a330366-4150-4832-97b8-6288fa732042	TypeError: upload.upload is not a function
2024-09-22	18:37:05	ba9b84f6-2195-49e8-9816-4d71c9808f2b	TypeError: upload is not a function
2024-09-22	22:39:59	c2b414de-5223-4fcf-a10c-26f10a30c7b1	TypeError: upload is not a function

2025-03-04	12:21:35	107a00e5-5fe5-47d5-ada3-bf1d2fe5b3aa	SyntaxError: Unexpected token u in JSON at position 4
2025-03-04	12:21:55	5200705a-77ef-455a-a713-2186d9eb0b14	SyntaxError: Unexpected token e in JSON at position 18
2025-03-04	12:22:02	c4f964bd-fdd1-4da2-9a30-1f87cee0cd14	SyntaxError: Unexpected token e in JSON at position 18
2025-03-04	12:22:58	6d33be59-c972-4e2e-8d6d-ca14328f163f	SyntaxError: Unexpected token e in JSON at position 18
2025-03-05	11:24:24	9cb89b86-4157-475f-adfd-e4e20fefbf47	SyntaxError: Unexpected token } in JSON at position 73
2025-05-27	19:47:49	1759225a-8ca1-4bfd-9a1e-050ee718de82	Error: Not allowed by CORS
2025-05-27	20:14:03	ac109c7d-9894-41e0-8863-fb0e472b2ccd	Error: Not allowed by CORS
2025-05-27	20:14:04	671b1158-c3f1-4970-8593-f0122319599e	Error: Not allowed by CORS
2025-05-27	20:14:12	f3e819a0-4d93-42f1-84e9-ab7a9658334d	Error: Not allowed by CORS
2025-05-27	20:33:20	c1b0eec1-323f-4c89-b925-ea61778be809	Error: Not allowed by CORS
2025-05-27	20:34:18	0ae8d4de-5601-4f0c-9d2e-fe2bfb0ed42d	Error: Not allowed by CORS
2025-05-27	20:38:35	01de8c44-62b1-4703-a236-0f88891de370	Error: Not allowed by CORS
2025-05-27	20:40:08	23caa0b8-18fe-4b79-b647-dae205ff0e13	Error: Not allowed by CORS
2025-05-27	20:44:32	3c816632-8763-41e8-95e8-5e3cad2b0f03	Error: Not allowed by CORS
2025-05-27	20:44:46	6a66bc64-adb6-4396-96ab-2503870a1182	Error: Not allowed by CORS
2025-05-27	20:45:43	d824e1f5-edfd-44d9-97dc-bfe28397e46d	Error: Not allowed by CORS
2025-05-27	20:46:06	b3012832-be45-409d-be25-3418164f02f7	Error: Not allowed by CORS
2025-05-27	20:47:24	055d58d8-da82-4665-a15a-f2df429b3c53	Error: Not allowed by CORS
2025-05-27	20:47:58	55f1548c-386b-4354-aa94-ef1a75411ac0	Error: Not allowed by CORS
2025-05-27	21:01:33	b332a63a-9cc2-4f58-be72-e3a36ba211a7	Error: Not allowed by CORS
2025-05-27	21:02:55	d4b62af1-7c4d-411b-bf6c-9f18c951f1e2	Error: Not allowed by CORS
2025-05-27	21:03:51	a31cf802-997e-419d-b995-60a07610231f	Error: Not allowed by CORS
2025-05-27	21:07:06	b5ab936d-edd1-4d9b-a259-b31760cc7631	Error: Not allowed by CORS
2025-05-27	21:07:28	a7e2551b-d846-4b76-a234-237b35e97ddc	Error: Not allowed by CORS
2025-05-27	21:21:55	883b1c39-5225-4203-b737-ca76dd771c5d	Error: Not allowed by CORS
2025-05-27	21:27:08	a9fbf7b8-d8ca-4a21-b1c8-82ba4df3c7e2	Error: Not allowed by CORS
2025-05-27	22:30:16	2fbd5022-7679-4d80-a842-09aebdfc822a	Error: Not allowed by CORS
2025-05-27	22:30:45	9fe5f9f4-3ba3-4e25-a6e3-87bac6d4470c	Error: Not allowed by CORS
2025-05-27	22:31:13	4eae91bb-ad2a-4be1-8813-1837ab42b079	Error: Not allowed by CORS
2025-05-29	04:50:44	cfddd331-223f-4b2e-a66f-2ee33d61a651	Error: Not allowed by CORS
2025-05-29	04:50:54	f0123f97-8996-4275-b205-2c176ea06779	Error: Not allowed by CORS
2025-07-06	12:40:56	ff9cb226-76fb-443a-9694-4c307db88932	ReferenceError: geo is not defined
2025-08-23	21:55:34	47239471-ab52-457c-9b54-58698b9d9846	Error: The database connection must be open to store files
