# Recruitable [GO-Platform] Backend
[![Build Status](http://*************/badge/1f172ad6?branch=master)](http://*************/repos/26)

## Overview
A modern web application built with TypeScript, Vue.js, and Node.js, providing a robust full-stack solution.

## Tech Stack

### Frontend
- Vue.js 3.4.29
- TypeScript 5.0.2
- Face API.js for facial recognition capabilities
- PDF handling with pdf-lib
- QR Code generation support

### Backend
- Node.js with Express 4.18.2
- MongoDB with Mongoose 6.0.0
- WebSocket support via Socket.IO 4.6.1
- Authentication using Passport 0.7.0
- Email functionality with Nodemailer 6.9.1

### AI & Machine Learning Integration
- OpenAI SDK integration
- Google Generative AI capabilities

## Key Features
- Real-time communication using WebSocket
- Facial recognition capabilities
- PDF generation and manipulation
- QR code generation
- Email notifications
- OAuth2 authentication (LinkedIn integration)
- File upload handling with Multer
- Secure authentication with JWT
- Geolocation services
- MongoDB GridFS for file storage
- Natural language processing with Compromise
- Automated job scheduling
- Browser automation capabilities with Puppeteer

## Prerequisites
- Node.js
- MongoDB
- NPM (Node Package Manager)

## Installation

1. Clone the repository:
2. Install dependencies:
3. Create a `.env` file in the root directory and configure your environment variables:

env PORT=3000 MONGODB_URI=your_mongodb_connection_string JWT_SECRET=your_jwt_secret

### Add other necessary environment variables

4. Start the development server:

## Scripts
- `npm run dev` - Start development server with hot-reload
- `npm start` - Start production server
- `npm run build` - Build for production
- `npm run test` - Run tests

## Project Structure

project-root/ ├── src/ │ ├── client/ # Vue.js frontend │ ├── server/ # Express backend │ ├── models/ # MongoDB models │ ├── routes/ # API routes │ ├── middleware/ # Custom middleware │ └── utils/ # Utility functions

## API Documentation
The API documentation will be available at `/api-docs` when running the server.

## Deployment
The application can be deployed using PM2 process manager:

`npm install pm2 -g pm2 start npm -- start`

## Contributing
1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request


