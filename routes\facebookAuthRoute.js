/*'use strict';

const express = require('express');
const LinkedInAPI = require('../controllers/linkedInAuthController3');
const router = express.Router();

router.get('/auth', (req, res) => {
    res.redirect(LinkedInAPI.getAuthorizationUrl());
});




router.get('/callback', async (req, res) => {
    const code = req.query.code;

    // Log the query parameters to see if LinkedIn is returning the authorization code
    console.log('Query params:', req.query);

    if (!req.query.code) {
        console.error('Authorization code not provided.');
        res.redirect('/');
        return;
    }
    try {
        console.log(req.query);
        const data = await LinkedInAPI.getAccessToken(req);
        console.log('Access Token Response:', data);
        
        if (data.access_token) {
            req.session.token = data.access_token;
            req.session.authorized = true;
          } else {
            console.error('Access token not retrieved:', data);
            res.send("Error retrieving access token.");
            return;
          }
          const linkedinId = await LinkedInAPI.getLinkedInId(req);
          req.session.linkedInId = linkedinId;
          console.log('LinkedIn ID:', linkedinId);

          // Prepare content for the post
          const content = {
            title: 'Your Post Title',
            text: 'This is the content of your post.',
            shareUrl: 'https://your-url.com',
            shareThumbnailUrl: 'https://your-thumbnail.com/image.png'
        };

        

        // Step 3: Automatically Publish the Post after Authorization
        const response = await LinkedInAPI.publishContent(req, linkedinId, content);

        if (response) {
            console.log('Post published successfully:', response);
            return res.send('Post published successfully!');
        } else {
            return res.send('Error publishing post.');
        }
    }


    //   return res.redirect('/publish');
       
     catch (err) {
        console.error('Error during LinkedIn authentication:', err);
    res.send("Error during LinkedIn authentication.");
        res.json(err);
    }
});




router.post('/publish', async (req, res) => {
    const { title, text, url, thumb, id } = req.body;
    console.log('Form Data:', { title, text, url, thumb, id }); // Log the form data
  /*  console.log('Session Token:', req.session.token);
    const errors = [];
    if (validator.isEmpty(title)) {
        errors.push({ param: 'title', msg: 'Invalid value.' });
    }
    if (validator.isEmpty(text)) {
        errors.push({ param: 'text', msg: 'Invalid value.' });
    }
    if (!validator.isURL(url)) {
        errors.push({ param: 'url', msg: 'Invalid value.' });
    }
    if (!validator.isURL(thumb)) {
        errors.push({ param: 'thumb', msg: 'Invalid value.' });
    }

    if (errors.length > 0) {
        console.error('Validation Errors:', errors); // Log validation errors
        return res.json({ errors });
    }
*/
/*    const content = { title, text, shareUrl: url, shareThumbnailUrl: thumb };
    if (!req.session.authorized || !req.session.linkedInId) {
        return res.status(403).json({ error: 'User not authorized or LinkedIn ID not found.' });
    }
    try {
        //const response = await LinkedInAPI.publishContent(req, id, content);
        const linkedinId = await LinkedInAPI.getLinkedInId(req);
        req.session.linkedInId = linkedinId;

        const response = await LinkedInAPI.publishContent(req, linkedinId, content);
        console.log('LinkedIn API Response:', response);
       
       
        res.json({ success: 'Post published successfully.' });
    } catch (err) {
        console.error('Error publishing post:', err);
        res.json({ error: 'Unable to publish your post.' });
    }
});




module.exports = router;
*/
const express = require("express");
const router = express.Router();
const {
    facebookAuthorization,
    facebookRedirectAndShare,

} = require("../controllers/facebookAuthController");



router.get("/auth", (req, res) => {
    const { title, text, thumb } = req.query;
res.redirect(facebookAuthorization(req.query));
});


router.get("/redirect", async (req, res) => {
    return  res.json(facebookRedirectAndShare(req));
});



module.exports = router;
