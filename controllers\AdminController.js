const jwt = require("jsonwebtoken");
const Admin = require("../models/Admin");
const validator = require("validator");
const bcrypt = require("bcrypt");



const registerAdmin = async (req, res, next) => {
  try {
    const email = req.body.email;
    const name = validator.escape(req.body.name);

    let password = await bcrypt.hash(req.body.password, 12);

    const admin = new Admin({ email, name, password });
    const result = await admin.save();
    if (!result) {
      res.status(500).json({ message: "No result found" });
    } else {
      const token = jwt.sign({ email: result.email, id: result._id }, "test", {
        expiresIn: "1h",
      }); // test will put it in a env file to store the secret string

      res.status(200).json({ result, token });
    }
  } catch (error) {
    next(error);
  }
};

const loginAdmin = async (req, res, next) => {
  let getAdmin;
  const email = validator.normalizeEmail(req.body.email, { gmail_remove_dots: false });
  Admin.findOne({
    email: email,
  })
    .then((admin) => {
      if (!admin) {
        return res.status(401).json({
          message: "Invalid Email!",
        });
      }
      getAdmin = admin;
      return bcrypt.compare(req.body.password, admin.password);
    })
    .then((response) => {
      if (!response) {
        return res.status(401).json({
          message: "Invalid Password!",
        });
      }
      let jwtToken = jwt.sign(
        {
          email: getAdmin.email,
          adminId: getAdmin._id,
        },
        "test",
        {
          expiresIn: "1h",
        }
      );
      res.status(200).json({
        token: jwtToken,
        expiresIn: 3600,
        message: getAdmin,
      });
    })

    .catch((err) => {
      return res.status(401).json({
        message: "Authentication failed",
      });
    });
};
// Get All Admin
const getAllAdmin = async (req, res) => {
  const admin = await Admin.find();
  if (!admin) return res.status(204).json({ message: "No Admin found." });
  res.json(admin);
};

// Get Admin by Id
const getAdmin = async (req, res) => {
  if (!req?.params?.adminId)
    return res.status(400).json({ message: "Admin ID required." });

  const admin = await Admin.findOne({
    _id: validator.escape(req.params.adminId),
  }).exec();
  if (!admin) {
    return res
      .status(204)
      .json({ message: `No Admin matches ID ${req.params.adminId}.` });
  }
  res.json(admin);
};

// Delete A Admin
const DeleteAdmin = async (req, res) => {
  if (!req?.params?.adminId)
    return res.status(400).json({ message: "Admin ID required." });

  const admin = await Admin.findOne({
    _id: validator.escape(req.params.adminId),
  }).exec();
  if (!Admin) {
    return res
      .status(204)
      .json({ message: `No Admin matches ID ${req.params.adminId}.` });
  }
  const result = await Admin.deleteOne();
  res.json(result);
};

// Define a middleware function for updating a admin's name
const updateAdmin = async (req, res, next) => {
  try {
    // check if the Admin ID in the request parameter matches the authenticated Admin ID
    if (req.AdminId != req.body._id) {
      res.status(401).json({ message: "You are not authorized!" });
    }

    const adminId = validator.escape(req.body._id); // get the admin ID from the request body
    const admin = await Admin.findById(adminId); // find the admin in the database by ID
    if (!admin) {
      res.status(401).json({ message: "No admin exist" });
    }

    admin.name = validator.escape(req.body.name); // update the admin's name with the value from the request body
    await admin.save(); // save the updated admin object to the database

    res.status(201).json({ message: "Admin Updated" });
  } catch (error) {
    next(error); // call the error handling middleware
  }
};

module.exports = {
  getAdmin,
  getAllAdmin,
  updateAdmin,
  DeleteAdmin,
  registerAdmin,
  loginAdmin
};
