const path = require("path");
const multer = require("multer");
const { GridFsStorage } = require("multer-gridfs-storage");

let url = process.env.DB_URI;

let storage = GridFsStorage({
  url,
  file: (req, file) => {
    //If it is an image, save to photos bucket
    if (
      file.mimetype === "image/jpeg" ||
      file.mimetype === "image/png" ||
      file.mimetype == "image/jpg"
    ) {
      return {
        bucketName: "photos",
        filename: `${Date.now()}_${file.originalname}`,
      };
    } else {
      console.log("only image formats are accepted");
    }
  },
});

let upload = multer({
  storage: storage,
});

module.exports = upload;
