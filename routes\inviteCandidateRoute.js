const express = require("express");
const {
  generateLink,
  redirectLink,
  createTalent,
  getCandidate,
  getEmail,
  multiInviteLink,
  selfInvite,
} = require("../controllers/inviteCandidateController");
const router = express.Router();

router.post("/", generateLink);
router.get("/", redirectLink);
router.post("/newTalent", createTalent);
router.post("/candidate", getCandidate);
router.get("/email", getEmail);
router.get("/multi-invite-link/:projectId", multiInviteLink);
router.post("/self-invite", selfInvite);
module.exports = router;
