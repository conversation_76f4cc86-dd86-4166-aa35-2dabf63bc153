// Redirect request to Particular method on Controller
const express = require("express");
const { body } = require("express-validator");
const {
  getAdmin,
  getAllAdmin,
  updateAdmin,
  DeleteAdmin,
  registerAdmin,
  loginAdmin,
} = require("../controllers/AdminController");

const router = express.Router();

// POST /auth/
router.post(
  "/",
  [
    body("name")
      .trim()
      .not()
      .isEmpty()
      .isLength({ min: 4 })
      .withMessage("Please enter a valid name, minimum 4 character long"),
    body("email")
      .trim()
      .isEmail()
      .custom((emailId) => {
        return isAdminExist(emailId)
          .then((status) => {
            if (status) {
              return Promise.reject("Admin already exist!");
            }
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }),
    body("password")
      .trim()
      .isLength({ min: 8 })
      .custom((password) => {
        return isPasswordValid(password)
          .then((status) => {
            if (!status)
              return Promise.reject(
                "Enter a valid password, having at least 8 characters including 1 small alphabet, 1 capital alphabet, 1 digit and 1 special character($,@,!,#,*)."
              );
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }),
  ],

  registerAdmin
);

// POST /auth/login
router.post(
  "/login",
  [
    body("email")
      .trim()
      .isEmail()
      .normalizeEmail()
      .withMessage("Invalid Email!"),
    body("password")
      .trim()
      .custom((password) => {
        return isPasswordValid(password)
          .then((status) => {
            if (!status) return Promise.reject();
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      })
      .withMessage("Invalid Password!"),
  ],
  loginAdmin
);

// Admin should be authenticate
// Admin should be authorize
//Get /admin/:adminId
router.route("/:adminId").get(getAdmin);

//Get /admin/all-Admin
router.route("/").get(getAllAdmin);

//Get /admin/delete-Admin/:id
router.route("/delete-Admin/:adminId").delete(DeleteAdmin);

// Admin should be authenticate
// Admin should be authorize
//Put /admin/
router.put("/", updateAdmin);

module.exports = router;
