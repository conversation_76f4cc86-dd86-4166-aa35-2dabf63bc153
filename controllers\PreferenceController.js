const Preference = require("../models/Preference");
const mongoose = require('mongoose');
const jwt = require("jsonwebtoken");

// Create a new preference
const addPreference = async (req, res) => {
    const {
        jobStatus,
        companySize,
        workStyle,
        location,
        minBaseSalary,
        maxBaseSalary,

    } = req.body;

    let user_token = req.cookies.candidate;
    if (user_token) {
        jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }

            try {
                const newPreference = new Preference({
                    candidate: decoded.id, // Assuming req.user is populated from JWT middleware
                    jobStatus,
                    companySize,
                    workStyle,
                    location,
                    minBaseSalary,
                    maxBaseSalary,

                });

                const savedPreference = await newPreference.save();
                res.status(201).json({ message: "Preferences added successfully", savedPreference });
            } catch (error) {
                console.error("Error adding preferences:", error);
                res.status(500).json({ error: "Internal server error" });
            }
        })
    }
};

// Get preferences by user ID
const getPreferenceByCandidate = async (req, res) => {
    try {
        let token = req.cookies.candidate;
        console.log("jn");

        jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            const preferences = await Preference.findOne({ candidate: mongoose.Types.ObjectId(decoded.id) });

            if (!preferences) {
                return res.status(204).json({ message: "No preferences found." });
            }

            res.status(200).json(preferences);
        })
    } catch (error) {
        console.error("Error retrieving preferences:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

// Get preference by ID
const getPreferenceById = async (req, res) => {
    const preferenceId = req.params.id;

    try {
        const preference = await Preference.findById(preferenceId);

        if (!preference) {
            return res.status(204).json({ message: "No preference found." });
        }

        res.status(200).json(preference);
    } catch (error) {
        console.error("Error retrieving preference:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

// Update a preference by ID
const updatePreferenceById = async (req, res) => {
    const preferenceId = req.params.id;

    try {
        const preference = await Preference.findById(preferenceId);

        if (!preference) {
            return res.status(404).json({ message: "Preference not found." });
        }

        // Update fields if provided
        const {
            jobStatus,
            companySize,
            workStyle,
            location,
            minBaseSalary,
            maxBaseSalary,
            referral1,
            referral2,
        } = req.body;

        if (jobStatus) preference.jobStatus = jobStatus;
        if (companySize) preference.companySize = companySize;
        if (workStyle) preference.workStyle = workStyle;
        if (location) preference.location = location;
        if (minBaseSalary) preference.minBaseSalary = minBaseSalary;
        if (maxBaseSalary) preference.maxBaseSalary = maxBaseSalary;


        const updatedPreference = await preference.save();
        res.status(200).json({ message: "Preference updated successfully", updatedPreference });
    } catch (error) {
        console.error("Error updating preference:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

// Delete a preference by ID
const deletePreferenceById = async (req, res) => {
    const preferenceId = req.params.id;

    try {
        const preference = await Preference.findByIdAndDelete(preferenceId);

        if (!preference) {
            return res.status(204).json({ message: "No preference found." });
        }

        res.status(200).json({ message: "Preference deleted successfully", deletedPreference: preference });
    } catch (error) {
        console.error("Error deleting preference:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

module.exports = {
    addPreference,
    getPreferenceByCandidate,
    getPreferenceById,
    updatePreferenceById,
    deletePreferenceById,
};
