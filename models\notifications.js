const mongoose = require("mongoose");
// const { defineSSRCustomElement } = require("vue");
const Schema = mongoose.Schema;
const notifications = new Schema(
  {
    title: String,
    text: String,
    date: {
      type: Date,
      default: Date.now(),
    },
    //reciever: {
    //type: mongoose.Schema.Types.ObjectId,
    //ref: "employee",
    //},
    reciever: String,
    seen: {
      type: Boolean,
      default: false,
    },
    link: String,
    //    type: String,
    //  icon: "",
  }
  //{ timestamps: true }
);

const Notification = mongoose.model("Notifications", notifications);

module.exports = Notification;
