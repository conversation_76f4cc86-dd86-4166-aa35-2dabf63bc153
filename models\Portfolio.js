const mongoose = require("mongoose");
const PortfolioSchema = mongoose.Schema({
    candidate: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Candidate",
        required: true
    },
    url: {
        type: String,
        required: true
    },
    screenshots: {
        type: [String],
        required: true
    },
});
const Portfolio = mongoose.model('Portfolio', PortfolioSchema);
module.exports = Portfolio