const { sendEmail } = require("../utils/email");

const requestPlan = async (req, res, next) => {
  console.log("Request Plan:", req.body);
  const { name, email, plan, message } = req.body;
  try {
    const emailText = `
    Hello,<br />
  
    I hope this email finds you well. <br />
    
    I am writing to request the activation of the following plan: <br />
    
        - Plan: ${plan} <br />
        - Name: ${name} <br />
        - Email: ${email} <br />
    
    Additional Message: <br />
    ${message}
    <br />
    Thank you for your assistance in activating the chosen plan. I appreciate your prompt attention to this matter.<br />
    
    Best regards,<br />
    ${name}
    `;
    await sendEmail(
      "<EMAIL>",
      "Subscription Plan Activation",
      emailText
    );
    res.status(200).json({ message: "Request Plan received" });
  } catch (error) {
    console.log(error);
    res.status(400).json({ message: error.message });
  }
};

module.exports = { requestPlan };
