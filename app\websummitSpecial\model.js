const mongoose = require("mongoose");

const WebSummitFormSchema = new mongoose.Schema(
  {
    fullName: {
      type: String,
      required: true,
      trim: true,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
    },
    company: {
      type: String,
      required: true,
      trim: true,
    },
    otherInfo: {
      type: String,
      trim: true,
    },
    collaboration: {
      type: [String],

      default: [],
    },
    organization: {
      type: [String],
      default: [],
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("WebSummitSubscribers", WebSummitFormSchema);
