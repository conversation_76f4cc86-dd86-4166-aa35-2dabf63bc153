const express = require("express");
const router = express.Router();
const {
  createNewTalent,
  signin,
  verifyTalent,
  ChangePassword,
  ChangePasswordEmail,
  ChangeTalentPassword,
  getGeneralData,
} = require("../controllers/talentsController.js");
const { verifyToken } = require("../helper/verifyToken.js");

router.post("/create", createNewTalent);
router.post("/signin", signin);
router.get("/verify", verifyTalent);
router.get("/changePassword", ChangePassword);
router.post("/changePassword", ChangePasswordEmail);
router.post("/changeTalentPassword", ChangeTalentPassword);
router.get("/isLoggedIn", async (req, res) => {
  let token = req.cookies.candidate;
  if (!token) {
    return res.status(401).json({ isLogged: false });
  }
  const decoded = await verifyToken(token);
  console.log({ decoded, token });
  if (decoded === "Not Authorized") {
    return res.status(401).json({ isLogged: false });
  }
  return res.json({ isLogged: true });
});

router.post("/logout", (req, res) => {
  if (req.cookies.candidate) {
    let options = {};
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 0, // Set maxAge to 0 to delete the cookie
      };
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 0, // Set maxAge to 0 to delete the cookie
      };
    }
    res.clearCookie("candidate", options);
    res.status(200).send("logged out");
  } else {
    res.status(400).send("not logged in");
  }
});

// this route is to get total candiadtes with time passing assessments.

router.get("/generalData", getGeneralData);

module.exports = router;
