const mongoose = require("mongoose");
const companyProject = mongoose.Schema({
  company_name: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  jobTitle: {
    type: String,
    required: true,
  },
  
  seniority: {
    type: String,
    required: true,
  },
  assessments: {
    type: Array,
    // required: true,
  },
  //codingChallenges: [codingChallengeSchema],
  is_coding_required: {
    type: Boolean,
    default: false
  },
  idChallenge: { type: mongoose.Schema.Types.ObjectId, ref: 'Challenge' },
      
  screeners: {
    type: Array,
    // required: true,
  },
  project_description: {
    type: String,
    // required: true
  },
  project_priority: {
    type: String,
    // required: true
  },
  project_status: {
    type: String,
    default: "Active",
    required: true,
  },
  recommanded_score: {
    type: Number,
    default: 70,
  },
  min_score: {
    type: Number,
    default: 0,
  },
  duration: {
    type: Number,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model("companyProjects", companyProject);
