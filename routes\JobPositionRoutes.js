const express = require("express");
const router = express.Router();
const jobPositionController = require("../controllers/JobPositionControllers");

// Create a new job position
router.post("/create", jobPositionController.createJobPosition);

// Update a job position
router.put("/update", jobPositionController.updateJobPosition);
router.put("/updateOne", jobPositionController.editJobPositions);

// Delete a job position
// router.delete("/:id", jobPositionController.deleteJobPosition);

// Get all job positions
router.get("/all", jobPositionController.getAllJobPositions);
router.get(
  "/getAllJobPositionsPerPage",
  jobPositionController.getAllJobPositionsPerPage
);
router.get("/titles", jobPositionController.getTitles);
router.get(
  "/skillsRecommendations",
  jobPositionController.getSkillsRecommendations
);
router.get("/getRecommended", jobPositionController.getRecommended);
router.delete("/:jobId", jobPositionController.deletePosition);
// Get a job position by ID
router.get("/:id", jobPositionController.getJobPositionById);

module.exports = router;
