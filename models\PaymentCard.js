const mongoose = require('mongoose');

const paymentCardSchema = new mongoose.Schema({
  userId:{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Candidate',
    required: true
  },
  cardNumber: {
    type: String,
    required: true,
    trim: true,
    minlength: 16,
    maxlength: 16
  },
  cardHolderName: {
    type: String,
    required: true,
    trim: true
  },
  expirationDate: {
    type: Date,
  },
  cvv: {
    type: String,
    required: true,
    trim: true,
    minlength: 3,
    maxlength: 4
  },
});

const PaymentCard = mongoose.model('PaymentCard', paymentCardSchema);

module.exports = PaymentCard;