// uploadController.js

const multer = require("multer");
const { GridFsStorage } = require("multer-gridfs-storage");

const url = process.env.DB_URI;

// Define storage configuration inside the controller
const storage = new GridFsStorage({
    url,
    file: (req, file) => {
        if (
            file.mimetype === "image/jpeg" ||
            file.mimetype === "image/png" ||
            file.mimetype === "image/jpg"
        ) {
            return {
                bucketName: "photos",
                filename: `${Date.now()}_${file.originalname}`,
            };
        } else {
            console.log("Only image formats (jpeg, png, jpg) are accepted");
            return null;
        }
    },
});

const upload = multer({
    storage,
    limits: { fileSize: 10 * 1024 * 1024 },  // 10MB limit
}).single("image");  // Handle single image upload

exports.uploadImage = (req, res) => {
    upload(req, res, (err) => {
        if (err) {
            return res.status(500).json({ message: "File upload failed", error: err.message });
        }

        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded or unsupported format" });
        }

        // Respond with success
        return res.status(200).json({
            message: "File uploaded successfully",
            file: req.file,
        });
    });
};
