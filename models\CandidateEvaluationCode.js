const mongoose = require("mongoose");

// Define the schema for storing assessment results
const CandidateEvaluationCodeSchema = new mongoose.Schema({
  candidate: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  challengeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Challenge' },
  idProject: { type: mongoose.Schema.Types.ObjectId, ref: 'companyProjects' },

  codeCorrectness: {
    score: { type: Number },
    subcriteria: {
      testCasePassRate: {
        score: { type: Number },
        feedback: { type: String }
      },
      edgeCaseHandling: {
        score: { type: Number },
        feedback: { type: String }
      },
      errorFreeExecution: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  efficiencyPerformance: {
    score: { type: Number },
    subcriteria: {
      timeComplexity: {
        score: { type: Number },
        feedback: { type: String }
      },
      spaceComplexity: {
        score: { type: Number },
        feedback: { type: String }
      },
      benchmarkCompliance: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  codeQualityReadability: {
    score: { type: Number },
    subcriteria: {
      readability: {
        score: { type: Number },
        feedback: { type: String }
      },
      modularity: {
        score: { type: Number },
        feedback: { type: String }
      },
      languageIdioms: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  problemSolvingApproach: {
    score: { type: Number },
    subcriteria: {
      algorithmChoice: {
        score: { type: Number },
        feedback: { type: String }
      },
      logicalFlow: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  securityRobustness: {
    score: { type: Number },
    subcriteria: {
      inputValidation: {
        score: { type: Number },
        feedback: { type: String }
      },
      vulnerabilityAvoidance: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  scalability: {
    score: { type: Number },
    subcriteria: {
      architecturalDesign: {
        score: { type: Number },
        feedback: { type: String }
      },
      concurrency: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  toolingOriginality: {
    score: { type: Number },
    subcriteria: {
      plagiarismDetection: {
        score: { type: Number },
        feedback: { type: String }
      },
      bestPractices: {
        score: { type: Number },
        feedback: { type: String }
      }
    }
  },
  overall_score: { type: Number },
  overall_feedback: { type: String }
});

// Create a model for the assessment results
const CandidateEvaluationCode = mongoose.model(
  "CandidateEvaluationCode",
  CandidateEvaluationCodeSchema
);

module.exports = CandidateEvaluationCode;
