require("dotenv").config();
const nodemailer = require("nodemailer");
const ProjectError = require("../helper/error");
const jwt = require("jsonwebtoken");

const {
    sendEmail
} = require("../utils/email");
const sendMailReferal = async (req, res) => {

    let token = req.cookies.user
    let name = ''

    if (token) {
        jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(400).json({
                    title: "unauthorized",
                });
            }
            try {
                name = decoded.first_name + ' ' + decoded.last_name
            } catch (error) {
                console.log({ error });
                res.status(400).json("there was an error", error);
            }
        })

    }

    const { email } = req.body
    const subject = 'Invitation Mail'
    const html = `<html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invitation to GO Platform</title>
    </head>
    <body style="font-family: 'Arial', sans-serif;">
    
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; text-align: center; border: 1px solid #ccc; border-radius: 10px;">
        <h2 style="color: #333;">Invitation to GO Platform</h2>
        <p>You have been invited by ${name} to join the GO Platform!</p>
        <p>Click on the link below to get started:</p>
    
        <a href="https://go-platform.com/" style="display: inline-block; padding: 10px 20px; background-color: #2196f3; color: #fff; text-decoration: none; border-radius: 5px; margin-top: 15px;">Visit GO Platform</a>
    
        <p style="margin-top: 20px;">We look forward to having you on board!</p>
      </div>
    
    </body>
    </html>`

    try {
        await sendEmail(email, subject, html)
        res.json({ message: "mail has been successfully sent" })

    } catch (err) {
        res.status(500).json({ error: "Error sending mail", details: err });

    }
};



module.exports = {
    sendMailReferal,
};