name: Deploy to DigitalOcean

on:
  push:
    branches: ["master"]
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install SSH Client
        run: sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        run: |
          sshpass -p "${{ secrets.PASS }}" ssh -o StrictHostKeyChecking=no ${{ secrets.USERNAME }}@${{ secrets.DO_IP }} << 'EOF'
            sudo su
            cd /root/server
            sudo git stash
            sudo git pull
            sudo npm i
            sudo pm2 reload server.js
            # Add additional deployment commands here, e.g., restart services
            # Example: sudo systemctl restart my-service
          EOF
