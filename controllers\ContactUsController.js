require("dotenv").config();
const ProjectError = require("../helper/error");
const { sendEmail } = require("../utils/email");

const sendContactUsForm = async (req, res) => {
  const { name, email, message, company, subject } = req.body;
  console.log({ name, email, message, company, subject });
  try {
    // Check if any of the required fields is missing
    if (!name || !email || !message) {
      throw new ProjectError("All fields are required");
    }

    // Construct the email text
    const emailText = `
    Contact Us Form Submission
  
    Name: ${name}
    Company: ${company}
    Subject: ${subject}
    Email: ${email}
  
    Message:
    ${message}
  
    -------------------------
    This email is sent via the Contact Us form on Go Platform.
  `;

    // Send the email using the sendEmail function
    await sendEmail(
      "<EMAIL>",
      "Contact Us Form Submission",
      emailText
    );
    // await sendEmail("<EMAIL>", "Contact Us Form Submission", emailText);

    // Respond to the client indicating success
    res.status(200).json({ message: "Message sent successfully!" });
  } catch (error) {
    console.log({ error });
    // Handle errors and respond to the client
    res.status(400).json({ message: error.message });
  }
};

module.exports = { sendContactUsForm };
