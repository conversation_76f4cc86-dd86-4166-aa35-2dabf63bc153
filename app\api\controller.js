const { verifyToken } = require("../../helper/verifyToken.js");
const User = require("../../models/user.js");
const Company = require("../../models/Company.js");
const Assessments = require("../../models/allAssessments.js");
const companyProject = require("../../models/companyProject");
const Notification = require("../../models/notifications.js");

const generateApiKey = async (req, res) => {
  const token = req.cookies.user;
  const decoded = await verifyToken(token);
  const user = await User.findById(decoded.id);
  const company = await Company.findOne({
    name: decoded.company_name,
  });

  if (!company) {
    return res.status(404).send("Company not found");
  }
  if (company.apiKey) {
    return res
      .status(400)
      .send({ key: company.apiKey, message: "API key already exists" });
  }

  const crypto = require("crypto");
  const bcrypt = require("bcrypt");

  const rawApiKey = crypto.randomBytes(24).toString("hex"); // 64-character API key
  const hashedApiKey = await bcrypt.hash(rawApiKey, 10); // Store this in DB
  company.apiKey = hashedApiKey;
  await company.save();
  res.json({ apiKey: company.apiKey });
};

const getAssessments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
    const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page if not provided
    const category = req.query.category;
    const skip = (page - 1) * limit;
    const search = req.query.search;

    let assessments = await Assessments.aggregate([
      {
        $match: {
          // Category filter
          category: !category
            ? { $in: ["Hard Skills", "Psychometrics", "Soft Skills"] }
            : { $eq: category },

          // Search filter (matches any text fields you specify)
          ...(search && {
            $or: [
              { name: { $regex: search, $options: "i" } }, // Case-insensitive match for "name" field
              { description: { $regex: search, $options: "i" } }, // Case-insensitive match for "description" field
            ],
          }),
        },
      },
      {
        $project: {
          questions_list: 0, // Exclude the questions_list field
          answers: 0,
          ranges: 0,
        },
      },
    ])
      .sort({ name: 1 })
      .skip(skip)
      .limit(limit);

    // const assessments = await Assessments.find();
    res.json(assessments);
  } catch (error) {
    console.error({ error });
    return res.status(400).send("Internal server error");
  }
};

// create a new project
const createProject = async (req, res) => {
  const authHeader = req.headers.authorization;
  const key = authHeader.split(" ")[1];

  const project = req.body;
  console.log({ project: JSON.stringify(project) });

  let company = await Company.findOne({
    apiKey: key,
  });
  if (company.plan === "free") {
    project.assessments.map(async (assessment) => {
      console.log({ RATINGG: assessment.rating, name: assessment.name });
      const assess = await Assessments.findOne({ name: assessment.name });
      if (assess.rating !== "essential") {
        return res.status(401).json({
          title: "unauthorized",
        });
      }
    });
  }

  // let projectOBJ = project.toObject();
  project.company_name = company.name;
  const projectToSave = await companyProject(project);
  const savedProject = await projectToSave.save();
  const recruiters = await User.find({
    company_name: { $in: [company.name] },
  });
  for (const rec of recruiters) {
    //     const notification = await notificationController.createNotification("NEW PROJECT: "+project.name,"JOB TITLE: "+project.jobTitle+" AND SENIORITY: "+project.seniority , rec._id);
    const newNotif = new Notification({
      title: "New project: " + project.name,
      text: "JOB : " + project.jobTitle + ", SENIORITY: " + project.seniority,
      reciever: rec._id,
      link: "/boards?id=" + savedProject._id,
    });
    await newNotif.save();
  }
  res.send(savedProject);
};

module.exports = { generateApiKey, getAssessments, createProject };
