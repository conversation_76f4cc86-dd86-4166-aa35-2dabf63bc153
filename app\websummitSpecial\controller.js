const WebSummitSubscribers = require("./model");

const createNewSubscriber = async (req, res) => {
  try {
    const subscriber = req.body;
    console.log({ subscriber });
    const newSubscriber = new WebSummitSubscribers(req.body);
    await newSubscriber.save();
    res.status(201).json({ message: "Subscriber created successfully!" });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

module.exports = { createNewSubscriber };
