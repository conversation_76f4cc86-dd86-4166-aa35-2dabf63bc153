// Candidate
const Candidate = require("../models/Candidate.js");
const validator = require('validator');
const getAllCandidate = async (req, res) => {
  const candidate = await Candidate.find();
  if (!candidate)
    return res.status(204).json({ message: "No Candidate found." });
  res.json(candidate);
};
const getCandidate = async (req, res) => {
  if (!req?.params?.id)
    return res.status(400).json({ message: "Candidate ID required." });

  const candidate = await Candidate.findOne({ _id: req.params.id }).exec();
  if (!candidate) {
    return res
      .status(204)
      .json({ message: `No Candidate matches ID ${req.params.id}.` });
  }
  res.json(candidate);
};

const createCandidate = async (req, res) => {
  const candidate = req.body;
  try {
    await Candidate.create(candidate);
    res.status(201).json({ message: "candidate created successfully!" });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

const deleteCandidate = async (req, res) => {
  if (!req?.body?.id)
    return res.status(400).json({ message: "Candidate ID required." });

  const candidate = await Candidate.findOne({ _id: validator.escape(req.body.id) }).exec();
  if (!candidate) {
    return res
      .status(204)
      .json({ message: `No candidate matches ID ${req.body.id}.` });
  }
  const result = await candidate.deleteOne();
  res.json(result);
};

module.exports = {
  getAllCandidate,
  getCandidate,
  createCandidate,
  deleteCandidate,
};
