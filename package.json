{"name": "go_platform", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon --legacy-watch server.js", "start:prod": "set NODE_ENV=production && nodemon server.js"}, "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "@types/uuid": "^9.0.1", "axios": "^1.7.7", "base-64": "^1.0.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "canvas": "^2.11.2", "compromise": "^14.14.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "ejs": "^3.1.9", "express": "^4.18.2", "express-validator": "^6.15.0", "face-api.js": "^0.22.2", "fs": "^0.0.1-security", "geoip-lite": "^1.4.10", "glob": "^10.3.10", "html-pdf": "^3.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^6.0.0", "mongoose-autopopulate": "^1.1.0", "mongoose-unique-validator": "^3.1.0", "morgan": "^1.10.0", "multer": "^1.4.4", "multer-gridfs-storage": "^5.0.2", "node-jobs": "^0.7.0", "nodemailer": "^6.9.1", "openai": "^4.68.4", "passport": "^0.7.0", "passport-linkedin-oauth2": "^2.0.0", "path": "^0.12.7", "pdf-lib": "^1.17.1", "peopledatalabs": "^9.3.2", "pm2": "^5.3.0", "puppeteer": "^21.10.0", "puppeteer-core": "^21.10.0", "qrcode": "^1.5.3", "querystring": "^0.2.1", "request": "^2.88.2", "socket.io": "^4.6.1", "ts-node": "^10.9.1", "typescript": "^5.0.2", "uuid": "9.0.0", "validator": "^13.0.0", "vue": "^3.4.29"}, "devDependencies": {"nodemon": "^2.0.20"}}