const jwt = require("jsonwebtoken");

module.exports = async (req, res, next) => {
  // Get the token from cookies
  let token = req.cookies.user || req.cookies.admin;

  // Check if the token exists
  if (!token) {
    return res.status(401).json({
      title: "unauthorized",
      message: "Token not provided.",
    });
  }

  // Verify the token
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
        message: "Invalid or expired token.",
      });
    }
    // Store decoded user data in req.user for controllers to access
    req.user = decoded;
    // Token is valid, proceed to the next middleware
    next();
  });
};