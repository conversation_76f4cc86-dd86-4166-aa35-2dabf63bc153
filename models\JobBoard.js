const mongoose = require("mongoose");
const JobBoardSchema = mongoose.Schema({
    company: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Company',
        required: true
    },
    job_position: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'JobPosition',
        required: true
    },
    min_salary: {
        type: Number,
        required: true
    },
    max_salary: {
        type: Number,
        required: true
    },
    location: {
        type: String,
        required: true
    },
    job_type: {
        type: String,
        required: true

    },
});
const JobBoard = mongoose.model('JobBoard', JobBoardSchema);
module.exports = JobBoard