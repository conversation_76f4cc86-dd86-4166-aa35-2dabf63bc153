const express = require("express");
const router = express.Router();
const authentication = require("../middleware/authentication");
const {
  addComment,
  likeComment,
  getProjectName,
  getComments,
  //   getProject,
  getProjects,
  //getProjectsNeed,
  createProject,
  updateProject,
  switchProjectStatus,
  deleteProject,
  getProject,
  duplicateProject,
  getProjectData,
  getProjectQsts,
  updateWeightning,
} = require("../controllers/companyProjectController");


  
router.post("/getProject", getProjectName);
  
  router.post("/addComment", authentication, addComment);
  router.post("/getComment",  getComments);
  router.put("/updateLike", authentication, likeComment);



router.post("/post", authentication, createProject);
router.put("/update", authentication, updateProject);
router.put("/update-weighting", authentication, updateWeightning);
router.post("/duplicateProject", authentication, duplicateProject);
router.get("/companyProjects", authentication, getProjects);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
//router.get("/companyNeedProjects", authentication, getProjectsNeed);
router.get("/project", getProject);
router.get("/preview", getProjectQsts);
router.put("/switchStatus", authentication, switchProjectStatus);
router.delete("/deleteProject", authentication, deleteProject);
router.get("/projectData", authentication, getProjectData);

module.exports = router;
