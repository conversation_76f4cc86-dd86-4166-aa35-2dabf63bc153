// routes/preferenceRoutes.js
const express = require("express");
const router = express.Router();
const {
    addPreference,
    getPreferenceByCandidate,
    getPreferenceById,
    updatePreferenceById,
    deletePreferenceById,
} = require("../controllers/PreferenceController");

// Create a new preference
router.post("/", addPreference);

// Get preferences by user ID
router.get("/", getPreferenceByCandidate);

// Get preference by ID
router.get("/:id", getPreferenceById);

// Update a preference by ID
router.put("/:id", updatePreferenceById);

// Delete a preference by ID
router.delete("/:id", deletePreferenceById);

module.exports = router;
