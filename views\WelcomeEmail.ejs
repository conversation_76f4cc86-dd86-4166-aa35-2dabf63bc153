<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to go platform</title>
  </head>
  <body>
    <!DOCTYPE html>
    <html>
      <body>
        <div
          class="ConfimationEmail"
          style="
            width: 640px;
            height: 911px;
            position: relative;
            background: #f8f8fc;
          "
        >
          <div
            class="EmailTemplateHeader"
            style="
              height: 104px;
              padding-left: 48px;
              padding-right: 48px;
              padding-top: 40px;
              padding-bottom: 40px;
              left: 0px;
              top: 0px;
              position: absolute;
              flex-direction: column;
              justify-content: center;
              align-items: flex-start;
              gap: 10px;
              display: inline-flex;
            "
          >
            <div
              class="Frame52"
              style="
                align-self: stretch;
                justify-content: flex-start;
                align-items: center;
                gap: 24px;
                display: inline-flex;
              "
            >
              <div
                class="Frame51"
                style="
                  flex: 1 1 0;
                  height: 24px;
                  justify-content: flex-end;
                  align-items: center;
                  gap: 16px;
                  display: flex;
                "
              >
                <div
                  class="FacebookBoxLine"
                  style="width: 24px; height: 24px; position: relative"
                >
                  <div
                    class="Vector"
                    style="
                      width: 24px;
                      height: 24px;
                      left: 0px;
                      top: 0px;
                      position: absolute;
                    "
                  ></div>
                  <div
                    class="Vector"
                    style="
                      width: 18px;
                      height: 18px;
                      left: 3px;
                      top: 3px;
                      position: absolute;
                      background: #b7c1cd;
                    "
                  ></div>
                </div>
                <div
                  class="InstagramLine"
                  style="width: 24px; height: 24px; position: relative"
                >
                  <div
                    class="Vector"
                    style="
                      width: 24px;
                      height: 24px;
                      left: 0px;
                      top: 0px;
                      position: absolute;
                    "
                  ></div>
                  <div
                    class="Vector"
                    style="
                      width: 20px;
                      height: 20px;
                      left: 2px;
                      top: 2px;
                      position: absolute;
                      background: #b7c1cd;
                    "
                  ></div>
                </div>
                <div
                  class="LinkedinBoxLine"
                  style="width: 24px; height: 24px; position: relative"
                >
                  <div
                    class="Vector"
                    style="
                      width: 24px;
                      height: 24px;
                      left: 0px;
                      top: 0px;
                      position: absolute;
                    "
                  ></div>
                  <div
                    class="Vector"
                    style="
                      width: 18px;
                      height: 18px;
                      left: 3px;
                      top: 3px;
                      position: absolute;
                      background: #b7c1cd;
                    "
                  ></div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="Information"
            style="
              left: 48px;
              top: 125px;
              position: absolute;
              flex-direction: column;
              justify-content: flex-start;
              align-items: flex-start;
              display: inline-flex;
            "
          >
            <div
              class="Image"
              style="
                width: 544px;
                height: 115px;
                position: relative;
                background: #ece9ff;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                overflow: hidden;
              "
            >
              <img
                class="Image1"
                style="
                  width: 544px;
                  height: 413.26px;
                  left: 0px;
                  top: -138px;
                  position: absolute;
                "
                src="https://via.placeholder.com/544x413"
              />
            </div>
            <div
              class="Content"
              style="
                flex-direction: column;
                justify-content: flex-start;
                align-items: flex-start;
                gap: 16px;
                display: flex;
              "
            >
              <div
                class="EmailContent"
                style="
                  padding: 40px;
                  background: white;
                  border-top-left-radius: 8px;
                  border-top-right-radius: 8px;
                  flex-direction: column;
                  justify-content: flex-start;
                  align-items: flex-start;
                  gap: 10px;
                  display: flex;
                "
              >
                <div
                  class="Content"
                  style="
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: flex-start;
                    gap: 24px;
                    display: flex;
                  "
                >
                  <div
                    class="Text"
                    style="
                      flex-direction: column;
                      justify-content: flex-start;
                      align-items: flex-start;
                      gap: 24px;
                      display: flex;
                    "
                  >
                    <div
                      class="WelcomeToGoPlatform"
                      style="
                        color: #121a26;
                        font-size: 20px;
                        font-family: Inter;
                        font-weight: 700;
                        line-height: 30px;
                        word-wrap: break-word;
                      "
                    >
                      Welcome to GO PLATFORM!
                    </div>
                    <div
                      class="HelloDiegoWeAreHappyToHaveYouAmongTheGoPlatformCommunityPleaseValidateYourEmailToJoinThePlatform"
                      style="
                        width: 534px;
                        color: #384860;
                        font-size: 16px;
                        font-family: Inter;
                        font-weight: 400;
                        line-height: 24px;
                        letter-spacing: 0.2px;
                        word-wrap: break-word;
                      "
                    >
                      Hello Diego,<br />We are happy to have you among the GO
                      PLATFORM community, <br />Please Validate your email to
                      join the Platform.
                    </div>
                    <div
                      class="MasterButton"
                      style="
                        padding-left: 32px;
                        padding-right: 32px;
                        padding-top: 16px;
                        padding-bottom: 16px;
                        background: #00aef0;
                        border-radius: 8px;
                        justify-content: center;
                        align-items: center;
                        gap: 8px;
                        display: inline-flex;
                      "
                    >
                      <div
                        class="Cta"
                        style="
                          color: white;
                          font-size: 16px;
                          font-family: Inter;
                          font-weight: 700;
                          line-height: 24px;
                          letter-spacing: 0.32px;
                          word-wrap: break-word;
                        "
                      >
                        Confirm Email
                      </div>
                    </div>
                    <div
                      class="YouAreReceivingThisEmailBecauseYourRegisteredToWwwGoPlatformCom"
                      style="width: 464px"
                    >
                      <span
                        style="
                          color: #384860;
                          font-size: 16px;
                          font-family: Inter;
                          font-weight: 400;
                          line-height: 24px;
                          letter-spacing: 0.2px;
                          word-wrap: break-word;
                        "
                        >You are receiving this email because your registered
                        to<br /></span
                      ><span
                        style="
                          color: #2969ff;
                          font-size: 16px;
                          font-family: Inter;
                          font-weight: 400;
                          text-decoration: underline;
                          line-height: 24px;
                          letter-spacing: 0.2px;
                          word-wrap: break-word;
                        "
                        >www. go-platform.com</span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="EmailContent"
                style="
                  padding-left: 40px;
                  padding-right: 40px;
                  padding-top: 24px;
                  padding-bottom: 24px;
                  background: white;
                  border-radius: 8px;
                  flex-direction: column;
                  justify-content: center;
                  align-items: flex-start;
                  gap: 8px;
                  display: flex;
                "
              >
                <div
                  class="Content"
                  style="
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: flex-start;
                    gap: 8px;
                    display: flex;
                  "
                >
                  <div
                    class="Text"
                    style="
                      flex-direction: column;
                      justify-content: flex-start;
                      align-items: flex-start;
                      gap: 8px;
                      display: flex;
                    "
                  >
                    <div
                      class="Questions"
                      style="
                        color: #121a26;
                        font-size: 16px;
                        font-family: Inter;
                        font-weight: 700;
                        line-height: 24px;
                        word-wrap: break-word;
                      "
                    >
                      ¿Questions?
                    </div>
                    <div
                      class="FeelFreeToAskAllYourRelevantQuestionsHereContactGoPlatformDz"
                      style="width: 464px"
                    >
                      <span
                        style="
                          color: #384860;
                          font-size: 14px;
                          font-family: Inter;
                          font-weight: 400;
                          line-height: 21px;
                          letter-spacing: 0.2px;
                          word-wrap: break-word;
                        "
                        >Feel free to ask all your relevant questions here
                        <br /></span
                      ><span
                        style="
                          color: #2969ff;
                          font-size: 14px;
                          font-family: Inter;
                          font-weight: 400;
                          text-decoration: underline;
                          line-height: 21px;
                          letter-spacing: 0.2px;
                          word-wrap: break-word;
                        "
                        ><EMAIL></span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="EmailTemplateFooter"
            style="
              padding-left: 48px;
              padding-right: 48px;
              padding-top: 24px;
              padding-bottom: 24px;
              left: 0px;
              top: 731px;
              position: absolute;
              justify-content: flex-start;
              align-items: flex-start;
              gap: 10px;
              display: inline-flex;
            "
          >
            <div
              class="EsteCorreoElectrNicoFueEnviadoAGitaEmailComSiPrefiereNoRecibirEsteTipoDeCorreoElectrNicoNoQuiereMSCorreosElectrNicosDeSuMarcaDarseDeBaja915WilshireBlvd1600LosAngelesCa900176410Usa2022Tumarca"
              style="width: 544px"
            >
              <span
                style="
                  color: #9d9d9d;
                  font-size: 14px;
                  font-family: Inter;
                  font-weight: 400;
                  line-height: 22.4px;
                  letter-spacing: 0.2px;
                  word-wrap: break-word;
                "
                >Este correo electrónico fue enviado a</span
              ><span
                style="
                  color: #2969ff;
                  font-size: 14px;
                  font-family: Inter;
                  font-weight: 400;
                  text-decoration: underline;
                  line-height: 22.4px;
                  letter-spacing: 0.2px;
                  word-wrap: break-word;
                "
              >
                <EMAIL></span
              ><span
                style="
                  color: #9d9d9d;
                  font-size: 14px;
                  font-family: Inter;
                  font-weight: 400;
                  line-height: 22.4px;
                  letter-spacing: 0.2px;
                  word-wrap: break-word;
                "
                >. Si prefiere no recibir este tipo de correo electrónico, ¿no
                quiere más correos electrónicos de su marca? </span
              ><span
                style="
                  color: #2969ff;
                  font-size: 14px;
                  font-family: Inter;
                  font-weight: 400;
                  text-decoration: underline;
                  line-height: 22.4px;
                  letter-spacing: 0.2px;
                  word-wrap: break-word;
                "
                >Darse de baja.<br /></span
              ><span
                style="
                  color: #9d9d9d;
                  font-size: 14px;
                  font-family: Inter;
                  font-weight: 400;
                  line-height: 22.4px;
                  letter-spacing: 0.2px;
                  word-wrap: break-word;
                "
                ><br />915 Wilshire BLVD #1600, Los Angeles, CA 90017-6410,
                USA<br />© 2022 TuMarca</span
              >
            </div>
          </div>
          <div
            class="Vector"
            style="
              width: 45.69px;
              height: 32.19px;
              left: 62px;
              top: 40.96px;
              position: absolute;
              background: #00aef0;
            "
          ></div>
          <div
            class="GoPlatform"
            style="
              width: 121.43px;
              height: 32.78px;
              left: 125.57px;
              top: 40px;
              position: absolute;
              color: #00aef0;
              font-size: 17.88px;
              font-family: Roboto;
              font-weight: 700;
              line-height: 31.29px;
              word-wrap: break-word;
            "
          >
            GO PLATFORM
          </div>
        </div>
      </body>
    </html>
  </body>
</html>
