const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const User = require("../models/user");
const Admin = require("../models/Admin");
const validator = require("validator");
const nodemailer = require("nodemailer");
const ejs = require("ejs");
const Company = require("../models/Company");

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.COMPANY,
    pass: process.env.PASS,
  },
});

const redirectNewUser = async (req, res) => {
const { email } = req.body;
  console.log({ email});
  const sanitizedEmail = validator.normalizeEmail(email, {
    gmail_remove_dots: false,
  });

  try {
    // Check if the email exists in the User model
    const existingUser = await User.findOne({ email: sanitizedEmail });
console.log({ existingUser });
    let companyFound = await Company.findOne({
      name: existingUser.company_name[0],
    });

    console.log({ companyFound });
    var currentTimestamp = Date.now();
    // Check if three months have passed
    if (currentTimestamp >= companyFound.endOfSubscription) {
      companyFound.plan = "free";
      companyFound.save();
    }

    const token = jwt.sign(
      {
        email: existingUser.email,
        first_name: existingUser.first_name,
        last_name: existingUser.last_name,
        id: existingUser._id,
        company_name: existingUser.company_name[0],
      },
      process.env.SECRET_WORD,
      { expiresIn: "24h" }
    );
    let options = {};
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    res.cookie("user", token, options);

    res.status(200).json({
      loggedIn: true,
      company_name: existingUser.company_name[0],
    });
  
  } catch (error) {
    console.log({ error });
    res.status(500).json({ message: "Something went wrong." });
  }
};

// Authentication function - handles user login
const signin = async (req, res) => {
  const { email, password } = req.body;
  console.log({ email, password });
  // Sanitize and validate email
  const sanitizedEmail = validator.normalizeEmail(email, {
    gmail_remove_dots: false,
  });

  try {
    // Check if the email exists in the User model
    const existingUser = await User.findOne({ email: sanitizedEmail });

    // If the email is not found in the User model, check the Admin model
    if (!existingUser) {
      const existingAdmin = await Admin.findOne({ email: sanitizedEmail });

      // If the email is not found in both User and Admin models, return an error
      if (!existingAdmin)
        return res.status(404).json({ message: "User does not exist." });

      // Proceed with admin authentication
      const isPasswordCorrect = await bcrypt.compare(
        password,
        existingAdmin.password
      );

      if (!isPasswordCorrect)
        return res.status(400).json({ message: "Invalid credentials." });

      const token = jwt.sign(
        {
          email: existingAdmin.email,
          id: existingAdmin._id,
          admin_name: existingAdmin.name,
          role: "admin", // Assuming you don't have a role field in the Admin model
        },
        process.env.SECRET_WORD,
        { expiresIn: "24h" }
      );

      let options = {};
      if (process.env.NODE_ENV === "dev") {
        options = {
          maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
          // httpOnly: true, // Set to true in production for HTTPS
        };
      }
      if (process.env.NODE_ENV === "production") {
        options = {
          domain: "server.go-platform.com",
          sameSite: "none",
          secure: true,
          path: "/",
          maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
          // httpOnly: true, // Set to true in production for HTTPS
        };
      }
      res.cookie("admin", token, options);

      res.status(200).json({
        loggedIn: true,
        role: "admin",
        admin_name: existingAdmin.name, // Include role in the response
      });

      return;
    }

    // Proceed with user authentication
    const isPasswordCorrect = await bcrypt.compare(
      password,
      existingUser.password
    );

    if (!isPasswordCorrect)
      return res.status(400).json({ message: "Invalid credentials." });

    let companyFound = await Company.findOne({
      name: existingUser.company_name[0],
    });
    // if (!companyFound.isVerified) {
    //   return res.status(400).json({
    //     message:
    //       "Email not verified. Please check your inbox to verify your account",
    //   });
    // }

    var currentTimestamp = Date.now();
    // Check if three months have passed
    if (currentTimestamp >= companyFound.endOfSubscription) {
      companyFound.plan = "free";
      companyFound.save();
    }

    const token = jwt.sign(
      {
        email: existingUser.email,
        first_name: existingUser.first_name,
        last_name: existingUser.last_name,
        id: existingUser._id,
        company_name: existingUser.company_name[0],
      },
      process.env.SECRET_WORD,
      { expiresIn: "24h" }
    );
    let options = {};
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    res.cookie("user", token, options);

    res.status(200).json({
      loggedIn: true,
      company_name: existingUser.company_name[0],
    });
  } catch (error) {
    console.log({ error });
    res.status(500).json({ message: "Something went wrong." });
  }
};

// User registration function
const signup = async (req, res) => {
  const recruiter = req.body;

  try {
    const existingUser = await User.findOne({ email: recruiter.email });

    if (existingUser) {
      return res.status(400).json({ message: "User already exists." });
    }

    if (recruiter.password !== recruiter.confirm_password)
      return res.status(400).json({ message: "Passwords do not match." });

    recruiter.password = await bcrypt.hash(recruiter.password, 12);
    const newRecruiter = await User(recruiter);

    const savedRecruiter = await newRecruiter.save();
    const token = jwt.sign(
      {
        email: newRecruiter.email,
        id: newRecruiter._id,
        company_name: newRecruiter.company_name[0],
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "1h",
      }
    );
    const options = {
      domain: "server.go-platform.com",
      sameSite: "none",
      secure: true,
      path: "/",
      maxAge: 1000 * 60 * 60, // Cookie expires in 1 hour
      httpOnly: true, // Set to true in production for HTTPS
    };
    res.cookie("user", token, options);
    res.status(200).json({ newRecruiter, token });
  } catch (error) {
    res.status(500).json({ message: "Something went wrong." + error });
  }
};

// Retrieves current user information from JWT token
const getUserInfo = async (req, res) => {
  // let token = req.headers.token; //token
  let user = req.cookies.user; //user

  jwt.verify(user, process.env.SECRET_WORD, (err, decoded) => {
    if (err)
      return res.status(401).json({
        title: "unauthorized",
      });
    User.findOne({ _id: decoded.id })
      .then((user) => {
        return res.status(200).json({
          title: "user grabbed",
          user: {
            email: user.email,
            name: user.first_name + " " + user.last_name,
            company_name: user.company_name,
          },
          userInfo: user,
        });
      })
      .catch((err) => {
        console.log(err);
      });
  });
};

// Admin function - gets all users in system
const getAllUsers = async (req, res) => {
  User.find({})
    .then((data) => {
      res.json(data);
      console.log("users retrieved");
    })
    .catch((err) => {
      console.log(err).sendStatus(500);
    });
};

// Gets companies associated with current user
const getCompanies = async (req, res) => {
  const token = req.cookies.user;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    //token is valid
    try {
      const user = await User.findOne({ _id: decoded.id });
      const companies = user.company_name;
      res.status(200).json({
        companies,
      });
    } catch (err) {
      console.log({ err });
    }
  });
};

// Email utility - sends password reset email
const sendChangePAsswordEmail = async (first_name, email, token) => {
  let redirectLink = '';
  if (process.env.NODE_ENV === "dev") {
    redirectLink = `http://localhost:3000/changePassword/?token=${token}`;
  }
  if (process.env.NODE_ENV === "production") {
    redirectLink = `https://go-platform.com/en/changePassword/?token=${token}`;
  }

  const template = await ejs.renderFile("./views/resetPassword.ejs", {
    data: {
      first_name,
      token,
      redirectLink,
    },
  });

  const mailOptions = {
    from: process.env.COMPANY,
    to: email,
    subject: "GO_PLATFORM Reset Password",
    html: template,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};

// Initiates password reset process
const ChangePasswordEmail = async (req, res) => {
  const email = req.body.email;
  try {
    if (!email) {
      return res.status(400).json({ message: "Email is required." });
    }
    const sanitizedEmail = validator.normalizeEmail(email, {
      gmail_remove_dots: false,
    });
    const existingUser = await User.findOne({ email: sanitizedEmail });
    if (!existingUser) {
      return res.status(400).json({ message: "User does not exist." });
    }
    const token = jwt.sign(
      {
        email: existingUser.email,
        id: existingUser._id,
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "1h",
      }
    );
    await sendChangePAsswordEmail(
      existingUser.first_name,
      existingUser.email,
      token
    );
    res.status(200).json({ message: "Email sent successfully!" });
  } catch (error) {
    console.log(error);
    res.status(505).json({ message: "Internal serer error" });
  }
};

// Handles password reset link validation
const ChangePassword = async (req, res) => {
  const token = req.query.token;
  try {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        // Check specifically for token expiration
        if (err.name === 'TokenExpiredError') {
          return res.status(401).json({
            title: "unauthorized",
            message: "Reset password link has expired. Please request a new one."
          });
        }
        return res.status(401).json({
          title: "unauthorized",
          message: "Invalid reset password link."
        });
      }
      const existingUser = await User.findOne({ email: decoded.email });
      if (!existingUser) {
        return res.status(400).json({ message: "User does not exist." });
      }
      res.redirect(`https://go-platform.com/en/changePassword/?token=${token}`);
    });
  } catch (error) {
    console.log(error);
    res.status(505).json({ message: "Internal server error" });
  }
};

// Actually changes user password from reset link
const ChangeUserPassword = async (req, res) => {
  const token = req.headers.authorization.replace("Bearer ", "");
  const { password, confirmPassword } = req.body;

  if (password !== confirmPassword) {
    return res.status(400).json({ message: "Passwords do not match." });
  }
  try {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
          message: "token Expired!",
        });
      }
      const existingUser = await User.findOne({ email: decoded.email });
      if (!existingUser) {
        return res.status(400).json({ message: "User does not exist." });
      }
      const isSamePassword = await bcrypt.compare(
        password,
        existingUser.password
      );
      if (isSamePassword) {
        return res
          .status(400)
          .json({ message: "New password cannot be the same as old password" });
      }
      existingUser.password = await bcrypt.hash(password, 12);
      existingUser.save();
      res.status(200).json({ message: "Password changed successfully!" });
    });
  } catch (error) {
    console.log(error);
    res.status(505).json({ message: "Internal server error" });
  }
};

// Updates user profile information
const updateRecruiterData = async (req, res) => {
  const token = req.cookies.user;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      const user = await User.findOne({ _id: decoded.id });
      if (!user) {
        return res.status(400).json({ message: "User does not exist." });
      }

      const { first_name, last_name, phone_nbr } = req.body;
      user.first_name = first_name;
      user.last_name = last_name;
      user.phone_nbr = phone_nbr;

      user.save();
      res.status(200).json({ message: "User updated successfully!" });
    } catch (error) {
      console.log(error);
      res.status(505).json({ message: "Internal server error" });
    }
  });
};

// Changes password for logged-in user
const updatePassword = async (req, res) => {
  const token = req.cookies.user;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      const user = await User.findOne({ _id: decoded.id });
      if (!user) {
        return res.status(400).json({ message: "User does not exist." });
      }
      const { oldPassword, newPassword } = req.body;

      const isSamePassword = await bcrypt.compare(oldPassword, user.password);
      if (!isSamePassword) {
        return res
          .status(400)
          .json({ oldPassword: "Old password is incorrect." });
      }
      user.password = await bcrypt.hash(newPassword, 12);
      user.save();
      res.status(200).json({ message: "Password updated successfully!" });
    } catch (error) {
      console.log(error);
      res.status(505).json({ message: "Internal server error" });
    }
  });
};


// Checks if user is currently logged in
const checkLogin = async (req, res) => {
console.log('req.cookies');
console.log(req.cookies);


  if (req.cookies.admin) {
    return res.send({ isLogged: "admin" });
  } else if (req.cookies.user 
    
  ) {
    try {
      const decoded = jwt.verify(req.cookies.user, process.env.SECRET_WORD);
      console.log("Decoded user:", decoded);
     
      let company =null;
      if (decoded.company_name) { company = await Company.findOne({ name: decoded.company_name });
console.log("Company found:", company);

    }
   else if(decoded.company_id) { company = await Company.findOne({ _id: decoded.company_id });
      console.log("Company found:", company);
    }
      
     else if (!company) {
        return res.status(404).json({ message: "Company not found." });
      }

      return res.send({
        isLogged: "user",
        cookie: req.cookies.user,
        id: decoded.id,
        premium: company.plan !== "free",
      });
    } catch (err) {
      console.error("JWT verification error:", err);
      return res.status(401).json({ message: "Invalid or expired token." });
    }
  } else {
    return res.send({ isLogged: "none" });
  }
};




const upgradePlan = async (req, res) => {
  let email = "<EMAIL>";
  let recruiter = await User.findOne({ email: email });

  let company = await Company.findOne({ name: recruiter.company_name[0] });

  var currentTimestamp = Date.now();
  var twomonthInMillis = 2 * 30 * 24 * 60 * 60 * 1000;
  var endOfSubscriptionTimestamp = currentTimestamp + twomonthInMillis;

  company.plan = "premium";
  company.endOfSubscription = endOfSubscriptionTimestamp;
  company.isVerified = true;

  // await recruiter.save();
  await company.save();
  console.log({ recruiter, company });
};
module.exports = {
  signin,
  redirectNewUser,
  signup,
  getUserInfo,
  getAllUsers,
  getCompanies,
  ChangePasswordEmail,
  ChangePassword,
  ChangeUserPassword,
  updateRecruiterData,
  updatePassword,
  checkLogin,
  upgradePlan,
};
