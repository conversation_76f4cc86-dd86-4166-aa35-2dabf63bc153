const User = require("../models/user");
const jwt = require("jsonwebtoken");
const mongoose = require("mongoose");

const getToltp = async (req, res) => {
  let token = req.cookies.user;

  if (req.cookies.user) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      //console.log(decoded.id);
      try {
        let User_toltip = await User.findOne({ _id: decoded.id });
        if (User_toltip.tooltip == null || User_toltip.tooltip == "") {
          User_toltip.tooltip = 0;

          // Save the updated user
          await User_toltip.save();
        }
        res.status(200).json(User_toltip.tooltip);
      } catch (error) {
        console.error(error);
        res.status(500).json({ message: error.message });
      }
    });
  }
};

const postToltp = async (req, res) => {
  let token = req.cookies.user;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {
        let userTooltip = await User.findOne({ _id: decoded.id });

        if (!userTooltip) {
          return res.status(404).json({ message: "User not found" });
        }

        userTooltip.tooltip = 1;

        // Save the updated user
        const updatedUserTooltip = await userTooltip.save();
        res
          .status(200)
          .send({ message: "User updated successfully", updatedUserTooltip });
      } catch (error) {
        console.error(error);
        res.status(500).json({ message: error.message });
      }
    });
  } else {
    res.status(400).json({ message: "No token provided" });
  }
};

const postToltp2 = async (req, res) => {
  let token = req.cookies.user;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {
        let userTooltip = await User.findOne({ _id: decoded.id });

        if (!userTooltip) {
          return res.status(404).json({ message: "User not found" });
        }

        userTooltip.tooltip = 2;

        // Save the updated user
        const updatedUserTooltip = await userTooltip.save();
        res
          .status(200)
          .send({ message: "User updated successfully", updatedUserTooltip });
      } catch (error) {
        console.error(error);
        res.status(500).json({ message: error.message });
      }
    });
  } else {
    res.status(400).json({ message: "No token provided" });
  }
};

module.exports = {
  getToltp,
  postToltp,
  postToltp2,
};
