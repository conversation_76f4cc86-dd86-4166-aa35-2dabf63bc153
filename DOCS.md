## GET API KEY

endpoint: http://localhost:3000/api/api-key
METHOD: GET

Include the user cookie in the request after signing up.

NOTE: the cookie last 24hours, you need to signup after that to be able to continue.

Response example:

{
"apiKey": "YOUR_API_KEY",
}

## FETCH ASSESSMENTS WITH PAGINATION

http://localhost:3000/api/assessments
METHOD: GET.

QUERY PARAMETERS: (NOT REQUIRED)
page: (Default: 1)
limit: (Default: 10)
category: (Pick one of the following: ["Hard Skills", "Psychometrics", "Soft Skills"])
search: ("Search assessment by name")

Authorization:
Bearer token. ( include your API key in each request)

Response example:

[
{
"_id": "67866f15091ebfa0e3267088",
"company": "M-Forte",
"name": "BD Assessment - 2",
"category": "Hard Skills",
"rating": "",
"description_test": "Sales Experience",
"instructions": "",
"important": "",
"duration": null,
"__v": 0
},
]

## CREATE A NEW PROJECT

Endpoint: http://localhost:3000/api/new-project
METHOD: POST

BODY:

Authorization:
Bearer token. ( include your API key in each request)
