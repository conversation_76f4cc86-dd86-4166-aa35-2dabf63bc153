const InvitedCandidate = require("../models/InvitedCandidate");
const Invitation = require("../models/Invitations");
const companyProject = require("../models/companyProject");
const CandidateEvaluation = require("../models/CandidateEvaluation");
const jwt = require("jsonwebtoken");


// Create a new invited candidate
exports.createInvitedCandidate = async (req, res) => {
    const data = req.body;
    const decoded = req.user; // Get user data from middleware

    try {
        const candidate = await InvitedCandidate.findOne({ email: data.email });
        if (!candidate) {
            const newInvitedCandidate = new InvitedCandidate({
                UserId: decoded.id,
                first_name: data.first_name,
                last_name: data.last_name,
                email: data.email,
            });
            const savedCandidate = await newInvitedCandidate.save();
            res.status(201).json(savedCandidate);
        } else {
            console.log("candidate", candidate);
            this.incrementCounter(candidate._id);
            return res.status(201).json(candidate);
        }
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Get all invited candidates with specific conditions
exports.getAllInvitedCandidates = async (req, res) => {
    const decoded = req.user; // Get user data from middleware

    try {
        const candidates = await InvitedCandidate.find({
            UserId: decoded.id,
            invitedCounter: { $gte: 2 }
        }).sort({ invitedCounter: -1 }).limit(4);
        res.status(200).json(candidates);
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// Get ALL invited candidates for a specific project with assessment status
exports.getAllInvitedCandidatesComplete = async (req, res) => {
    const decoded = req.user;
    const { projectId } = req.query;

        if (!projectId) {
            return res.status(400).json({ message: "Project ID is required" });
        }

        try {
            // Verify project belongs to user's company
            const project = await companyProject.findOne({
                _id: projectId,
                company_name: decoded.company_name
            });

            if (!project) {
                return res.status(403).json({ message: "Project not found or access denied" });
            }

            // Get candidates from both collections and their assessment status
            const [newCandidates, historicalInvitations, assessmentResults] = await Promise.all([
                InvitedCandidate.find({ UserId: decoded.id, projectId }),
                Invitation.find({ projectId }),
                CandidateEvaluation.find({ projectId })
            ]);

            // Create assessment status map for quick lookup
            const assessmentMap = new Map();
            assessmentResults.forEach(assessment => {
                assessmentMap.set(assessment.candidate.toLowerCase(), {
                    status: assessment.status,
                    passed: assessment.passed,
                    submittedAt: assessment.createdAt,
                    assessmentId: assessment._id
                });
            });

            // Helper function to get assessment status
            const getAssessmentStatus = (email) => {
                const assessment = assessmentMap.get(email.toLowerCase());
                if (!assessment) {
                    return {
                        assessmentStatus: 'not_started',
                        assessmentPassed: null,
                        assessmentSubmittedAt: null
                    };
                }

                return {
                    assessmentStatus: assessment.status,
                    assessmentPassed: assessment.passed,
                    assessmentSubmittedAt: assessment.submittedAt
                };
            };

            // Create a map to avoid duplicates (prioritize new candidates)
            const candidateMap = new Map();

            // Add new candidates first
            newCandidates.forEach(candidate => {
                const assessmentInfo = getAssessmentStatus(candidate.email);
                candidateMap.set(candidate.email.toLowerCase(), {
                    _id: candidate._id,
                    first_name: candidate.first_name,
                    last_name: candidate.last_name,
                    email: candidate.email,
                    projectId: candidate.projectId,
                    source: 'new',
                    ...assessmentInfo
                });
            });

            // Add historical candidates only if not already present
            historicalInvitations.forEach(invitation => {
                const emailKey = invitation.Email.toLowerCase();
                if (!candidateMap.has(emailKey)) {
                    const assessmentInfo = getAssessmentStatus(invitation.Email);
                    candidateMap.set(emailKey, {
                        _id: invitation._id,
                        first_name: invitation.first_name,
                        last_name: invitation.last_name,
                        email: invitation.Email,
                        projectId: invitation.projectId,
                        source: 'historical',
                        ...assessmentInfo
                    });
                }
            });

            // Convert to array and sort by email alphabetically
            const allCandidates = Array.from(candidateMap.values())
                .sort((a, b) => a.email.localeCompare(b.email));

            console.log(`Found ${allCandidates.length} candidates (${newCandidates.length} new, ${historicalInvitations.length} historical)`);

            res.status(200).json(allCandidates);

        } catch (error) {
            console.error("Error in getAllInvitedCandidatesComplete:", error);
            res.status(500).json({ message: error.message });
        }
};


// Increment invitation counter for a candidate
exports.incrementCounter = async (id) => {
    try {
        const fetchedCandidate = await InvitedCandidate.findById(id);
        if (!fetchedCandidate) {
            return { status: 404, message: "Invited candidate not found" };
        }
        fetchedCandidate.invitedCounter++;
        const updatedCandidate = await fetchedCandidate.save();

        return { status: 200, data: updatedCandidate };
    } catch (err) {
        return { status: 500, message: err.message };
    }
};

// Delete an invited candidate by ID
exports.deleteInvitedCandidate = async (req, res) => {
    try {
        const deletedCandidate = await InvitedCandidate.findByIdAndDelete(req.params.id);
        if (!deletedCandidate) {
            return res.status(404).json({ message: "Invited candidate not found" });
        }
        res.status(200).json({ message: "Invited candidate deleted successfully" });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};
