const mongoose = require("mongoose");
const ReferralSchema = mongoose.Schema({
    preference: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Preference',
        required: true
    },
    full_name: {
        type: String,
        required: true
    },
    relationship: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    }
},{ _id: false });
const Referral = mongoose.model('Referral', ReferralSchema);
module.exports = Referral