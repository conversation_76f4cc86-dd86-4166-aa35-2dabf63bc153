const Experience = require("../models/Experience");
const mongoose = require('mongoose');
const jwt = require("jsonwebtoken");
// Create a new experience
const addExperience = async (req, res) => {
    const { title, type, company, startingDate, endingDate, level, industry, description, currentlyWorkingHere } = req.body;
    let user_token = req.cookies.candidate;
    if (user_token) {
        jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            try {
                const newExperience = new Experience({
                    candidate: decoded.id,  // Assuming req.user is populated from JWT middleware
                    title,
                    type,
                    company,
                    startingDate,
                    endingDate,
                    level,
                    industry,
                    description,
                    currentlyWorkingHere
                });

                const savedExperience = await newExperience.save();
                res.status(201).json({ message: 'Experience added successfully', savedExperience });
            } catch (error) {
                console.error('Error adding experience:', error);
                res.status(500).json({ error: 'Internal server error' });
            }
        })
    }
};

// Get all experiences for a user
const getExperiencesByCandidate = async (req, res) => {
    try {
        let token = req.cookies.candidate;

        jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            const experiences = await Experience.find({ candidate: mongoose.Types.ObjectId(decoded.id) });

            if (!experiences || experiences.length === 0) {
                return res.status(204).json({ message: "No experiences found." });
            }

            res.status(200).json(experiences);
        })
    } catch (error) {
        console.error('Error retrieving experiences:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Get experience by ID
const getExperienceById = async (req, res) => {
    const experienceId = req.params.id;

    try {
        const experience = await Experience.findById(experienceId);

        if (!experience) {
            return res.status(204).json({ message: "No experience found." });
        }

        res.status(200).json(experience);
    } catch (error) {
        console.error('Error retrieving experience:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Update an experience by ID
const updateExperienceById = async (req, res) => {
    const experienceId = req.params.id;

    try {
        const experience = await Experience.findById(experienceId);

        if (!experience) {
            return res.status(404).json({ message: "Experience not found." });
        }

        // Update fields if provided
        const { title, type, company, startingDate, endingDate, level, industry, description, currentlyWorkingHere } = req.body;

        if (title) experience.title = title;
        if (type) experience.type = type;
        if (company) experience.company = company;
        if (startingDate) experience.startingDate = startingDate;
        if (level) experience.level = level;
        if (description) experience.description = description;
        if (industry) experience.industry = industry;
        if (endingDate) experience.endingDate = endingDate;
        if (typeof currentlyWorkingHere !== 'undefined') experience.currentlyWorkingHere = currentlyWorkingHere;

        const updatedExperience = await experience.save();
        res.status(200).json({ message: "Experience updated successfully", updatedExperience });
    } catch (error) {
        console.error('Error updating experience:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Delete an experience by ID
const deleteExperienceById = async (req, res) => {
    const experienceId = req.params.id;

    try {
        const experience = await Experience.findByIdAndDelete(experienceId);

        if (!experience) {
            return res.status(204).json({ message: "No experience found." });
        }

        res.status(200).json({ message: "Experience deleted successfully", deletedExperience: experience });
    } catch (error) {
        console.error('Error deleting experience:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

module.exports = {
    addExperience,
    getExperiencesByCandidate,
    getExperienceById,
    updateExperienceById,
    deleteExperienceById,
};
