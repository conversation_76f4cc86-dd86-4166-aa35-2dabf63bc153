const Candidate = require("../models/Candidate");
const bcryptjs = require("bcrypt");

exports.isValidCandidate = async (Email, Password) => {
  let candidateFound = await Candidate.findOne({ Email });
  if (!candidateFound) return false;

  const correctPassword = await bcryptjs.compare(
    Password,
    candidateFound.Password
  );
  if (!correctPassword) return false;
  return true;
};

exports.saveCandidate = async (candidateData) => {
  try {
    let candidateModel = new Candidate(candidateData);
    // const salt = await bcryptjs.genSalt(10);
    // candidateModel.Password = await bcryptjs.hash(
    //   candidateModel.Password,
    //   salt
    // );
    const savedCandidate = await candidateModel.save();
    return savedCandidate;
  } catch (error) {
    console.error(error);
    throw new Error("Failed to save candidate");
  }
};

exports.findCandidate = async (Email) => {
  let candidateFound = await Candidate.findOne({ Email });
  return candidateFound;
};

exports.searchCandidates = async (firstLettersCandidate) => {
  rgx = new RegExp("^" + firstLettersCandidate);
  let candidates = await Candidate.find({ First_name: rgx });
  return candidates.map((candidate) => {
    return { value: candidate._id, label: candidate.First_name };
  });
};

exports.getCandidateWithCompany = async (candidateId) => {
  let candidateWithCompany = await Candidate.aggregate([
    {
      $match: { _id: mongoose.Types.ObjectId(candidateId) },
    },
    {
      $lookup: {
        from: "companies",
        localField: "_id",
        foreignField: "candidate",
        as: "Company",
      },
    },
  ]);

  return candidateWithCompany;
};
