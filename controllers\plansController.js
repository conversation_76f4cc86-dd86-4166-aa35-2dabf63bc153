const { verifyToken } = require("../helper/verifyToken");
const Company = require("../models/Company");
const choosePlan = async (req, res) => {
  const token = req.cookies.user;
  const planPicked = req.body;

  const decoded = await verifyToken(token);
  const company = await Company.findOne({ name: decoded.company_name });
  console.log({ company });

  switch (planPicked.title) {
    case "free":
      company.plan = "free";
      await company.save();
      break;
    case "basic":
      company.recruiters_count = 3;
      company.credit += 50;
      company.plan = "basic";
      company.upgradeDate = Date.now();
      await company.save();
      break;
    case "pro":
      company.recruiters_count = 5;
      company.credit += 300;
      company.plan = "pro";
      company.upgradeDate = Date.now();
      await company.save();
      break;
    case "custom":
      company.plan = "custom";
      break;
    default:
      break;
  }
  res.json({
    plans: {
      name: "free",
    },
  });
};

module.exports = {
  choosePlan,
};
