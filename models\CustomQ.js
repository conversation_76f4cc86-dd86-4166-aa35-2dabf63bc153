const mongoose = require('mongoose');

const CustomQSchema = new mongoose.Schema({
  company: {
    type: String,
    required: false,
  },
  name: {
    type: String,
    required: true,
    unique: true,
  },
  category: {
    type: String,
    required: true,
  },
  time: {
    type: Number,
  },
  description: {
    type: String,
    default: "",
  },
  question: {
    type: String,
    default: "",
  },
  options: {
    type: Object,
    default: "",
  },

});

const CustomQ = mongoose.model('CustomQ', CustomQSchema);

module.exports = CustomQ;