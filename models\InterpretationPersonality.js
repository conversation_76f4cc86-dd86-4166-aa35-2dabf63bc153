const mongoose = require("mongoose");

// Define the schema for storing assessment results
const InterpretationSchema = new mongoose.Schema({
    assessment_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "allAssessments",
    },
    traits: [
        {
            trait_name: {
                type: String, // Specifying the type for trait name

            },
            definition: {
                type: String,
            },
            scores: [
                {
                    status: {
                        type: String,
                        enum: ['Very Low', 'Low', 'Medium', 'High', 'Very High']
                    },
                    description: {
                        type: String,
                    },
                    relationship: {
                        type: String,

                    },
                    at_work: {
                        type: String,

                    },
                    attributes: {
                        type: String,

                    },
                    challenges: {
                        type: String,

                    },
                    tips: {
                        type: String,

                    },
                    discussion: {
                        type: String,

                    },
                }
            ]

        }
    ]




});

// Create a model for the assessment results
const Interpretation = mongoose.model(
    "Interpretation",
    InterpretationSchema
);

module.exports = Interpretation;
