const express = require('express');
const router = express.Router();
const portfolioController = require('../controllers/PortfolioController');

// Create a new portfolio
router.post('/', portfolioController.createPortfolio);

// Get all portfolios
router.get('/', portfolioController.getPortfolioByCandidate);

// Get a single portfolio by ID
router.get('/:id', portfolioController.getPortfolioById);

// Update a portfolio by ID
router.put('/:id', portfolioController.updatePortfolio);

// Delete a portfolio by ID
router.delete('/:id', portfolioController.deletePortfolio);

module.exports = router;
