const mongoose = require("mongoose");

const validateTrait = function (value) {
  return this.traits.includes(value);
};

const allAssessments = mongoose.Schema({
  company: {
    type: String,
    required: false,

  },
  name: {
    type: String,
    default: "",
  },
  category: {
    type: String,
    default: "",
  },
  rating: {
    type: String,
    default: "",
  },
  description_test: {
    type: String,
    default: "",
  },
  instructions: {
    type: String,
    default: "",
  },
  important: {
    type: String,
    default: "",
  },
  duration: {
    type: Number,
    default: "",
  },
  questions_list: [
    {
      title: {
        type: String,
        default: "",
      },
      category: {
        type: String,
        default: "",
      },
      time: {
        type: Number,
      },
      trait: {
        type: String,
        default: "",
      },
      question_number: {
        type: Number,
      },
      description: {
        type: String,
      },
      question: {
        type: String,
        default: "",
      },
      code: {
        type: String,
        default: "",
      },
      options: {
        type: Object,
        default: "",
      },
    },
  ],
  answers: {
    type: Object,
  },
  ranges: {
    type: Object,
  },
});

module.exports = mongoose.model("allAssessments", allAssessments);
