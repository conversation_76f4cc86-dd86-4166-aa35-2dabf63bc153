const RecruitedCandidate = require("../models/recruited_candidate");

// @desc Create a new recruited candidate
// @route POST /api/recruited-candidates
exports.createRecruitedCandidate = async (req, res) => {
    try {
        const { user, company, job } = req.body;

        const newRecruitedCandidate = new RecruitedCandidate({
            user,
            company,
            job
        });

        await newRecruitedCandidate.save();
        res.status(201).json({ message: "Recruited candidate added successfully", newRecruitedCandidate });
    } catch (error) {
        res.status(500).json({ error: "Server error", details: error.message });
    }
};

// @desc Get all recruited candidates for a company
// @route GET /api/recruited-candidates/:companyId
exports.getRecruitedCandidatesForCompany = async (req, res) => {
    try {
        const { companyId } = req.params;
        const candidates = await RecruitedCandidate.find({ company: companyId }).populate('user job');

        res.status(200).json(candidates);
    } catch (error) {
        res.status(500).json({ error: "Server error", details: error.message });
    }
};

// @desc Get a specific recruited candidate for a company
// @route GET /api/recruited-candidates/:companyId/:userId
exports.getRecruitedCandidateForCompany = async (req, res) => {
    try {
        const { companyId, userId } = req.params;
        const candidate = await RecruitedCandidate.findOne({ company: companyId, user: userId }).populate('user job');

        if (!candidate) {
            return res.status(404).json({ message: "Candidate not found" });
        }

        res.status(200).json(candidate);
    } catch (error) {
        res.status(500).json({ error: "Server error", details: error.message });
    }
};
