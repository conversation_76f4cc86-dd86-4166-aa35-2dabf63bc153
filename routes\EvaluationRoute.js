const express = require("express"); 

const API = require("../controllers/CandidateEvaluationCodeController.js"); 

const router = express.Router();

// POST /api/evaluate
router.post("/evaluate", API.evaluateSubmission);
router.get("/candidate-evaluations", API.getCandidateEvaluations);
// Call with: /candidate-evaluations?candidateId=YOUR_ID&idProject=PROJECT_ID

// Add more routes as needed
// router.get("/history", evaluationController.getEvaluationHistory);

module.exports = router; 