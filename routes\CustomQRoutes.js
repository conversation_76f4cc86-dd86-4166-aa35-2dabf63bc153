const express = require("express");
const {
    addCustomQ,
    getCustomQ,
    getCustomQById,
    getCustomQByCategory,
    deleteCustomQById,
    updateCustomQById,
    getCustomQByUserId

} = require("../controllers/CustomQController");
const router = express.Router();

router.post("/add", addCustomQ);
router.get("/get", getCustomQ);
router.get("/get/:id", getCustomQById);
router.get("/get/category/:cat", getCustomQByCategory);
router.get("/company", getCustomQByUserId);
router.delete("/delete/:id", deleteCustomQById);
router.put("/update/:id", updateCustomQById);


module.exports = router;