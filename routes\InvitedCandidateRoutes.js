const express = require("express");
const router = express.Router();
const invitedCandidateController = require("../controllers/InvitedCandidateController");
const authentication = require("../middleware/authentication"); 

// Create a new invited candidate
router.post("/", authentication, invitedCandidateController.createInvitedCandidate);

// Get all invited candidates (most invited - counter >= 2)
router.get("/", authentication, invitedCandidateController.getAllInvitedCandidates);

// Get ALL invited candidates 
router.get("/all", authentication, invitedCandidateController.getAllInvitedCandidatesComplete);

// Update an invited candidate by ID
router.put("/:id", invitedCandidateController.incrementCounter);

// Delete an invited candidate by ID
router.delete("/:id", invitedCandidateController.deleteInvitedCandidate);

module.exports = router;
