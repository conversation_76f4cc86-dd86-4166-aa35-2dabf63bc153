const Job = require("../models/job");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");
const jwt = require("jsonwebtoken");
const validator = require("validator");
const getJobs = async (req, res) => {
  let jobs = await Job.find({});
  let images = [];
  let image = {
    jobId: "",
    imgData: "",
  };

  await Promise.all(
    jobs.map(async (job) => {
      file = await Photo_Files.findOne({
        _id: job.imgFileId,
      });
      if (file) {
        job.imgFileId = "";
        image.jobId = job._id;
        console.log({ files_id: file.id });
        let imgChunks = await Photo_chunks.find({
          files_id: file._id,
        });
        imgChunks.map((chunk) => {
          job.imgFileId += chunk.data.toString("base64");
        });
      }
    })
  );

  res.send(jobs);
  // .then((data) => {
  //   res.json(data);
  // })
  // .catch((err) => {
  //   console.log(err).sendStatus(500);
  // });
};

const postJob = async (req, res) => {
  // if (!req.file) {
  //   return res.send({ msg: "no image selected" });
  // }
  // upload.single("imgpath");

  const jobRecieved = req.body;
  console.log({ jobRecieved });
  const job = new Job(jobRecieved);
  console.log({ job });

  await job.save();
  res.send("job created");
};

const postJobByCompanyName = async (req, res) => {
  // if (!req.file) {
  //   return res.send({ msg: "no image selected" });
  // }
  // upload.single("imgpath");

  const jobRecieved = req.body;
  console.log({ jobRecieved });
  const job = new Job(jobRecieved);
  console.log({ job });

  await job.save();
  res.send("job created");
};

const getJobsByCompanyId = async (req, res) => {
  let token = req.cookies.user;

  if (req.cookies.user) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      const company_name = decoded.company_name;
      console.log({ company_name });
      try {
        if (!company_name)
          return res.status(400).json({ message: "Company id required." });

        let jobs = await Job.find({ company_name: company_name });
        console.log({ jobs });
        res.send(jobs);
      } catch (error) {
        // Handle any unexpected errors
        console.error(error);
        res.status(500).json({ message: "Internal server error." });
      }
    });
  }
};

const postNewJob = async (req, res) => {
  const token = req.cookies.user;
  console.log({ token });
  if (req.cookies.user) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      const company_name = decoded.company_name;
      console.log({ company_name });
      if (!company_name)
        return res.status(400).json({ message: "Company id required." });

      const jobRecieved = req.body;
      console.log({ jobRecieved });
      const jobToSave = {
        ...jobRecieved,
        company_name: company_name,
      };
      const job = new Job(jobToSave);
      console.log({ job });

      await job.save();
      res.send("job created");
    });
  }
};

module.exports = {
  getJobs,
  postJob,
  postJobByCompanyName,
  getJobsByCompanyId,
  postNewJob,
};
