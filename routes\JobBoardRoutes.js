const express = require('express');
const router = express.Router();
const jobBoardController = require('../controllers/JobBoardController');

// Create a new job board
router.post('/create', jobBoardController.createJobBoard);

// Get all job boards
router.get('/jobs', jobBoardController.getJobBoards);

// Get a single job board by ID
router.get('/:id', jobBoardController.getJobBoardById);

// Update a job board by ID
router.put('/:id', jobBoardController.updateJobBoard);

// Delete a job board by ID
router.delete('/:id', jobBoardController.deleteJobBoard);

module.exports = router;
