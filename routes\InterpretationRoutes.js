const express = require('express');
const router = express.Router();
const {
    createInterpretation,
    getAllInterpretations,
    getInterpretationById,
    updateInterpretation,
    deleteInterpretation,
    getScoresForTraits
} = require('../controllers/InterpretationController');

// Route to create a new interpretation
router.post('/', createInterpretation);

router.post('/scores/:assessmentName', getScoresForTraits);


// Route to get all interpretations
router.get('/', getAllInterpretations);

// Route to get a single interpretation by ID
router.get('/:id', getInterpretationById);

// Route to update an interpretation by ID
router.put('/:id', updateInterpretation);

// Route to delete an interpretation by ID
router.delete('/:id', deleteInterpretation);

module.exports = router;
