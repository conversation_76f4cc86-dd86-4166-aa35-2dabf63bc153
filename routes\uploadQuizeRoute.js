const express = require("express");
const router = express.Router();
const {
  uploadAssessment,
  updateAssessment,
  getEmptyAssessments,
  setDuration,
  getAllAssess,
  getAssessmentById,
  deleteAssessment
} = require("../controllers/uploadQuizeController.js");

router.post("/", uploadAssessment);
router.get("/all", getAllAssess);
router.get("/:id", getAssessmentById);
router.delete("/:id", deleteAssessment);
router.get("/", getEmptyAssessments);
router.put("/:id", updateAssessment);
router.put("/duration/:id", setDuration);

module.exports = router;
