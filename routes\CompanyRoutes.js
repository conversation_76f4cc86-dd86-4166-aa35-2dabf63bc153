const express = require("express");
const router = express.Router();
const CompanyController = require("../controllers/CompanyController");
const authentication = require("../middleware/authentication");
const upload = require("../middleware/imageUpload");
router.post(
  "/post",
  upload.fields([{ name: "logo" }, { name: "cover" }]),
  CompanyController.createCompany
);
router.post(
  "/register",
  upload.single("logo"),
  CompanyController.registerCompany
);

router.post(
  "/registerAccount",
  CompanyController.register
);


router.post(
  "/registerCompany",
  CompanyController.registerComplete
);

router.get("/fetchcompanies", CompanyController.fetchcompanies);

router.get("/verify", CompanyController.verifyCompany);
router.get("/invitedCoworker", CompanyController.redirectTeammate);
router.put("/update", CompanyController.updateCompany);
router.get("/getCredit", CompanyController.getCredit);
router.get("/info", CompanyController.getCompanyInfo);
router.get("/candidates", authentication, CompanyController.getCandidates);
router.get(
  "/archivedCandidates",
  authentication,
  CompanyController.getArchivedCandidates
);

router.get(
  "/InvitedCandidates",
  authentication,
  CompanyController.getInvitedCandidates
);

router.get(
  "/invitations",
  authentication,
  CompanyController.getInvitedCandidates
);
router.get(
  "/bestCandidate/:projectId",
  // authentication,
  CompanyController.getBestCandidate
);
router.get("/companyLogo", authentication, CompanyController.companyLogo);
router.post("/changeLogo", upload.single("logo"), CompanyController.changeLogo);
router.post(
  "/changeCover",
  upload.single("cover"),
  CompanyController.changeCover
);
router.post("/fetchTeamates",authentication, CompanyController.getCompanyTeamates);
router.get("/search", authentication, CompanyController.searchCompanies);
router.post("/DeleteTeamates", CompanyController.deleteTeamate);
router.post("/UpdateTeamates", CompanyController.UpdateTeamate);

router.post(
  "/inviteTeammate",
  authentication,
  CompanyController.inviteTeammate
);
router.post("/switchCompany", authentication, CompanyController.switchCompany);
router.post(
  "/companyFeedback",
  authentication,
  CompanyController.companyFeedback
);

router.post("/updateCredit", authentication, CompanyController.updateCredit);
router.delete("/deletecompany/:companyID", CompanyController.deleteCompany);
router.get("/:companyId", CompanyController.getCompanyWithTeam);

module.exports = router;
