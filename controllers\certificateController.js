const fs = require("fs");
const path = require("path");
const ejs = require("ejs");
const { PDFDocument } = require("pdf-lib");
const pdf = require("html-pdf");
const puppeteer = require("puppeteer");
const validator = require("validator");
var QRCode = require("qrcode");
const jwt = require("jsonwebtoken");

const generateCertificate = async (req, res) => {
  const fullName = req.query.fullName;
  let token = req.cookies.candidatecookie;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    console.log({ decodedFROMCERTGENERATION: decoded });
    try {
      // Read the EJS certificate file
      const certificateTemplate = fs.readFileSync(
        path.join(__dirname, "../views/talentsCertificate.ejs"),
        "utf-8"
      );
      // const imagePath = path.join(__dirname, "../views/price.png");
      const targetURL = `https://go-platform.com/talent/${token}`;
      const qrcode = await generateQRCode(targetURL);
      // Render the EJS template with data
      const renderedCertificate = ejs.render(certificateTemplate, {
        data: { name: fullName, qrcode },
      });

      // Create a temporary HTML file with the rendered content
      const tempHTMLFilePath = path.join(__dirname, "tempCertificate.html");
      fs.writeFileSync(tempHTMLFilePath, renderedCertificate, "utf-8");

      // Define PDF options (adjust as needed)
      const pdfOptions = {
        format: "A4",
        orientation: "landscape", // Set orientation to landscape
        border: "0", // Set a border for the whole page
        margin: { top: "0", right: "0", bottom: "0", left: "0" }, // Set all margins to zero

        zoomFactor: "1",
      };

      // Convert HTML to PDF
      pdf.create(renderedCertificate, pdfOptions).toBuffer((err, buffer) => {
        if (err) {
          console.error("Error generating PDF:", err);
          return res.status(500).send("Error generating PDF");
        }

        // Set the response headers for file download
        res.setHeader("Content-Type", "application/pdf");
        res.setHeader(
          "Content-Disposition",
          'attachment; filename="certificate.pdf"'
        );

        // Send the PDF file to the client
        res.end(buffer);
      });

      // Cleanup: Remove the temporary HTML file
      fs.unlinkSync(tempHTMLFilePath);
    } catch (error) {
      console.error("Error generating certificate:", error);
      res.status(500).send("Error generating certificate");
    }
  });
};

const generateCertificateImg = async (req, res) => {
  const fullName = req.query.fullName;
  let token = req.cookies.candidatecookie
    ? req.cookies.candidatecookie
    : req.body.token;
  console.log({ token, reqCOOKIIIIIIIE: req.cookies });
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    console.log({ decodedFROMCERTGENERATION: decoded });
    const targetURL = `https://go-platform.com/talent/${token}`;
    const qrcode = await generateQRCode(targetURL);

    console.log({ fullName });
    console.log({ qrcode });
    try {
      const browser = await puppeteer.launch({
        headless: "new",
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
      });
      const page = await browser.newPage();

      // Read the EJS certificate file
      const certificateTemplate = fs.readFileSync(
        path.join(__dirname, "../views/talentsCertificate.ejs"),
        "utf-8"
      );

      // Render the EJS template with data
      const renderedCertificate = ejs.render(certificateTemplate, {
        data: { name: fullName, qrcode },
      });

      // Set the HTML content of the page
      await page.setContent(renderedCertificate);

      // Generate a screenshot of the page
      const screenshot = await page.screenshot();

      const folderPath = path.join(__dirname, "../images/certificates");
      const certificateFileName = `${fullName}.png`;

      fs.writeFileSync(path.join(folderPath, certificateFileName), screenshot);

      // Set the response headers for image download
      res.setHeader("Content-Type", "image/png");
      res.setHeader("Cache-Control", "public, max-age=3600");
      res.setHeader("Access-Control-Allow-Origin", "*");
      // res.setHeader(
      //   "Content-Disposition",
      //   'attachment; filename="certificate.png"'
      // );

      // Send the image to the client
      res.end(screenshot);

      // Cleanup: Close the browser
      await browser.close();
    } catch (error) {
      console.error("Error generating certificate image:", error);
      res.status(500).send("Error generating certificate image");
    }
  });
};

const generateQRCode = (data) => {
  return new Promise((resolve, reject) => {
    QRCode.toDataURL(data, (err, url) => {
      if (err) {
        reject(err);
      } else {
        resolve(url);
      }
    });
  });
};

module.exports = {
  generateCertificate,
  generateCertificateImg,
};
