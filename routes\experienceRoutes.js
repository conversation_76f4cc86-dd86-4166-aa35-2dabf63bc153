// routes/experienceRoutes.js
const express = require("express");
const router = express.Router();
const {
    addExperience,
    getExperiencesByCandidate,
    getExperienceById,
    updateExperienceById,
    deleteExperienceById,
} = require("../controllers/ExperienceController");

// Create a new experience
router.post("/", addExperience);

// Get all experiences for a user
router.get("/", getExperiencesByCandidate);

// Get experience by ID
router.get("/:id", getExperienceById);

// Update an experience by ID
router.put("/:id", updateExperienceById);

// Delete an experience by ID
router.delete("/:id", deleteExperienceById);

module.exports = router;
