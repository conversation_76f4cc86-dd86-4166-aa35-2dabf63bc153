const mongoose = require("mongoose");
const assessmentSchema = mongoose.Schema(
{
    challenge: { type: mongoose.Schema.Types.ObjectId, ref: 'Challenge', required: true },
    candidate: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    recruiter: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    codeSolution: { type: String, required: true },
    testResults: [{
      testCase: { type: mongoose.Schema.Types.ObjectId, required: true },
      passed: { type: Boolean, required: true },
      output: { type: String },
      executionTime: { type: Number }
    }],
    submittedAt: { type: Date, default: Date.now },
    status: { type: String, enum: ['pending', 'completed', 'evaluated'], default: 'pending' }
  });

  module.exports = mongoose.model("AssessmentCode", assessmentSchema);
    