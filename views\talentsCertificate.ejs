<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Certificate</title>
    <meta property="og:title" content="Your Article Title" />
    <meta property="og:description" content="Description of your article" />
    <meta
      property="og:image"
      content="http://localhost:3000/certificate/candidateCertificateImg"
    />
    <!-- Replace with the direct image URL -->
    <meta property="og:url" content="https://example.com/article" />
    <!-- Replace with your article URL -->
    <meta property="og:type" content="article" />
    <meta property="og:site_name" content="Your Website Name" />
  </head>
  <body style="padding: 0; margin: 0">
    <div
      class="StCertificate"
      style="
        width: 842px;
        height: 595px;
        overflow: hidden;
        position: relative;
        background: linear-gradient(128deg, #ddb4fb 0%, #fdba18 100%);
      "
    >
      <div
        class="Group2"
        style="
          width: 735.42px;
          height: 469.5px;
          left: -285px;
          top: -194px;
          position: absolute;
        "
      >
        <div
          class="Rectangle9"
          style="
            width: 473.47px;
            height: 175.25px;
            left: 161px;
            top: 236.73px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            background: #fac595;
          "
        ></div>
        <div
          class="Rectangle12"
          style="
            width: 274.57px;
            height: 65.27px;
            left: 465px;
            top: 226.28px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            border: 4px #d604da solid;
          "
        ></div>
        <div
          class="Rectangle10"
          style="
            width: 473.47px;
            height: 175.25px;
            left: 0px;
            top: 317.73px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            background: linear-gradient(269deg, #ffa246 0%, #6e21e0 100%);
          "
        ></div>
        <div
          class="Rectangle11"
          style="
            width: 274.57px;
            height: 101.63px;
            left: 182px;
            top: 370.28px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            border: 4px #d604da solid;
          "
        ></div>
      </div>
      <div
        class="Group3"
        style="
          width: 658.66px;
          height: 553.5px;
          left: 460px;
          top: 248px;
          position: absolute;
        "
      >
        <div
          class="Rectangle14"
          style="
            width: 473.47px;
            height: 175.25px;
            left: 0px;
            top: 401.73px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            background: #ddb4fb;
          "
        ></div>
        <div
          class="Rectangle13"
          style="
            width: 473.47px;
            height: 175.25px;
            left: 161px;
            top: 236.73px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            background: linear-gradient(269deg, #ffa246 0%, #6e21e0 100%);
          "
        ></div>
        <div
          class="Rectangle15"
          style="
            width: 274.57px;
            height: 101.63px;
            left: 105px;
            top: 288.28px;
            position: absolute;
            transform: rotate(-30deg);
            transform-origin: 0 0;
            border: 4px #d604da solid;
          "
        ></div>
      </div>
      <div
        class="Rectangle16"
        style="
          width: 730px;
          height: 490px;
          left: 56px;
          top: 52px;
          position: absolute;
          background: linear-gradient(161deg, #ddb4fb 0%, white 100%);
        "
      ></div>
      <div
        class="Group1"
        style="
          width: 351.46px;
          height: 237.46px;
          left: -36px;
          top: 397px;
          position: absolute;
        "
      >
        <div
          class="Rectangle17"
          style="
            width: 130px;
            height: 130px;
            left: 0px;
            top: 144.92px;
            position: absolute;
            transform: rotate(-45deg);
            transform-origin: 0 0;
            border: 3px #fdba18 solid;
          "
        ></div>
        <div
          class="Rectangle18"
          style="
            width: 81.26px;
            height: 81.26px;
            left: 35px;
            top: 57.46px;
            position: absolute;
            transform: rotate(-45deg);
            transform-origin: 0 0;
            border: 3px #fdba18 solid;
          "
        ></div>
        <div
          class="Rectangle19"
          style="
            width: 154.48px;
            height: 154.48px;
            left: 133px;
            top: 128.23px;
            position: absolute;
            transform: rotate(-45deg);
            transform-origin: 0 0;
            border: 3px #fdba18 solid;
          "
        ></div>
      </div>
      <img
        class="Union"
        style="
          width: 730px;
          height: 490px;
          left: 56px;
          top: 52px;
          position: absolute;
        "
        src="https://drive.google.com/uc?export=view&id=1MOg5pVwtXurRxLOTTYVo6gKbW8Ptzu-A"
      />
      <div
        class="SkillsValidationCertificate"
        style="
          left: 97px;
          top: 115px;
          position: absolute;
          color: #ac06f2;
          font-size: 22px;
          font-family: Montserrat;
          font-weight: 600;
          word-wrap: break-word;
        "
      >
        Skills Validation Certificate
      </div>
      <div
        class="PresentedTo"
        style="
          left: 97px;
          top: 165px;
          position: absolute;
          color: #272727;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 500;
          word-wrap: break-word;
        "
      >
        Presented to:
      </div>
      <div
        class="AbdelghaniBahri"
        style="
          left: 97px;
          top: 182px;
          position: absolute;
          color: #60b3ff;
          font-size: 26px;
          font-family: Montserrat;
          font-weight: 700;
          word-wrap: break-word;
        "
      >
        <%= data.name %>
      </div>
      <div
        class="Skills"
        style="
          left: 97px;
          top: 249px;
          position: absolute;
          color: #272727;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 500;
          word-wrap: break-word;
        "
      >
        Skills:
      </div>
      <div
        class="Verification"
        style="
          left: 615px;
          top: 392px;
          position: absolute;
          color: #272727;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 500;
          word-wrap: break-word;
        "
      >
        Verification
      </div>
      <div
        class="GoPlatformTeamCertifyThePersonWithTheNameOfThisCertificateHavePassedAndSucceededOurEmploymentReadinessAssessmentTest"
        style="
          width: 512px;
          left: 97px;
          top: 329px;
          position: absolute;
          color: #ac06f2;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 500;
          word-wrap: break-word;
        "
      >
        GO PLATFORM Team Certify the person with the name of this certificate
        have passed and succeeded our employment readiness assessment Test.
      </div>
      <div
        class="BasicSkillsForEmployment"
        style="
          left: 97px;
          top: 266px;
          position: absolute;
          color: #60b3ff;
          font-size: 22px;
          font-family: Montserrat;
          font-weight: 700;
          word-wrap: break-word;
        "
      >
        Basic Skills for Employment.
      </div>
      <div
        class="CofounderCeo"
        style="
          left: 361px;
          top: 425px;
          position: absolute;
          color: #60b3ff;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 700;
          word-wrap: break-word;
        "
      >
        Cofounder & CEO
      </div>
      <div
        class="AbdellahAouf"
        style="
          left: 372px;
          top: 458px;
          position: absolute;
          color: #ffa246;
          font-size: 14px;
          font-family: Montserrat;
          font-weight: 700;
          word-wrap: break-word;
        "
      >
        Abdellah Aouf
      </div>
      <img
        class="St1"
        style="
          width: 94px;
          height: 111px;
          left: 709px;
          top: 26px;
          position: absolute;
        "
        src="https://drive.google.com/uc?export=view&id=1CPeyoI-KbwT1mcIohZNkBkxbmcRDIVLl"
      />
      <div
        class="GoPlatform"
        style="
          left: 594.53px;
          top: 116.4px;
          position: absolute;
          color: #60b3ff;
          font-size: 14.88px;
          font-family: Poppins;
          font-weight: 700;
          word-wrap: break-word;
        "
      >
        GO PLATFORM
      </div>
      <div
        class="Vector"
        style="
          width: 65.73px;
          height: 47.75px;
          left: 504px;
          top: 104px;
          position: absolute;
          /* background: #60b3ff; */
        "
      >
        <svg
          width="66"
          height="48"
          viewBox="0 0 66 48"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M55.4633 0C55.4911 0 55.5189 0 55.5467 0C55.6032 0.0342251 55.6554 0.0772258 55.7162 0.102675C58.7033 1.35233 61.6911 2.60111 64.6782 3.84989C65.0233 3.99469 65.3675 4.14124 65.7292 4.29394C65.7292 4.37555 65.7292 4.45102 65.7292 4.52737C65.7257 16.6738 65.7214 28.8202 65.7214 40.9666C65.7214 41.1307 65.6692 41.2141 65.5284 41.2904C62.238 43.0754 58.9493 44.8647 55.6606 46.6532C55.6067 46.6831 55.5511 46.7094 55.505 46.7322C55.4841 46.7252 55.4772 46.7226 55.4711 46.7199C55.465 46.7173 55.4589 46.7138 55.4528 46.7103C55.4467 46.7068 55.4415 46.7024 55.4354 46.6989C55.4241 46.691 55.412 46.6831 55.4007 46.6752C40.2187 35.4783 25.0358 24.2814 9.85299 13.0845C9.83038 13.0679 9.80778 13.0521 9.78518 13.0354C9.77388 13.0275 9.76345 13.0187 9.75215 13.0099C9.74693 13.0055 9.74084 13.002 9.73476 12.9985C9.72259 12.9915 9.71129 12.9836 9.69912 12.9775C9.69303 12.974 9.68694 12.9704 9.67999 12.9687C9.67391 12.9669 9.66608 12.9669 9.66 12.9669C9.65391 12.9669 9.64696 12.9696 9.64087 12.9704C9.63566 12.9748 9.62783 12.9792 9.62609 12.9854C9.62001 13.0047 9.61045 13.0249 9.61045 13.045C9.6061 21.023 9.60175 29.0018 9.59827 36.9798C9.59827 37.0219 9.60523 37.0632 9.60958 37.1053C9.61044 37.1114 9.61479 37.1185 9.61914 37.1228C9.62349 37.1281 9.63044 37.1307 9.66 37.1518C12.9252 35.4932 16.2017 33.8276 19.5156 32.1444C17.3857 30.5578 15.2828 28.9913 13.1799 27.4257C13.1843 27.4152 13.1895 27.4047 13.1938 27.3933C18.169 27.5012 23.1433 27.6091 28.1333 27.7171C28.1376 27.8198 28.1454 27.9022 28.1454 27.9856C28.1463 31.4239 28.1446 34.8622 28.1498 38.3005C28.1498 38.4866 28.0976 38.5849 27.9272 38.6691C25.5305 39.8459 23.1364 41.0298 20.7431 42.2145C17.1154 44.01 13.4894 45.8081 9.86255 47.6054C9.78257 47.6449 9.70955 47.6993 9.63305 47.7467C9.60523 47.7467 9.57741 47.7467 9.54959 47.7467C9.49569 47.6975 9.44875 47.637 9.3879 47.601C6.32177 45.7792 3.25477 43.96 0.187774 42.1417C0.129529 42.1074 0.0625918 42.089 0 42.0627C0 29.9926 0 17.9226 0 5.8525C0.0773704 5.83056 0.159956 5.8209 0.230372 5.7858C0.957132 5.41898 1.68128 5.04864 2.40631 4.67831C4.72394 3.49623 7.04331 2.31678 9.35747 1.12767C9.53742 1.03553 9.65565 1.04343 9.81995 1.16278C14.63 4.64672 19.4452 8.12452 24.2587 11.6041C29.1121 15.1117 33.963 18.622 38.8182 22.1279C43.2883 25.3555 47.7627 28.5788 52.2346 31.8048C53.2578 32.5428 54.2801 33.2826 55.3042 34.0198C55.3607 34.061 55.4102 34.1312 55.4972 34.1102C55.5415 33.6591 55.5102 8.33776 55.4633 8.1719C55.3981 8.19384 55.3329 8.21315 55.2694 8.2386C54.4375 8.57032 53.5994 8.89063 52.7744 9.23815C46.9716 11.6848 41.1715 14.1367 35.3704 16.5869C35.3087 16.6132 35.2453 16.6352 35.1827 16.6589C35.2183 16.5799 35.267 16.522 35.3191 16.4684C36.145 15.6058 36.9717 14.744 37.7985 13.8822C41.5444 9.97531 45.2904 6.06838 49.0346 2.15969C49.1337 2.05614 49.2423 1.98593 49.3806 1.94293C51.3487 1.32776 53.3151 0.708197 55.2816 0.0877567C55.345 0.0666951 55.4033 0.0289597 55.4633 0Z"
            fill="#60B3FF"
          />
        </svg>
      </div>
      <div
        class="IcomoonFreeQrcode"
        style="
          width: 90px;
          height: 90px;
          left: 611px;
          top: 425px;
          position: absolute;
        "
      >
        <div
          class="Vector"
          style="
            width: 90px;
            height: 90px;
            left: 0px;
            top: 0px;
            position: absolute;
            background: #272727;
          "
        >
          <img src="<%= data.qrcode %>" width="90" height="90" />
        </div>
      </div>
    </div>
  </body>
</html>
