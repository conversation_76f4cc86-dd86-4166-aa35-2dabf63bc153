const whitelist = [
  "https://www.Recruitable.com",
  "http://127.0.0.1:5500",
  "http://localhost:3000",
  "http://localhost:8080",
  "http://localhost:3001",
  "http://localhost:8081",
  "http://localhost:8082",
  "http://recruitable.go-platform.com",
  "http://www.go-platform.com",
  "https://server.go-platform.com",
  "https://www.go-platform.com",
  "https://go-platform.com",
  "https://assessment.go-platform.com",
  "https://www.linkedin.com",
  "https://linkedin.com",
  "http://go-profiling.dz",
  "https://go-profiling.dz",
  "http://www.go-profiling.dz",
  "https://www.go-profiling.dz",
  "go-profiling.dz"
];

const corsOptions = {
  origin: (origin, callback) => {
    if (!origin) {
      return callback(null, true);
    }

    const normalizedOrigin = origin.replace(/^https?:\/\//, "");
    const whitelistedDomains = whitelist.map((url) =>
      url.replace(/^https?:\/\//, "")
    );

    if (
      whitelistedDomains.some(
        (domain) =>
          normalizedOrigin === domain || normalizedOrigin.endsWith("." + domain)
      )
    ) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  optionSuccessStatus: 200,
  credentials: true,
};

module.exports = corsOptions;
