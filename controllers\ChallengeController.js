const express = require('express');
const auth = require('../middleware/auth');
const { check, validationResult } = require('express-validator');
const Challenge = require('../models/Challenge');

const router = express.Router();

// Create a challenge

exports.createChallenge = async (req, res) => {

const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    try {
      const { title, description, difficulty, templateCode, testCases } = req.body;
      
      const challenge = new Challenge({
        title,
        description,
        difficulty: difficulty || 'medium',
        templateCode,
        testCases: testCases || [],
        creator: req.user.id
      });
      
      await challenge.save();
      
      res.json(challenge);
    } catch (err) {
      res.status(500).json({ message: err.message });
    }
  }
;

// Get all challenges for recruiter
    exports.getAllChallenge = async (req, res) => {

    try {
    const challenges = await Challenge.find({ creator: req.user.id });
    res.json(challenges);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get challenge details
exports.getChallenge = async (req, res) => {

  try {
    const challenge = await Challenge.findById(req.params.id);
    
    if (!challenge) {
      return res.status(404).json({ message: 'Challenge not found' });
    }
    
    // Only creator can view challenge details
    if (challenge.creator.toString() !== req.user.id) {
      return res.status(401).json({ message: 'Not authorized' });
    }
    
    res.json(challenge);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Run tests (for candidate)
    exports.RunChallenge = async (req, res) => {

    try {
    const challenge = await Challenge.findById(req.params.id);
    const { code } = req.body;
    
    if (!challenge) {
      return res.status(404).json({ message: 'Challenge not found' });
    }
    
    // Execute code and get results
    const testResults = await executeTests(challenge, code);
    
    res.json(testResults);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

module.exports = router;