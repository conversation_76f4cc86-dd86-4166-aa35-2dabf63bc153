const Certification = require("../models/Certification");
const mongoose = require('mongoose');
const jwt = require("jsonwebtoken");

// Create a new certification
const addCertification = async (req, res) => {
    const { title, issuer, expire, credentials, issuerCountry, startingDate, endingDate, skillsAcquired } = req.body;
    let user_token = req.cookies.candidate;
    if (user_token) {
        jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            try {
                const newCertification = new Certification({
                    candidate: decoded.id,
                    title,
                    expire,
                    issuer,
                    credentials,
                    issuerCountry,
                    startingDate,
                    endingDate,
                    skillsAcquired
                });

                const savedCertification = await newCertification.save();
                res.status(201).json({ message: 'Certification created successfully', savedCertification });
            } catch (error) {
                console.error('Error adding certification:', error);
                res.status(500).json({ error: 'Internal server error' });
            }
        })
    }
};

// Get all certifications for a user
const getCertificationsByCandidate = async (req, res) => {
    try {
        let token = req.cookies.candidate;

        jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            const certifications = await Certification.find({ candidate: mongoose.Types.ObjectId(decoded.id) });

            if (!certifications || certifications.length === 0) {
                return res.status(204).json({ message: "No certifications found." });
            }

            res.status(200).json(certifications);
        })
    } catch (error) {
        console.error('Error retrieving certifications:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Get certification by ID
const getCertificationById = async (req, res) => {
    const certificationId = req.params.id;

    try {
        const certification = await Certification.findById(certificationId);

        if (!certification) {
            return res.status(204).json({ message: "No certification found." });
        }

        res.status(200).json(certification);
    } catch (error) {
        console.error('Error retrieving certification:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Update a certification by ID
const updateCertificationById = async (req, res) => {
    const certificationId = req.params.id;

    try {
        const certification = await Certification.findById(certificationId);

        if (!certification) {
            return res.status(404).json({ message: "Certification not found." });
        }

        // Update fields if provided
        const { title, issuer, expire, credentials, issuerCountry, startingDate, endingDate, skillsAcquired } = req.body;

        if (title) certification.title = title;
        if (expire) certification.expire = expire;
        if (issuer) certification.issuer = issuer;
        if (credentials) certification.credentials = credentials;
        if (issuerCountry) certification.issuerCountry = issuerCountry;
        if (startingDate) certification.startingDate = startingDate;
        if (endingDate) certification.endingDate = endingDate;
        if (skillsAcquired) certification.skillsAcquired = skillsAcquired;

        const updatedCertification = await certification.save();
        res.status(200).json({ message: "Certification updated successfully", updatedCertification });
    } catch (error) {
        console.error('Error updating certification:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Delete a certification by ID
const deleteCertificationById = async (req, res) => {
    const certificationId = req.params.id;

    try {
        const certification = await Certification.findByIdAndDelete(certificationId);

        if (!certification) {
            return res.status(204).json({ message: "No certification found." });
        }

        res.status(200).json({ message: "Certification deleted successfully", deletedCertification: certification });
    } catch (error) {
        console.error('Error deleting certification:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

module.exports = {
    addCertification,
    getCertificationsByCandidate,
    getCertificationById,
    updateCertificationById,
    deleteCertificationById,
};
