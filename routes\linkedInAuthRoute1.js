const express = require("express");
const router = express.Router();
const {
    authorization,
    redirect,

} = require("../controllers/linkedInAuthController1");

router.get("/auth", (req, res) => {
    res.redirect(authorization());
});


router.get("/redirect", (req, res) => {
    //   const code = req.query.code;
    //   redirect(code);
    //   res.redirect("http://localhost:3000/dashboard");
    return res.json(redirect(req));
});



module.exports = router;
