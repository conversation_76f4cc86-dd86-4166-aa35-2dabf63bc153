//JUST First Shcema TEST
const mongoose = require("mongoose");

const postSchema = mongoose.Schema({
  pack_Starter: String,
  pack_Professional: String,
  pack_Company: String,

  category: String,

  cont_Starter: String,
  cont_Professional: String,
  cont_Company: String,

  image: String,

  price_Starter: Number,
  price_Professional: Number,
  price_Company: Number,

  S_plan_1: String,
  S_plan_2: String,
  S_plan_3: String,

  P_plan_1: String,
  P_plan_2: String,
  P_plan_3: String,
  P_plan_4: String,

  C_plan_1: String,
  C_plan_2: String,
  C_plan_3: String,
  C_plan_4: String,
  C_plan_5: String,

  offer: Number,

  Personality_type: String,
  Personality_Description: String,
  Natural_Strengths: String,
  Natural_Weaknesses: String,
  Basic_Disposition: String,
  Motivated_by: String,
  Time_Management: String,
  Communication_Style: String,
  Decision_Making: String,
  In_Pressure_Tense_Situations: String,
  Greatest_Needs: String,
  Personality_Desires: String,

  Candidate_Job_Position: String,
  Candidate_Full_Name: String,
  Candidate_Image: String,
  Candidate_Score: Number,
  Candidate_Completion_time: Number,

  Score: Number,

  Invited_Candidate: Number,
  Email_Delivered: Number,
  Email_Opened: Number,
  Links_Clicked: Number,
  Attempted: Number,
  Passed: Number,

  Avr_Completion_Time: Number,
  Candidate_Behind_Schedule: Number,
  Nbr_Applicants: Number,
  Nbr_Test_Sent: Number,
  Success_Rate: Number,
  Candidate_Percentage_Completion: Number,
  Candidate_Late_Applicant: Number,
  Nbr_Candidate_Pass_Test: Number,
  Nbr_Candidate_DidntPass_Test: Number,
});
module.exports = mongoose.model("Post", postSchema);
