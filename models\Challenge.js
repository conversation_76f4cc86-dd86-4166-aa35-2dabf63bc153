const mongoose = require("mongoose");
const challengeSchema = mongoose.Schema(
    {
    title: { type: String, required: true },
    description: { type: String },
    difficulty: { type: String, enum: ['easy', 'medium', 'hard'] },
    idProject: { type: mongoose.Schema.Types.ObjectId, ref: 'companyProjects' },
    
    assessments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CompanyAssessment',
  }],

    //creator: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    templateCode: { type: String},

    testCases: [{
      input: { type: String },
      expectedOutput: { type: String},
      isPublic: { type: Boolean, default: false }
    }],
    createdAt: { type: Date, default: Date.now }
  });


  module.exports = mongoose.model("Challenge", challengeSchema);
  