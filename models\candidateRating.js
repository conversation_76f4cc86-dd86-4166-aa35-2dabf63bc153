const mongoose = require("mongoose");

const candidateRatingSchema = new mongoose.Schema({
  candidateEmail: {
    type: String,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
  rating: {
    type: Number,
//    required: true,
  },
  feedback: {
    type: String,
  //  required: false,
  },
});

const CandidateRating = mongoose.model(
  "CandidateRating",
  candidateRatingSchema
);
module.exports = CandidateRating;
