const express = require("express");
const router = express.Router();
const API = require("../controllers/AssessmentTestController.js");
const authentication = require("../middleware/authentication");

router.get("/allResults", API.getAllResults);
router.get("/evaluations", API.getEvaluations);
router.get("/hardSkills", API.getHardSkillsFromDB);
router.get("/psychometrics", API.getPsychometrics);
router.get("/softSkills", API.getSoftSkills);

router.get("/allSkills", API.getAllSkills);


router.get("/progSkills", API.getProgrammingSkills);

router.get("/customQuestoins", API.getCustomQuestion);
router.get("/personality", API.getPersonality);
router.get("/topAssessments", API.getTopAssessments);
router.get("/sampleQuestions", API.getSampleQuestions);
router.get("/companyAssessments/", API.getCompanyAssessments);
router.get("/candidatesAssessments", API.getCandidatesAssessments);
router.post("/evaluateCandidate/", API.postCandidatesAnswers);
router.post("/submitScreeners", API.submitScreeners);
router.get("/", API.getAllAssessmentTest);
router.post("/candidateRating", API.createCandidateRating);
router.get(
  "/candidateRating/:projectId",
  authentication,
  API.getCandidateRating
);
router.get("/allEvaluations", API.getAllResults);
router.get("/:id", API.getAssessmentTest);
router.post("/", API.createAssessmentTest);
router.delete("/:id", API.deleteAssessmentTest);
router.post("/companyAssessment", API.createCompanyAssessments);
router.put("/updateScore/:assessmentName", API.updateAssessmentScore);

module.exports = router;
// /AssessmentTest/candidateRating
