const CaseStudy = require("../models/CaseStudy");
const multer = require("multer");
const validator = require("validator");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");

const getCaseStudies = async (req, res) => {
    try {
        let caseStudies = await CaseStudy.find({})

        await Promise.all(
            caseStudies.map(async (caseStudy) => {
                let image = "";
                if (caseStudy.image && !caseStudy.image.toLowerCase().includes(".jpg")) {
                    image = await Photo_Files.findOne({
                        _id: caseStudy.image,
                    });
                }
                if (image) {
                    caseStudy.image = "";

                    let imgChunks = await Photo_chunks.find({
                        files_id: image._id,
                    });
                    imgChunks.map((chunk) => {
                        caseStudy.image += chunk.data.toString("base64");
                    });
                    console.log({ caseStudyImage: caseStudy.image });
                }
            })
        );
        res.send(caseStudies);
    } catch (error) {
        console.log({ error });
        res.status(404).json({ message: "Error getting caseStudies" });
    }
};


const getCaseStudiesAdmin = async (req, res) => {
    try {
        // Fetch caseStudies and exclude the 'image' field
        let caseStudies = await CaseStudy.find({}).select('-image')

        // Return the caseStudies without the image field
        res.send(caseStudies);
    } catch (error) {
        console.log({ error });
        res.status(404).json({ message: "Error getting admin caseStudies" });
    }
};

const getCaseStudiesById = async (req, res) => {
    try {
        const caseStudyId = req.params.caseStudyId; // Assuming the caseStudy ID is part of the route parameters

        const caseStudy = await CaseStudy.findById(caseStudyId)

        if (!caseStudy) {
            return res.status(404).json({ message: "CaseStudy not found" });
        }

        let image = "";
        if (caseStudy.image && !caseStudy.image.toLowerCase().includes(".jpg")) {
            image = await Photo_Files.findOne({
                _id: caseStudy.image,
            });
        }

        if (image) {
            caseStudy.image = "";

            let imgChunks = await Photo_chunks.find({
                files_id: image._id,
            });

            imgChunks.map((chunk) => {
                caseStudy.image += chunk.data.toString("base64");
            });
            console.log({ caseStudyImage: caseStudy.image });
        }

        res.send(caseStudy);
    } catch (error) {
        console.log({ error });
        res.status(500).json({ message: "Internal Server Error" });
    }
};



const postCaseStudy = async (req, res) => {
    const { title, description, text, subtopics, date } = req.body;

    try {
        let caseStudy = new CaseStudy({
            title,
            text,

            subtopics,

            date,
            description

        });

        if (req.file) {
            caseStudy.image = req.file.id;
        }

        const savedCaseStudy = await caseStudy.save();
        res.send({ message: `caseStudy saved successfully,${savedCaseStudy}` });
    } catch (error) {
        console.log(error);
        res
            .status(404)
            .json({ message: "error saving your caseStudy, please try again" });
    }
};

const updateCaseStudy = async (req, res) => {
    const caseStudyId = req.params.caseStudyId; // Assuming you are passing the caseStudy ID in the request parameters

    try {
        const { description, title, text, subtopics, date } = req.body;

        // Find the caseStudy by ID
        const caseStudy = await CaseStudy.findById(caseStudyId);

        if (!caseStudy) {
            return res.status(404).json({ message: "CaseStudy not found" });
        }

        // Update the caseStudy fields
        if (title) caseStudy.title = title;
        if (description) caseStudy.description = description;
        if (text) caseStudy.text = text;

        if (subtopics) caseStudy.subtopics = subtopics;
        if (date) caseStudy.date = date;

        if (req.file) {
            caseStudy.image = req.file.id;
        }

        // Save the updated caseStudy
        const updatedCaseStudy = await caseStudy.save();
        res.send({ message: "CaseStudy updated successfully", updatedCaseStudy });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: error });
    }
};

const deleteCaseStudy = async (req, res) => {
    const caseStudyId = req.params.caseStudyId;

    try {
        const caseStudy = await CaseStudy.findById(caseStudyId);

        if (!caseStudy) {
            return res.status(404).json({ message: "CaseStudy not found" });
        }

        // Delete the caseStudy
        await caseStudy.remove();

        res.send({ message: "CaseStudy deleted successfully" });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Error deleting the caseStudy" });
    }
};

module.exports = { getCaseStudiesAdmin, getCaseStudiesById, deleteCaseStudy, getCaseStudies, postCaseStudy, updateCaseStudy };
