const Company = require("../models/Company");
const bcryptjs = require("bcrypt");
const jwt = require("jsonwebtoken");
const nodemailer = require("nodemailer");
const ejs = require("ejs");
const fs = require("fs");

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.COMPANY,
    pass: process.env.PASS,
  },
});

exports.isValidCompany = async (email, password) => {
  let companyFound = await Company.findOne({ email });
  if (!companyFound) return false;

  const correctPassword = await bcryptjs.compare(
    password,
    companyFound.password
  );
  if (!correctPassword) return false;
  return true;
};

exports.saveCompany = async (request) => {
  const JSONCompany = request.body.company;
  const discount = request.body.discount;
  const company = JSON.parse(JSONCompany);
  console.log({ companyToSave: company });
  let companyModel = new Company(company);

  if (request.files) {
    console.log({ files: request.files.logo[0] });
    companyModel.logo = request.files.logo[0] ? request.files.logo[0].id : "";
    companyModel.cover = request.files.cover[0]
      ? request.files.cover[0].id
      : "";
  }
  console.log({ companyModel });
  if (discount == "GO100") {
    companyModel.credit = 10;
  }
  // const salt = await bcryptjs.genSalt(10);
  // companyModel.password = await bcryptjs.hash(companyModel.password, salt);
  const savedCompany = await companyModel.save();
  return savedCompany;
};

exports.findCompany = async (name) => {
  let companyFound = await Company.findOne({ name });
  return companyFound;
};

exports.searchCompanies = async (firstLettersCompany) => {
  rgx = new RegExp("^" + firstLettersCompany);
  let companies = await Company.find({ name: rgx });
  return companies.map((company) => {
    return { value: company._id, label: company.name };
  });
};

exports.getCompanyWithTeam = async (companyId) => {
  let companyWithTeam = await Company.aggregate([
    {
      $match: { _id: mongoose.Types.ObjectId(companyId) },
    },
    {
      $lookup: {
        from: "users",
        localField: "_id",
        foreignField: "company",
        as: "Team",
      },
    },
  ]);

  return companyWithTeam;
};

exports.sendInvitationEmail = async (email, company_name) => {
  const expirationTime = Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60; // 3 days

  let specialToken = jwt.sign(
    {
      email: email,
      company_name: company_name,
      exp: expirationTime,
    },
    process.env.SPECIAL_LINK_KEY
  );
  let redirectLink = "";
  if (process.env.NODE_ENV === "dev") {
    redirectLink = `http://localhost:3000/company/invitedCoworker/?token=${specialToken}`;
  }
  if (process.env.NODE_ENV === "production") {
    redirectLink = `https://server.go-platform.com/company/invitedCoworker/?token=${specialToken}`;
  }
  // const assessmentLink = `http://localhost:3000/assessment/${specialToken}`;
  const template = await ejs.renderFile("./views/invitation.ejs", {
    data: {
      redirectLink,
      email: email,
      company_name: company_name,
    },
  });

  const mailOptions = {
    from: process.env.COMPANY,
    to: email,
    subject: "invitation Link",
    html: template,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};

const readJSONFile = (filename, callback) => {
  fs.readFile(filename, "utf8", (err, data) => {
    if (err) {
      callback(err);
      return;
    }
    try {
      const jsonArray = JSON.parse(data);
      callback(null, jsonArray);
    } catch (parseError) {
      callback(parseError);
    }
  });
};

const deleteValueFromArray = (jsonArray, value) => {
  const index = jsonArray.indexOf(value);
  console.log(`deleteValueFromArray: ${index}, value: ${value}`);
  if (index !== -1) {
    jsonArray.splice(index, 1);
  }
};

exports.updateArray = (valueToDelete) => {
  const filename = "./controllers/promoCodes/lifeTimeCodes.json";

  readJSONFile(filename, (err, jsonArray) => {
    if (err) {
      console.error("Error reading file:", err);
      return;
    }

    // Check if the value exists in the array and delete it
    deleteValueFromArray(jsonArray, valueToDelete);

    // Convert the modified array back to JSON format
    const updatedJSON = JSON.stringify(jsonArray);
    console.log(jsonArray.length);
    // Write the updated JSON back to the file
    fs.writeFile(filename, updatedJSON, "utf8", (writeErr) => {
      if (writeErr) {
        console.error("Error writing file:", writeErr);
      } else {
        console.log(
          `Value "${valueToDelete}" has been deleted from ${filename}`
        );
      }
    });
  });
};

exports.isValidCode = async (code) => {
  const filename = "./controllers/promoCodes/lifeTimeCodes.json";

  try {
    const data = await readMyFile(filename);
    const codeFound = data.includes(code);
    console.log(`isValid Code: ${codeFound}`);
    return codeFound;
  } catch (error) {
    console.error("Error reading file:", error);
    // You might want to handle the error appropriately here
    throw error;
  }
};

const readMyFile = async (filename) => {
  return new Promise((resolve, reject) => {
    fs.readFile(filename, "utf8", (err, data) => {
      if (err) {
        reject(err);
        return;
      }
      try {
        const jsonArray = JSON.parse(data);
        resolve(jsonArray);
      } catch (parseError) {
        reject(parseError);
      }
    });
  });
};
