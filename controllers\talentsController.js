const Candidate = require("../models/Candidate");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcrypt");
const validator = require("validator");
const nodemailer = require("nodemailer");
const ejs = require("ejs");
const CandidateEvaluation = require("../models/CandidateEvaluation.js");
const Company = require("../models/Company");

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.COMPANY,
    pass: process.env.PASS,
  },
});
const createNewTalent = async (req, res) => {
  const talent = req.body.talent;
  console.log({ talent });
  try {
    let validEmail = validator.normalizeEmail(talent.Email, {
      gmail_remove_dots: false,
    });
    let talentExists = await Candidate.findOne({ Email: validEmail });
    console.log({ talentExists });
    if (talentExists) {
      if (talentExists.Password) {
        return res.status(400).json({ message: "Email already used" });
      }
    }
    if (talent.Password !== talent.confirmPassword) {
      return res.status(400).json({ message: "Passwords do not match" });
    }
    talent.Password = await bcrypt.hash(talent.Password, 12);
    let talentModel = {};
    if (talentExists) {
      talentModel = await Candidate.findOneAndUpdate(
        { _id: talentExists._id }, // Assuming you're checking based on an ID or unique field
        talent, // Data to update
        { new: true } // Returns the updated document
      );
    } else talentModel = new Candidate(talent);

    const savedTalent = await talentModel.save();

    const token = jwt.sign(
      {
        email: savedTalent.Email,
        id: savedTalent._id,
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "5d",
      }
    );
    let redirectLink = "";
    if (process.env.NODE_ENV === "dev") {
      redirectLink = `http://localhost:3000/talents/verify/?token=${token}`;
    }
    if (process.env.NODE_ENV === "production") {
      redirectLink = `https://server.go-platform.com/talents/verify/?token=${token}`;
    }
    await sendConfirmationEmail(
      savedTalent.First_name,
      savedTalent.Email,
      redirectLink
    );

    console.log("Registred successfully");
    res.status(200).json({ company_name: savedRecruiter.company_name[0] });
  } catch (error) {}
  res.send(talent);
};

const signin = async (req, res) => {
  const { email, password } = req.body;
  console.log({ email, password });
  // Sanitize and validate email
  const sanitizedEmail = validator.normalizeEmail(email, {
    gmail_remove_dots: false,
  });

  try {
    // Check if the email exists in the User model
    const existingTalent = await Candidate.findOne({ Email: sanitizedEmail });
    // If the email is not found in the User model, check the Admin model
    if (!existingTalent) {
      return res.status(404).json({ message: "User does not exist." });
    }
    if (!existingTalent.isVerified) {
      return res
        .status(404)
        .json({ message: "Email not verified, please check your email." });
    }

    // Proceed with user authentication
    const isPasswordCorrect = await bcrypt.compare(
      password,
      existingTalent.Password
    );

    if (!isPasswordCorrect)
      return res.status(400).json({ message: "Invalid credentials." });

    const token = jwt.sign(
      {
        email: existingTalent.Email,
        first_name: existingTalent.First_name,
        last_name: existingTalent.Fast_name,
        id: existingTalent._id,
      },
      process.env.SECRET_WORD,
      { expiresIn: "24h" }
    );
    let options = {};
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    res.cookie("candidate", token, options);

    res.status(200).json({
      loggedIn: true,
    });
  } catch (error) {
    console.log({ error });
    res.status(500).json({ message: "Something went wrong." });
  }
};

const verifyTalent = async (req, res) => {
  const token = req.query.token;

  try {
    const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);

    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      return res.send("Link has expired.");
    }
    console.log({ decoded });
    const talent = await Candidate.findOne({ _id: decoded.id });
    console.log({ talent });
    if (talent) {
      // Update the isVerified attribute
      talent.isVerified = true; // Set to the desired value

      // Save the updated company
      await talent.save();
    }
    let options = {};
    let redirectLink = "";
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
      redirectLink = "http://localhost:8080";
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
      redirectLink = `https://talent.go-platform.com`;
    }
    res.cookie("candidate", token, options);

    res.redirect(redirectLink);
  } catch (err) {
    res.status(401).json({ message: "Invalid token!" });
  }
};

const ChangePasswordEmail = async (req, res) => {
  const email = req.body.email;

  try {
    if (!email) {
      return res.status(400).json({ message: "Email is required." });
    }
    const sanitizedEmail = validator.normalizeEmail(email, {
      gmail_remove_dots: false,
    });
    const existingTalent = await Candidate.findOne({ Email: sanitizedEmail });

    if (!existingTalent) {
      return res.status(400).json({ message: "User does not exist." });
    }
    const token = jwt.sign(
      {
        email: existingTalent.Email,
        id: existingTalent._id,
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "1h",
      }
    );
    let redirectLink = "";
    if (process.env.NODE_ENV === "dev") {
      redirectLink = `http://localhost:3000/talents/changePassword/?token=${token}`;
    }
    if (process.env.NODE_ENV === "production") {
      redirectLink = `https://server.go-platform.com/talents/changePassword/?token=${token}`;
    }

    await sendChangePAsswordEmail(
      existingTalent.First_name,
      existingTalent.Email,
      redirectLink
    );
    res.status(200).json({ message: "Email sent successfully!" });
  } catch (error) {
    console.log({ error });
    res.status(505).json({ message: "Internal serer error" });
  }
};

const ChangePassword = async (req, res) => {
  const token = req.query.token;
  try {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      const existingTalent = await Candidate.findOne({ _id: decoded.id });

      if (!existingTalent) {
        return res.status(400).json({ message: "User does not exist." });
      }
      let redirectLink = "";
      if (process.env.NODE_ENV === "dev") {
        redirectLink = `http://localhost:8080/changePassword/?token=${token}`;
      }
      if (process.env.NODE_ENV === "production") {
        redirectLink = `https://talent.go-platform.com/changePassword/?token=${token}`;
      }

      res.redirect(redirectLink);
    });
  } catch (error) {
    console.log(error);
    res.status(505).json({ message: "Internal server error" });
  }
};

const ChangeTalentPassword = async (req, res) => {
  const token = req.headers.authorization.replace("Bearer ", "");
  const { password, confirmPassword } = req.body;

  if (password !== confirmPassword) {
    return res.status(400).json({ message: "Passwords do not match." });
  }
  try {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
          message: "token Expired!",
        });
      }

      const existingTalent = await Candidate.findOne({ Email: decoded.email });
      if (!existingTalent) {
        return res.status(400).json({ message: "Talent does not exist." });
      }
      const isSamePassword = await bcrypt.compare(
        password,
        existingTalent.Password
      );
      if (isSamePassword) {
        return res
          .status(400)
          .json({ message: "New password cannot be the same as old password" });
      }
      existingTalent.Password = await bcrypt.hash(password, 12);
      existingTalent.save();
      res.status(200).json({ message: "Password changed successfully!" });
    });
  } catch (error) {
    console.log(error);
    res.status(505).json({ message: "Internal server error" });
  }
};

const sendConfirmationEmail = async (first_name, email, redirectLink) => {
  console.log("sending email...");
  try {
    const template = await ejs.renderFile("./views/TalentConfEmail.ejs", {
      data: {
        first_name,
        redirectLink,
      },
    });

    const mailOptions = {
      from: process.env.COMPANY,
      to: email,
      subject: "WELCOME TO GO-PLATFORM",
      html: template,
    };

    await transporter.sendMail(mailOptions);
    console.log("Email sent successfully!");
  } catch (e) {
    console.log({ e });
  }
};
const sendChangePAsswordEmail = async (first_name, email, redirectLink) => {
  const template = await ejs.renderFile("./views/resetTalentPass.ejs", {
    data: {
      first_name,
      redirectLink,
    },
  });

  const mailOptions = {
    from: process.env.COMPANY,
    to: email,
    subject: "GO_PLATFORM Reset Password",
    html: template,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};

const getGeneralData = async (req, res) => {
  const excludedEmails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const totalCandidatesPassed = await CandidateEvaluation.find({
    candidate: { $nin: excludedEmails },
  });
  let totalTime = 0;
  await Promise.all(
    totalCandidatesPassed.map((candidate) => {
      totalTime += candidate.candidateTime ? candidate.candidateTime : 0;
    })
  );
  let totalCandidate = await CandidateEvaluation.countDocuments({
    candidate: { $nin: excludedEmails },
  });

  let totalCompanies = await Company.countDocuments({});

  console.log({ totalCandidate, totalTime, totalCompanies });
};

module.exports = {
  createNewTalent,
  signin,
  verifyTalent,
  ChangePassword,
  ChangePasswordEmail,
  ChangeTalentPassword,
  getGeneralData,
};
