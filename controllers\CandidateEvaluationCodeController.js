const { GoogleGenerativeAI } = require("@google/generative-ai");
const Evaluation = require("../models/CandidateEvaluation.js");
const CandidateRating = require("../models/candidateRating.js");
const User = require("../models/user");

// Evaluation prompt template
const EVAL_PROMPT = `
You are an expert code reviewer and technical interviewer. Given the following coding problem, user code, and test results, evaluate the submission according to these criteria:

1. Code Correctness (35%)
   a. Test Case Pass Rate (20%): Predefined unit tests (basic, edge, failure cases).
   b. Edge Case Handling (10%): Validates inputs like nulls, empty arrays, or extreme values.
   c. Error-Free Execution (5%): No crashes/timeouts.

2. Efficiency & Performance (25%)
   a. Time Complexity (15%): Optimal Big O
   b. Space Complexity (7%): Minimal memory usage.
   c. Benchmark Compliance (3%): Runtime/memory within expected thresholds.

3. Code Quality & Readability (15%)
   a. Readability (6%): Consistent naming, indentation, and structure.
   b. Modularity (5%): Separation of concerns (e.g., functions, classes).
   c. Language Idioms (4%): Use of best practices (e.g., Pythonic code).

4. Problem-Solving Approach (10%)
   a. Algorithm Choice (6%): Optimal vs. brute-force approaches.
   b. Logical Flow (4%): Code structure (e.g., recursion vs. loops).

5. Security & Robustness (5%)
   a. Input Validation (3%): Sanitization of user inputs.
   b. Vulnerability Avoidance (2%): No SQLi, XSS, or buffer overflows.

6. Scalability (5%)
   a. Architectural Design (3%): Scalable patterns (e.g., caching, sharding).
   b. Concurrency (2%): Thread safety/parallelism (if applicable).

7. Tooling & Originality (5%)
   a. Plagiarism Detection (3%): Code similarity checks (MOSS, custom ML).
   b. Best Practices (2%): Use of linters, formatters, or CI/CD scripts.

For each main criterion and sub-criterion, provide:
- A score (0-100, or 0-criterion weight if you prefer)
- A short explanation/feedback

Return your response as a JSON object with this structure:
{
  "criteria": [
    {
      "name": "Code Correctness",
      "score": 0-35,
      "subcriteria": [
        { "name": "Test Case Pass Rate", "score": 0-20, "feedback": "..."},
        { "name": "Edge Case Handling", "score": 0-10, "feedback": "..."},
        { "name": "Error-Free Execution", "score": 0-5, "feedback": "..."}
      ],
    },
    ... (repeat for all main criteria)
  ],
  "overall_score": 0-100,
  "overall_feedback": "..." // summary for the whole submission
}

Here is the problem, user code, and test results:
`;

class EvaluationController {
  constructor() {
    this.genAI = new GoogleGenerativeAI('AIzaSyAnX6wTVZ7wDDLFgQt6YMWWrMXOUC7LNi0');
  }

  async evaluateSubmission(req, res) {
    try {
      console.log("Received evaluation request:", req.body);
      if(!req.body.evaluation) {
        console.log("No assessment provided, skipping candidate rating.");
      } else {
//      var evaluation = req.body.evaluation;
      var evaluation = typeof req.body.evaluation === "string"
  ? JSON.parse(req.body.evaluation)
  : req.body.evaluation;
        const candidateRating = await CandidateRating.create(evaluation);
        await candidateRating.save();
        console.log({ candidateRating });
      }

      // Validate input
      if (!req.body.code || !req.body.problem || !req.body.lang) {
        console.log("No coding challenge provided, skipping.");
      
      } else {
      var language = req.body.lang;
      var problem = req.body.problem.text;
      var expectedResult = req.body.problem.answer;
      var code = req.body.code;
   
        // Build the prompt
        const userPrompt = `
          Problem Description:
          ${typeof problem === "string" ? problem : JSON.stringify(problem, null, 2)}

          User Code (${language}):
          ${code}

          Expected Result:
          ${typeof expectedResult === "string" ? expectedResult : JSON.stringify(expectedResult, null, 2)}
        `;
        // Call Gemini AI
        const model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
        const result = await model.generateContent({
          contents: [
            { role: "user", parts: [{ text: EVAL_PROMPT + userPrompt }] }
          ]
        });
        // Process response
        let content = result.response.candidates?.[0]?.content?.parts?.[0]?.text || result.response.text;
        content = this.cleanAIResponse(content);
        let evaluation;
        try {
          evaluation = JSON.parse(content);
        } catch (e) {
          return res.status(500).json({
            success: false,
            error: "AI response parsing failed",
            details: e.message,
            rawResponse: content
          });
        }
        // Success response
        console.log("Evaluation result:");
        console.dir(evaluation, { depth: null });
        const getCriteria = (name) =>
          evaluation.criteria.find(
            (crit) => crit.name && crit.name.toLowerCase() === name.toLowerCase()
          );
        const EvaluationResult = new Evaluation({
          candidate: req.body.idCandidate,
          projectId: req.body.idProject,
          codeCorrectness: getCriteria("Code Correctness"),
          efficiencyPerformance: getCriteria("Efficiency & Performance"),
          codeQualityReadability: getCriteria("Code Quality & Readability"),
          problemSolvingApproach: getCriteria("Problem-Solving Approach"),
          securityRobustness: getCriteria("Security & Robustness"),
          scalability: getCriteria("Scalability"),
          toolingOriginality: getCriteria("Tooling & Originality"),
          overall_score: evaluation.overall_score,
          overall_feedback: evaluation.overall_feedback,
        });
        await EvaluationResult.save();
        
        return res.json({
          success: true,
          evaluation
        });
      }
    } catch (err) {
      console.error("Evaluation error:", err);
      return res.status(500).json({
        success: false,
        error: "Internal server error",
        details: err.message
      });
    }
  }

  cleanAIResponse(content) {
    if (!content) return content;
    // Remove JSON code block markers if present
    return content.replace(/^```json|^```|```$/gm, "").trim();
  }
}

const evaluationController = new EvaluationController();
const CandidateEvaluation = require("../models/CandidateEvaluation.js");

// ...existing code...

// Get all evaluation results for a candidate and project
async function getCandidateEvaluations(req, res) {
  try {
    const { email, projectId } = req.query; // or req.body if sent in body
    console.log("Request", req.query);
    if (!email || !projectId) {
      return res.status(400).json({ success: false, error: "email and projectId are required" });
    }

    // Find the user by email
    const user = await User.findOne({ email: email.trim() });
    if (!user) {
      return res.status(404).json({ success: false, error: "Candidate not found" });
    }

    // Find the evaluation(s) by candidate id and project id
    const results = await CandidateEvaluation.find({
      candidate: email,
      projectId: projectId
    }).populate("candidate").populate("challengeId").populate("projectId");

    if (!results.length) {
      return res.status(404).json({ success: false, message: "No evaluation results found" });
    }
    console.log("Results", results);
    return res.json({ success: true, results });
  } catch (err) {
    console.error("Error fetching candidate evaluations:", err);
    return res.status(500).json({ success: false, error: "Internal server error" });
  }
};

module.exports = {
  evaluateSubmission: evaluationController.evaluateSubmission.bind(evaluationController),
  getCandidateEvaluations,
};