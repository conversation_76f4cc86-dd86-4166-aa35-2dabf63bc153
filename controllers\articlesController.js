const Article = require("../models/articles");
const multer = require("multer");
const validator = require("validator");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");

const getArticles = async (req, res) => {
  const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
  const limit = parseInt(req.query.option) || 5; // Default to 10 items per page if not provided
  const skip = (page - 1) * limit;
  try {
    let articles = await Article.find({}).populate('category').sort({ date: -1 }) // Sort by date in descending order (most recent first)
    .skip(skip)
      .limit(limit);
      //const totalArticles = await Article.countDocuments();

    await Promise.all(
      articles.map(async (article) => {
        let image = "";
        if (article.image && !article.image.toLowerCase().includes(".jpg")) {
          image = await Photo_Files.findOne({
            _id: article.image,
          });
        }
        if (image) {
          article.image = "";

          let imgChunks = await Photo_chunks.find({
            files_id: image._id,
          });
          imgChunks.map((chunk) => {
            article.image += chunk.data.toString("base64");
          });
          
          console.log(article.title);
        }
      })
    );
    console.log("Load more articles");
    console.log(page);
    console.log("Load more articles");
    res.send(articles);
  } catch (error) {
    console.log({ error });
    res.status(404).json({ message: "Error getting articles" });
  }
};


const getArticlesAdmin = async (req, res) => {
  try {
    // Fetch articles and exclude the 'image' field
    let articles = await Article.find({}).select('-image').populate('category');

    // Return the articles without the image field
    res.send(articles);
  } catch (error) {
    console.log({ error });
    res.status(404).json({ message: "Error getting admin articles" });
  }
};
const getArticlesByCategory = async (req, res) => {
  const { category } = req.body;
  const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
  const limit = parseInt(req.query.option) || 5; // Default to 10 items per page if not provided
  const skip = (page - 1) * limit;

  try {
    let articles = await Article.find({ category }).populate('category').sort({ date: -1 }) // Sort by date in descending order (most recent first)
    .skip(skip)
      .limit(limit);

    await Promise.all(
      articles.map(async (article) => {
        let image = "";
        if (article.image && !article.image.toLowerCase().includes(".jpg")) {
          image = await Photo_Files.findOne({
            _id: article.image,
          });
        }
        if (image) {
          article.image = "";

          let imgChunks = await Photo_chunks.find({
            files_id: image._id,
          });
          imgChunks.map((chunk) => {
            article.image += chunk.data.toString("base64");
          });
          console.log({ articleImage: article.image });
        }
      })
    );
    res.send(articles);
  } catch (error) {
    console.log({ error });
    res.status(404).json({ message: "Error getting articles" });
  }
};
const getArticlesById = async (req, res) => {
  try {
    const articleId = req.params.articleId; // Assuming the article ID is part of the route parameters
    const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
  const limit = parseInt(req.query.option) || 5; // Default to 10 items per page if not provided
  const skip = (page - 1) * limit;


    const article = await Article.findById(articleId).populate('category').sort({ date: -1 }) // Sort by date in descending order (most recent first)
    .skip(skip)
      .limit(limit);

    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }

    let image = "";
    if (article.image && !article.image.toLowerCase().includes(".jpg")) {
      image = await Photo_Files.findOne({
        _id: article.image,
      });
    }

    if (image) {
      article.image = "";

      let imgChunks = await Photo_chunks.find({
        files_id: image._id,
      });

      imgChunks.map((chunk) => {
        article.image += chunk.data.toString("base64");
      });
      console.log({ articleImage: article.image });
    }

    res.send(article);
  } catch (error) {
    console.log({ error });
    res.status(500).json({ message: "Internal Server Error" });
  }
};



const postArticle = async (req, res) => {
  const { title, description, text, author, keywords, reading_time, subtopics, category, date } = req.body;

  try {
    let article = new Article({
      title,
      text,
      author,
      keywords,
      subtopics,
      category,
      reading_time,
      date,
      description

    });

    if (req.file) {
      article.image = req.file.id;
    }

    const savedArticle = await article.save();
    
    res.send({ message: `article saved successfully,${savedArticle}` });
  } catch (error) {
    console.log(error);
    res
      .status(404)
      .json({ message: "error saving your article, please try again" });
  }
};

const updateArticle = async (req, res) => {
  const articleId = req.params.articleId; // Assuming you are passing the article ID in the request parameters

  try {
    const { description, title, text, author, keywords, reading_time, subtopics, category, date } = req.body;

    // Find the article by ID
    const article = await Article.findById(articleId);

    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }

    // Update the article fields
    if (title) article.title = title;
    if (description) article.description = description;
    if (text) article.text = text;
    if (author) article.author = author;
    if (keywords) article.keywords = keywords;
    if (reading_time) article.reading_time = reading_time;
    if (subtopics) article.subtopics = subtopics;
    if (category) article.category = category;
    if (date) article.date = date;

    if (req.file) {
      article.image = req.file.id;
    }

    // Save the updated article
    const updatedArticle = await article.save();
    res.send({ message: "Article updated successfully", updatedArticle });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: error });
  }
};

const deleteArticle = async (req, res) => {
  const articleId = req.params.articleId;

  try {
    const article = await Article.findById(articleId);

    if (!article) {
      return res.status(404).json({ message: "Article not found" });
    }

    // Delete the article
    await article.remove();

    res.send({ message: "Article deleted successfully" });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Error deleting the article" });
  }
};

module.exports = { getArticlesAdmin, getArticlesById, deleteArticle, getArticles, postArticle, getArticlesByCategory, updateArticle };
