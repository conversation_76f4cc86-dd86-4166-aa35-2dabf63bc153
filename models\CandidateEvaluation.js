const mongoose = require("mongoose");

// Define the schema for storing assessment results
const CandidateEvaluationSchema = new mongoose.Schema({
  candidate: { type: String, required: true },
  projectId: { type: String, required: true },
  candidateTime: {
    type: Number,
    // required: true,
  },
  results: [],
  createdAt: { type: Date, default: Date.now }, // Save the date when the assessment was submitted
  status: {
    type: String,
    enum: ["pending", "accepted", "rejected"], // Allowed values
    default: "pending", // Default value
  },
  exitedTab: {
    type: Boolean,
    default: false,
  },
  mouseExited: {
    type: Boolean,
    default: false,
  },
  fullScreenExited: {
    type: Boolean,
    default: false,
  },
  exitCount: {
    type: Number,
    default: 0,
  },
  screens: {
    type: [String],
  },
  
  passed: {
    type: Boolean,
    default: true,
  },
  screeners: {
    type: Array,
  },
});

// Create a model for the assessment results
const CandidateEvaluation = mongoose.model(
  "CandidateEvaluation",
  CandidateEvaluationSchema
);

module.exports = CandidateEvaluation;
