const JobBoard = require('../models/JobBoard');
const mongoose = require("mongoose");
// Create a new job board entry
exports.createJobBoard = async (req, res) => {
    try {
//////////////////////
const jobsData = [
    {
      company: mongoose.Types.ObjectId("6731da1367fa7a183944f380"), // Replace with actual company ID
      job_position: mongoose.Types.ObjectId("66a3d7a31e61f4ffd06e4cbc"), // Replace with actual job position ID
      min_salary: 50000,
      max_salary: 70000,
      location: "New York",
      job_type: "Full-time",
    },
    {
      company: mongoose.Types.ObjectId("6731da1367fa7a183944f380"), // Replace with actual company ID
      job_position: mongoose.Types.ObjectId("66a3d7a31e61f4ffd06e4cbe"), // Replace with actual job position ID
      min_salary: 45000,
      max_salary: 65000,
      location: "San Francisco",
      job_type: "Part-time",
    },
    {
        company: mongoose.Types.ObjectId("6731da1367fa7a183944f380"), // Replace with actual company ID
        job_position: mongoose.Types.ObjectId("66a3d7a31e61f4ffd06e4cd8"), // Replace with actual job position ID
        min_salary: 45000,
        max_salary: 65000,
        location: "LA",
        job_type: "Part-time",
      },
  ];
/////////////////////////
        //const newJobBoard = new JobBoard(req.body);
        const jobEntries = await JobBoard.insertMany(jobsData);
        
        //const savedJobBoard = await newJobBoard.save();
        res.status(201).json(jobEntries);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Get all job board entries
exports.getJobBoards = async (req, res) => {
    try {
        // In your backend controller
const jobs = await JobBoard.find().populate('job_position', 'title').populate('company');
res.status(200).json(jobs);

        
    } catch (err) {
        console.error("Error fetching job boards:", err);
        if (!res.headersSent) {
            res.status(500).json({ message: "An error occurred while fetching job boards." });
          }
    }
};

// Get a single job board entry by ID
exports.getJobBoardById = async (req, res) => {
    try {
        const jobBoard = await JobBoard.findById(req.params.id).populate('company job_position');
        if (!jobBoard) return res.status(404).json({ error: "Job not found" });
        res.status(200).json(jobBoard);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Update a job board entry
exports.updateJobBoard = async (req, res) => {
    try {
        const updatedJobBoard = await JobBoard.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedJobBoard) return res.status(404).json({ error: "Job not found" });
        res.status(200).json(updatedJobBoard);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Delete a job board entry
exports.deleteJobBoard = async (req, res) => {
    try {
        const deletedJobBoard = await JobBoard.findByIdAndDelete(req.params.id);
        if (!deletedJobBoard) return res.status(404).json({ error: "Job not found" });
        res.status(200).json({ message: "Job deleted successfully" });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};
