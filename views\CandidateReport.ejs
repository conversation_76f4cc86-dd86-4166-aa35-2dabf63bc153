<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Report</title>
  </head>
  <body>
    <div style="display: grid; grid-template-columns: 1fr 1fr; margin: 3% 5%">
      <div style="display: flex; align-items: center">
        <img
          style="width: 100px; height: fit-content"
          src="data:image/png;base64, <%= data.logo %>"
          alt="Company Logo"
        />
        <h2 style="margin-left: 1%; font-size: 16px"><%=data.company %></h2>
      </div>
      <div
        style="
          border-left: 2px solid #2196f3;
          margin: 2% 0;
          /* margin-left: 50%; */
          padding-left: 5%;
          font-size: 14px;
          display: flex;
          align-items: center;
        "
      >
        Project: <br />
        <%=data.project %>
      </div>
    </div>
    <div
      style="
        width: 100%;
        height: 100%;
        background-color: #f5f6f6;
        padding: 1% 0;
      "
    >
      <div
        style="
          width: 85%;
          height: 80%;
          border-radius: 10px;
          padding: 2%;
          margin: 1% 5%;
          margin-right: 10%;
        "
      >
        <div
          style="
            display: flex;
            justify-content: space-between;
            width: 100%;
            align-items: center;
          "
        >
          <div>
            <h4 style="font-size: 14px; font-weight: 600">
              <%= data.fullName %>
            </h4>
            <h4><%= data.email %></h4>
          </div>
          <div>
            <h4>Score: <%=data.score%>%</h4>
            <h5>Time: <%=data.time%></h5>
            <h5>Passed On: <%=data.datePassed%></h5>
          </div>
        </div>
        <div
          style="
            margin-top: 5%;
            width: 100%;
            display: grid;
            height: 90px;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            grid-template-rows: 100px 50px; /* Adjust row height as needed */
            grid-gap: 1%;
          "
        >
          <% data.results.forEach(result => { %>
          <div>
            <div
              style="
                font-weight: 600;
                font-size: 12px;
                padding: 1% 3%;
                padding-right: 6%;
                background-color: #f5f6f6;
                height: 60px;
                border-bottom: 2px solid #f5f6f6;
                padding-bottom: 3%;
                display: flex;
                align-items: center;
              "
            >
              <h4><%= result.assessmentName %></h4>
            </div>
            <div
              style="
                font-weight: 500;
                font-size: 16px;
                padding: 1% 3%;
                padding-right: 6%;
                height: 30px;
              "
            >
              <h6>
                <%= result.rangesPoint ? result.rangesPoint + ' / ' +
                (result.quesionsNbr * 5) : (result.totalPoints || 0) + ' / ' +
                (result.quesionsNbr * 5 || 0) %>
              </h6>
            </div>
          </div>
          <% }); %>
        </div>

        <div style="margin-top: 10%">
          <h3>Candidate Answers:</h3>
          <% data.customResults.forEach((interpretation, index) => { %>
          <h5>Q<%= index+1 %>: <%= interpretation.question %></h5>
          <p>A<%= index+1 %>: <%= interpretation.answer %></p>
          <% }); %>
          <h3>Candidate Interpretations:</h3>
          <% data.interpretations.forEach(interpretation => { %>
          <h5><%= interpretation.title %></h5>
          <p><%= interpretation.description %></p>
          <% }); %>
        </div>
      </div>
    </div>
  </body>
</html>
