const Company = require("../../models/Company.js");
const jwt = require("jsonwebtoken");

const authMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization;

  // Check if Authorization header exists
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ message: "Unauthorized: No token provided" });
  }
  // Extract the token (remove "Bearer " prefix)
  const key = authHeader.split(" ")[1];

  try {
    // Verify the token
    // const decoded = jwt.verify(token, process.env.SECRET_WORD);
    const company = await Company.findOne({ apiKey: key });
    if (!company) {
      return res.status(401).json({ message: "Unauthorized: Invalid API key" });
    }
    // Attach user info to request
    // req.user = decoded;
    console.log("authorized...");
    // Proceed to next middleware/route handler
    next();
  } catch (error) {
    return res.status(403).json({ message: "Forbidden: Invalid token" });
  }
};

module.exports = authMiddleware;
