const io = require("../server").io;
const { createNotification } = require("../services/notificationsService");
const User = require("../models/user");
const Notification = require("../models/notifications");
let userSockets = {};

module.exports = (socket) => {
  socket.on("SUSCRIBE", (taskId) => {
    socket.join(taskId);
  });
  // CREATE BLOG NOTIFICATION
  socket.on('blog-notification', (title, text, company) => {
    /// Emit a notification to all clients
    User.find({company_name: { $in: [company] }}, (err, users) => {
      if (err) {
        console.error(err);
        return;
      }
      users.forEach(async (user) => { 
        let newNotif = new Notification({ title: "New blog: "+title, text: text, reciever: user._id//, link: link 
      });
    
        try {
            await newNotif.save();

        } catch (error) { console.error(error);}
    }); 
    }); 
       
    });
    //// CREATE PORJECT  NOTIFICATION
    socket.on('project-archived', (//{
      title, company//, link}
      ) => {
      /// Emit a notification to all clients
      io.emit('project-archived1', 'Button clicked: ');  
      User.find({company_name: company}, (err, users) => {
        if (err) {
          console.error(err);
          return;
        }
        users.forEach(async (user) => {
        let newNotif = new Notification({ title: "Project : "+title, text: "is been archived", 
          reciever: user._id//, link: link 
        } );
      
          try {
              await newNotif.save();
          } catch (error) {
              console.error(error);
        }
      }); 
      }); 
         
      });
        

//// CREATE PORJECT  NOTIFICATION
    socket.on('projectNotification', (//{
      title, text, company//, link}
      ) => {
        // Emit a notification to all clients
        User.find({company_name: { $in: [company] }}, (err, users) => {
        if (err) {
        console.error(err);
        return;
        }  
        users.forEach(async (user) => {
          let newNotif = new Notification({ title: "New project: "+title, text: "for the job title: "+text, 
          reciever: user._id//, link: link 
        });
      
          try {
              await newNotif.save();
              io.emit('projectNotification1');     
          } catch (error) {console.error(error); }
      }); 
      });          
      });


//////// CANDIDATE ASSESSMENT TEST NOTIFICATION
      socket.on('assessement-notification', ({title, text//, link
        , company}) => {

        /// Emit a notification to all clients
      io.emit('assessement-notification1', 'Button clicked: ');
      
        User.find({company_name: { $in: [company] }}, (err, users) => {
          if (err) {
            console.error(err);
            return;
          }
          
          users.forEach(async (user) => {            
            let newNotif = new Notification({ title: "New assessement: "+title, text: " candidate: "+text, reciever: user._id, link: link } );      
            try {await newNotif.save();} 
            catch (error) {console.error(error);}
        }); 
        }); 
           
        });

//// INVITE CANDIDATE NOTIFICATION
        socket.on('invite-notification', ({title, text, link, company}) => {

          /// Emit a notification to all clients
          io.emit('invite-notification1', 'Button clicked: ');
        
          User.find({company_name: { $in: [company] }}, (err, users) => {
            if (err) {
              console.error(err);
              return;
            }
            
            users.forEach(async (user) => {
              
              let newNotif = new Notification({ title: "Company: "+title, text: " New candidate: "+text, reciever: user._id, link: link } );
          
              try {
                  await newNotif.save();
                  
      
              } catch (error) {
                  console.error(error);
            }
          }); 
          }); 
             
          });


/////// SHOW NUMBER  OF UNREAD NOTIFICATION

    socket.on('NotifNumbers', async (_id) => {
      /// Emit a notification to all clients
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 80);

      
      let count=await Notification.countDocuments({ "seen": false, "reciever": _id, "date": { $gte: oneWeekAgo }});
      
      io.emit('NotifNumbers1', count);   
      });
        
      
/////////SHOW NOTIFICATION DATA WINDOW
      //console.log(count);
       socket.on('NotifData', async (_id) => {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 80);
        const Notif1 = await Notification.find({ "seen": false, "reciever": _id},{ "text": 1, "date": 1, "title": 1, "_id": 1, "reciever": 1, "seen": 1 }).sort({ "date": -1});
        //console.log("This is the id of the user: ",_id);
        //const Notif1 = await Notification.find({"seen": false, "reciever": _id}); 
        
        io.emit('NotifData1', Notif1);
        });
      
  
  // UPDATE SEEN NOTIFICATION
  socket.on('UpdateSeen', async (idNotif) => {
    /// Emit a notification to all clients   
  // Update one notification where idNotif equals idNotif1
 try{var NotifUpdate= await Notification.findOneAndUpdate({ _id: idNotif}, {$set: {"seen": true}});


 }
 catch(err){ console.error('!!!!!!!!!!!!Error updating notification:!!!!!!!!!!', err);}
   
});

io.emit('Updatenotification');
 
  
   
       

  // UPDATE SEEN NOTIFICATION
  socket.on('AllSeen', async (_id) => {
    /// Emit a notification to all clients   
  // Update one notification where idNotif equals idNotif1

 try{
  
var NotifUpdate= await Notification.updateMany({ "reciever": _id}, {$set: {"seen": true}});
}
 catch(err){ 
  console.log(err);

 }
 io.emit('UpdateAllnotification');





});

io.emit('AllSeen1', 'Button clicked: ');
 
  
  
  
  
  
  
  
  
  
  
  

  //notifications management

  socket.on("JOIN_TASK", async (room) => {
    socket.join(room);
  });


  socket.on("PUSH_NOTIFICATION", async (data) => {
    // let notification = await createNotification(data);
    
    io.sockets.to(data.room).emit("NOTIFICATION", data);
  });

  socket.on("login", (receiver) => {
    userSockets[receiver] = socket;
  });
  socket.on('buttonClicked', (message) => {
    
    console.log('Button clicked:', message)}); // Emit a notification to all clients io.emit('notification', 'Button clicked: ' + message); 
  
  socket.on("NOTIFICATION", async (data) => {
    // let notification = await createNotification(data);
    // console.log(userSockets);
    console.log(data);
    const userSocket = userSockets[data.reciever];
    console.log(userSocket);
    if (userSocket) {
      console.log(data);
      userSocket.emit("taskNotification", {
        taskId: "12333",
        message: "Task updated.",
      });
    }
  });
};
