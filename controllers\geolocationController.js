const geoip = require('geoip-lite');

exports.getGeolocation = (req, res) => {
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress || (req.connection.socket ? req.connection.socket.remoteAddress : null);
    console.log(ip);

    // Use a test IP for local development. You can change this to test other countries.
    // Algeria: "**************", France: "*********", US: "*******",UAE: "***********"
    // const testIp = (ip === '::1' || ip === '127.0.0.1') ? '**************' : ip; // Default test to Algeria
    const geo = geoip.lookup(ip);
    console.log({ geo });

    let responseData = {
        lang: 'en',
        currency: 'USD',
        country: 'US'
    };

    if (geo) {
        switch (geo.country) {
            case 'DZ':
                responseData = { lang: 'fr', currency: 'DZD', country: 'DZ' };
                break;
            case 'FR':
                responseData = { lang: 'fr', currency: 'EUR', country: 'FR' };
                break;
            case 'US':
                responseData = { lang: 'en', currency: 'USD', country: 'US' };
                break;
            default:
                responseData = { lang: 'en', currency: 'USD', country: geo.country };
        }
    }
    console.log("Response Data:", responseData);
    res.json(responseData);
};
