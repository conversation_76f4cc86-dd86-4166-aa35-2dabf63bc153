const express = require("express");
const router = express.Router();

// const User = require("../models/user");
// const Admin = require("../models/Admin");
const allAssessments = require("../models/allAssessments");
// const CandidateEvaluation = require("../models/CandidateEvaluation.js");
// const CandidateRating = require("../models/candidateRating.js");
// const companyProject = require("../models/companyProject");
// const Company = require("../models/Company");
// const Photo_Files = require("../models/photos.files");
// const Photo_chunks = require("../models/photos.chunks");
// const Interception = require("../models/interception");
// const Candidate = require("../models/Candidate");
// const CapturedPhotos = require("../models/CapturedPhotos");

// const { MongoClient } = require("mongodb");
// const client = new MongoClient(process.env.DB_URI, {
//   useNewUrlParser: true,
//   useUnifiedTopology: true,
// });

// const destClient = new MongoClient(process.env.NEW_DB, {
//   useNewUrlParser: true,
//   useUnifiedTopology: true,
// });

// router.get("/users", async (req, res) => {
//   const allUsers = await User.find({});
//   res.status(200).json({ allUsers });
// });

// router.get("/candidates", async (req, res) => {
//   const allCandidates = await Candidate.find({});
//   res.status(200).json({ allCandidates });
// });

// router.get("/admins", async (req, res) => {
//   const allAdmins = await Admin.find({});
//   res.status(200).json({ allAdmins });
// });

// router.get("/candidatesEvaluation", async (req, res) => {
//   const allEvaluations = await CandidateEvaluation.find({});
//   res.status(200).json({ allEvaluations });
// });

router.get("/allAssessments", async (req, res) => {
  //   const assessments = await allAssessments.find({});

  let assessments = await allAssessments.aggregate([
    {
      $project: {
        questions_list: 0, // Exclude the questions_list field
        answers: 0,
        ranges: 0,
      },
    },
  ]);
  console.log({ LENGTH: assessments.length });
  res.status(200).json({ assessments });
});

// router.get("/companies", async (req, res) => {
//   const companies = await Company.find({});
//   res.status(200).json({ companies });
// });

// router.get("/photoChunks", async (req, res) => {
//   const chunks = await Photo_chunks.find({});
//   res.status(200).json({ chunks });
// });

// async function listCollections() {
//   try {
//     await client.connect();
//     await destClient.connect();

//     console.log("Connected successfully to MongoDB Atlas");

//     const database = client.db();
//     const destDatabase = destClient.db();
//     const collections = await database.listCollections().toArray();

//     console.log("Collections:");

//     for (const collectionInfo of collections) {
//       const collection = database.collection(collectionInfo.name);
//       const data = await collection.find({}).toArray();

//       if (data.length > 0) {
//         const destCollection = destDatabase.collection(collectionInfo.name);
//         await destCollection.insertMany(data);
//         console.log(
//           `Copied ${data.length} documents from collection: ${collectionInfo.name}`
//         );
//       } else {
//         console.log(`Collection: ${collectionInfo.name} is empty, skipping`);
//       }
//     }
//   } catch (error) {
//     console.error("Error connecting to MongoDB Atlas", error);
//   } finally {
//     await client.close();
//   }
// }

// router.get("/allColl", async (req, res) => {
//   await listCollections();
//   res.status(200).json({ message: "Done" });
// });

module.exports = router;
