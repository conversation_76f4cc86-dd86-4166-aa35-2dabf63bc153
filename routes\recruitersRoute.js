const jwt = require("jsonwebtoken");
// Test for Candidate signUP and SignIN
const express = require("express");
// const authentication = require("../middleware/authentication");
const {
  signin,
  signup,
  redirectNewUser,
  getUserInfo,
  getAllUsers,
  getCompanies,
  ChangePasswordEmail,
  ChangePassword,
  ChangeUserPassword,
  updateRecruiterData,
  updatePassword,
  checkLogin,
  upgradePlan,
} = require("../controllers/user");
const authentication = require("../middleware/authentication");
const verify = require("../helper/verifyToken");
const router = express.Router();

router.post("/signin", signin);
router.post("/signup", signup);
router.post("/redirectNewUser", redirectNewUser);
router.get("/info", getUserInfo);
router.get("/isLoggedIn", checkLogin);

router.post("/logout", (req, res) => {
  if (req.headers["cookie"]) {
    let options = {};
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: 0, // Set maxAge to 0 to delete the cookie
      };
    }
    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 0, // Set maxAge to 0 to delete the cookie
      };
    }
    res.clearCookie("user", options);
    res.clearCookie("admin", options);
    res.status(200).send("logged out");
  } else {
    res.status(400).send("not logged in");
  }
});
router.put("/update", authentication, updateRecruiterData);
router.put("/updatePass", authentication, updatePassword);
router.get("/companies", authentication, getCompanies);
router.post("/changePassword", ChangePasswordEmail);
router.post("/changeUserPassword", ChangeUserPassword);
router.get("/changePassword", ChangePassword);
router.get("/all", getAllUsers);
router.post("/upgradePlan", upgradePlan);
module.exports = router;
