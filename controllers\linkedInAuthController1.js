const qs = require("querystring");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const CLIENT_ID = process.env.LINKEDIN_CLIENT_ID;
const CLIENT_SECRET = process.env.LINKEDIN_CLIENT_SECRET;
const REDIRECT_URI = process.env.LINKEDIN_REDIRECT_URI;


const authorization = (req, res) => {
    return encodeURI(
        `https://www.linkedin.com/oauth/v2/authorization?client_id=${CLIENT_ID}&response_type=code&scope=${process.env.SCOPE}&redirect_uri=${REDIRECT_URI}`
    );
};


const redirect = async (req) => {
    const code = req.query.code;
    let token = req.cookies.candidatecookie;
    console.log({ TTOOOOOOOOKKEEEENNNNNNN: token });
    const payload = qs.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        redirect_uri: REDIRECT_URI,
        grant_type: "authorization_code",
        code: code,
    });
    console.log({ payload });

    const data = await axios({
        method: "POST",
        url: "https://www.linkedin.com/oauth/v2/accessToken",
        data: payload,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
    })
        .then(async (res) => {
            const userInfo = await axios.get("https://api.linkedin.com/v2/userinfo", {
                headers: {
                    Authorization: `Bearer ${res.data.access_token}`,
                    "Content-Type": "application/json",
                },
            });

            console.log("User Info:", userInfo.data);


            const postData = {
                author: `urn:li:person:${userInfo.data.sub}`,
                lifecycleState: "PUBLISHED",
                specificContent: {
                    "com.linkedin.ugc.ShareContent": {
                        shareCommentary: {
                            text: "Hey, check out this amazing platform!",
                        },
                        shareMediaCategory: "ARTICLE",
                        mediaDescription: {
                            text: "Description for the article",
                        },
                        media: [
                            {
                                originalUrl: 'https://go-platform.com/', // Replace with your platform link
                                thumbnailUrl:
                                    "https://images.pexels.com/photos/268533/pexels-photo-268533.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500",
                                title: {
                                    text: "Platform Showcase",
                                },
                                description: {
                                    text: "Description for your platform",
                                },
                                status: "READY",
                            },
                        ],
                        mediaTitle: {
                            text: "Title of the platform showcase",
                        },
                    },
                },
                visibility: {
                    "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
                },
            };

            const config = {
                headers: {
                    Authorization: `Bearer ${res.data.access_token}`,
                    "Content-Type": "application/json",
                },
            };

            axios
                .post("https://api.linkedin.com/v2/ugcPosts", postData, config)
                .then((response) => {
                    console.log("UGC Post created successfully:", response.data);
                })
                .catch((error) => {
                    console.error("Error creating UGC Post:", error);
                });

            return res;
        })
        .catch((err) => {
            console.error({ err });
            return err;
        });
    console.log({ data: data });
    return data;
};

module.exports = {
    authorization,
    redirect,
};
