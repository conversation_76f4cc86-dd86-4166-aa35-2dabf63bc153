const handleError = require("../helper/handleError");
const companyService = require("../services/companyService");
const companyAuthService = require("../services/companyAuthService");
const jwt = require("jsonwebtoken");
const Role = require("../models/role");
const multer = require("multer");
const Company = require("../models/Company");
const User = require("../models/user");
const Candidate = require("../models/Candidate");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");
const CompanyAssessment = require("../models/companyAssessment.js");
const CandidateEvaluation = require("../models/CandidateEvaluation.js");
const Invitation = require("../models/Invitations");
const bcrypt = require("bcrypt");
const companyProject = require("../models/companyProject");
const mongoose = require("mongoose");
const nodemailer = require("nodemailer");
const ejs = require("ejs");
const { verifyToken } = require("../helper/verifyToken.js");
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.COMPANY,
    pass: process.env.PASS,
  },
});
const validator = require("validator");

exports.createCompany = async (req, res) => {
  let { state, errors } = handleError.withErrorRequest(req);
  if (state) return res.status(400).json({ errors: errors.array() });
  const JSONCompany = req.body.company;
  const company = JSON.parse(JSONCompany);
  try {
    let companyFound = await companyService.findCompany(company.name);
    if (companyFound)
      return res.status(400).json({ msg: "Company already exists" });
    const companyCreated = await companyService.saveCompany(req);

    // let token = await companyAuthService.generatingJWT(email);
    res.status(200).json({ companyCreated });
  } catch (error) {
    console.log({ error });
    res.status(400).send("Was there an error");
  }
};

const sendEmailToRegistredCmpany = async (first_name, email, redirectLink) => {
  const template = await ejs.renderFile("./views/welcomeEmailTemplate.ejs", {
    data: {
      first_name,
      redirectLink,
    },
  });

  const mailOptions = {
    from: process.env.COMPANY,
    to: email,
    subject: "WELCOME TO GO-PLATFORM",
    html: template,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};


exports.register = async (req, res) => {
  const { email, password } = req.body;
  console.log('req.body');
  console.log(req.body);
  console.log('req.body');

  try {
    if (!email || !password) {
      return res.status(400).json({ message: "Email and password required." });
    }

    const existingUser = await User.findOne({ email });
    
    if (existingUser) {
    console.log('User already exists');
      return res.status(409).json({ message: "User already exists." });
    }
      else {
        const hashedPassword = await bcrypt.hash(password, 12);

        // Step 1: Create the user
        const user = new User({
          email,
          password: hashedPassword,
          companies: [],
          isProfileComplete: false,
        });
        await user.save();


        // Step 2: Create a company with user's email
    const company = new Company({
      email: email, // or use as placeholder name if needed
      name: "", // will be filled later
      createdBy: user._id,
    });
    await company.save();

    // Step 3: Link company to user
    user.companies.push(company._id);
    await user.save();
    
  
    const role = await Role.findOneAndUpdate(
      {
        user: mongoose.Types.ObjectId(user._id),
        company: mongoose.Types.ObjectId(company._id),
      }, // Search criteria
      {
        user: mongoose.Types.ObjectId(user._id),
        company: mongoose.Types.ObjectId(company._id),
        type: "Owner",
      }, // Data to update or insert
      { new: true, upsert: true } // Create if not found, return updated or created document
    ).populate("company user"); // Optional: Populate referenced fields if needed

    // Step 4: Send back token / redirect
    
    const token = jwt.sign(
      {
        email: email,
        id:  user._id,
        company_id: company._id,
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "5d",
      }
    );
    let redirectLink = "";
    if (process.env.NODE_ENV === "dev") {
      redirectLink = `http://localhost:3000/company/verify/?token=${token}`;
    }
    if (process.env.NODE_ENV === "production") {
      redirectLink = `https://server.go-platform.com/company/verify/?token=${token}`;
    }
    
    await sendEmailToRegistredCmpany(
      '',
      email,
      redirectLink
    );
    
    console.log("Registration begins");
    res.send("Registration begins");
      }
 } catch (err) {
    console.error(err);
    res.status(500).json({ message: "Registration failed." });
  }
};



///////////////////////////////////////////////////////////////////////////////////////////////

exports.registerComplete = async (req, res) => {


  console.log(req.body);
  /// update user
  try {
    
  
    const updatedUser = await User.findByIdAndUpdate(
      req.body.userID,
      {
        first_name: req.body.firstName,
        last_name: req.body.lastName,
        company_name: req.body.companyName,
      },
      { new: true } // this option returns the updated document
    );
      
  
    if (!updatedUser) {
      console.log('User not found');
      return null;
    }
  
    console.log('Updated User:', updatedUser);
 
    
    var currentTimestamp = Date.now();
    var lifetimeInMillis = 100 * 30 * 24 * 60 * 60 * 1000;

    // Add three months to the current timestamp
    var endOfSubscriptionTimestamp = currentTimestamp + lifetimeInMillis;

    
    

    const updatedCompany = await Company.findByIdAndUpdate(
      req.body.companyID,
      {
        name: req.body.companyName,
        employees_count: req.body.companySize,
        email: req.body.email,
        endOfSubscription: endOfSubscriptionTimestamp,
        plan: "lifetime",
        credit: 10
      },
      { new: true } // this option returns the updated document
    );
  
    if (!updatedCompany) {
      console.log('Company not found');
      return null;
    }
  
    updatedUser.company_name.push(req.body.companyName);
    await updatedUser.save();
    await updatedCompany.save();
   
    const role = await Role.findOneAndUpdate(
      {
        user: mongoose.Types.ObjectId(updatedUser._id),
        company: mongoose.Types.ObjectId(updatedCompany._id),
      }, // Search criteria
      {
        user: mongoose.Types.ObjectId(updatedUser._id),
        company: mongoose.Types.ObjectId(updatedCompany._id),
        type: "Owner",
      }, // Data to update or insert
      { new: true, upsert: true } // Create if not found, return updated or created document
    ).populate("company user"); // Optional: Populate referenced fields if needed
    
   
   console.log('Updated Company:', updatedCompany);
    //return updatedCompany;
    
    const options = {
      domain: "server.go-platform.com",
      sameSite: "none",
      secure: true,
      path: "/",
      maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
      // httpOnly: true, // Set to true in production for HTTPS
    };
    res.cookie("user", req.body.token, options);


    
    res.status(200).json({
      loggedIn: true,
      company_name: req.body.companyName,
    });

      res.send("Registration completed");
  //  return  res.redirect(`https://go-platform.com/dashboard`);
 // return res.status(200).json({ redirectTo: `http://localhost:8081/en/dashboard` });
 // return  res.redirect(`http://localhost:8081/en/dashboard`);
  //return res
  //.status(200)
  //.redirect(
    //`https://www.go-platform.com/dashboard?company_name=${req.body.companyName}`
  //);
} catch (error) {
    console.error('Error updating user:', error);
   if (!res.headersSent) {
        return res.status(500).json({ message: "Internal server error" });
      }
  }
  
  }

  //////////////////////////////////////////////////////////////////////////////////////////////









exports.registerCompany = async (req, res) => {
  let { state, errors } = handleError.withErrorRequest(req);
  if (state) return res.status(400).json({ errors: errors.array() });

  const JSONCompany = req.body.company;
  const JSONRecruiter = req.body.recruiter;
  const discount = req.body.discount;
  console.log({ BODY: req.body.company });
  const company = JSON.parse(JSONCompany);
  const recruiter = JSON.parse(JSONRecruiter);
  const sanitizedEmail = validator.normalizeEmail(recruiter.email, {
    gmail_remove_dots: false,
  });
  recruiter.email = sanitizedEmail;
  recruiter.company_name = company.name;
  try {
    let companyFound = await companyService.findCompany(
      company.name.toLowerCase()
    );
    let recruiterFound = await User.findOne({ email: recruiter.email });
    let recruiterByPhone = await User.findOne({
      phone_nbr: recruiter.phone_nbr,
    });
    if (companyFound)
      return res.status(400).json({ msg: "Company already exists" });
    if (recruiter.password !== recruiter.confirm_password) {
      return res.status(400).json({ password: "Passwords do not match" });
    }
    if (recruiterFound) {
      return res.status(400).json({ email: "Email already exists" });
    }
    if (recruiterByPhone) {
      return res.status(400).json({ phone: "Phone number already exists" });
    }

    recruiter.password = await bcrypt.hash(recruiter.password, 12);

    let companyModel = new Company(company);
    let recruiterModel = new User(recruiter);
    const role = await Role.findOneAndUpdate(
      {
        user: mongoose.Types.ObjectId(recruiter._id),
        company: mongoose.Types.ObjectId(company._id),
      }, // Search criteria
      {
        user: mongoose.Types.ObjectId(recruiter._id),
        company: mongoose.Types.ObjectId(company._id),
        type: "Owner",
      }, // Data to update or insert
      { new: true, upsert: true } // Create if not found, return updated or created document
    ).populate("company user"); // Optional: Populate referenced fields if needed
    if (req.file) {
      companyModel.logo = req.file.id;
    }

    if (discount == "GO100") {
      companyModel.credit = 10;
    }

    if (discount == "KALEIDO") {
      const currentDate = new Date();

      companyModel.discounts.push({
        name: discount,
        appliedAt: currentDate,
        status: "active",
      });
    }
    let isValidCode = await companyService.isValidCode(discount);
    if (isValidCode) {
      var currentTimestamp = Date.now();
      var lifetimeInMillis = 100 * 30 * 24 * 60 * 60 * 1000;

      // Add three months to the current timestamp
      var endOfSubscriptionTimestamp = currentTimestamp + lifetimeInMillis;

      companyModel.plan = "lifetime";
      companyModel.endOfSubscription = endOfSubscriptionTimestamp;
      await companyService.updateArray(discount);
    } else {
      companyModel.plan = "free";
      var currentTimestamp = Date.now();

      // // Calculate the timestamp for three months in milliseconds
      // var threeMonthsInMillis = 3 * 30 * 24 * 60 * 60 * 1000; // Assuming 30 days per month

      // // Add three months to the current timestamp
      // var endOfSubscriptionTimestamp = currentTimestamp + threeMonthsInMillis;
      companyModel.endOfSubscription = currentTimestamp;
    }
    const savedCompany = await companyModel.save();
    const savedRecruiter = await recruiterModel.save();

    const token = jwt.sign(
      {
        email: savedRecruiter.email,
        id: savedRecruiter._id,
        company_name: savedRecruiter.company_name[0],
      },
      process.env.SECRET_WORD,
      {
        expiresIn: "5d",
      }
    );
    let redirectLink = "";
    if (process.env.NODE_ENV === "dev") {
      redirectLink = `http://localhost:3000/company/verify/?token=${token}`;
    }
    if (process.env.NODE_ENV === "production") {
      redirectLink = `https://server.go-platform.com/company/verify/?token=${token}`;
    }
    await sendEmailToRegistredCmpany(
      savedRecruiter.first_name,
      recruiter.email,
      redirectLink
    );

    console.log("Registred successfully");
    res.status(200).json({ company_name: savedRecruiter.company_name[0] });
  } catch (error) {
    console.log({ error });
    res.status(400).send("Was there an error");
  }
};

exports.verifyCompany = async (req, res) => {
  const token = req.query.token;
  
  try {
    const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);
    console.log(decoded);

    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      return res.send("Link has expired.");
    }

    const company = await Company.findOne({ _id: decoded.company_id });
    if (company) {
      // Update the isVerified attribute
      company.isVerified = true; // Set to the desired value

      // Save the updated company
      await company.save();
    }
    
    const options = {
      domain: "server.go-platform.com" ? process.env.NODE_ENV === "production" : "http://localhost:3000",
      sameSite: "none",
      secure: true,
      path: "/",
      maxAge: 1000 * 60 * 60, // Cookie expires in 1 hour
      // httpOnly: true, // Set to true in production for HTTPS
    };
    res.cookie("user", token, options);
if (process.env.NODE_ENV === "dev") {
      
    res.redirect(`http://localhost:8081/en/completeRegister?email=${encodeURIComponent(decoded.email)}&id=${encodeURIComponent(decoded.id)}&company=${decoded.company_id}&token=${token}`);
    }
    if (process.env.NODE_ENV === "production") {
   
    res.redirect(`https://www.go-platform.com/en/completeRegister?email=${encodeURIComponent(decoded.email)}&id=${encodeURIComponent(decoded.id)}&company=${decoded.company_id}&token=${token}`);
    }

  
    
  } catch (err) {
    res.status(401).json({ message: "Invalid token!" });
  }
};

exports.searchCompanies = async (req, res) => {
  try {
    let companiesFound = await companyService.searchCompanies(
      req.query.firstLetter
    );
    res.json({ companiesFound });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};

exports.getCompanyWithTeam = async (req, res) => {
  try {
    let companyWithTeam = await companyService.getCompanyWithTeam(
      req.params.companyId
    );
    res.json({ companyWithTeam });
  } catch (error) {
    res.status(400).send("Was there an error");
  }
};

exports.getCompanyInfo = async (req, res) => {
  let token = req.cookies.user;

  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }

    try {
      let company = await Company.findOne({ name: decoded.company_name });

      if (!company) {
        return res.status(400).send("Company not found");
      }
      let logo = "";
      if (company.logo) {
        logo = await Photo_Files.findOne({
          _id: company.logo,
        });
      }
      let cover;
      if (company.cover) {
        cover = await Photo_Files.findOne({
          _id: company.cover,
        });
      }
      if (logo) {
        company.logo = "";

        let imgChunks = await Photo_chunks.find({
          files_id: logo._id,
        });
        imgChunks.map((chunk) => {
          company.logo += chunk.data.toString("base64");
        });
      }

      if (cover) {
        company.cover = "";

        let imgChunks = await Photo_chunks.find({
          files_id: cover._id,
        });
        imgChunks.map((chunk) => {
          company.cover += chunk.data.toString("base64");
        });
      }
      res.send(company);
    } catch (error) {
      console.log({ error });
      res.status(400).send("there was an error");
    }
  });
};

exports.updateCompany = async (req, res) => {
  const { name, updates } = req.body;
  console.log("Request body:", req.body);

  try {
    const company = await Company.findOneAndUpdate({ name }, updates, {
      new: true,
    });
    if (!company) {
      return res.status(404).json({ error: "Company not found" });
    }
    res.json(company);
  } catch (error) {
    handleError(error, res);
  }
};

exports.getInvitedCandidates = async (req, res) => {
  let token = req.cookies.user;
  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let companyProjects = await companyProject.find({
          company_name: decoded.company_name,
        });

        let invitationsSent = [];
        companyProjects.length > 0 &&
          (await Promise.all(
            companyProjects.map(async (project) => {
              let invitedCandidates = await Invitation.find({
                projectId: project._id,
              });

              invitedCandidates.length > 0 &&
                (await Promise.all(
                  invitedCandidates.map((candidate) => {
                    const candidateObject = candidate.toObject();
                    candidateObject.prjectName = project._id;
                    invitationsSent.push(candidateObject);
                  })
                ));
            })
          ));

        return res.send({ invitationsSent });
      } catch (error) {
        console.log({ error });
      }
    });
  }
};

exports.getCandidates = async (req, res) => {
  let token = req.cookies.user;
  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {
        let companyProjects = await companyProject.find({
          company_name: decoded.company_name,
        });
        if (companyProjects.length === 0) {
          return res.status(400).send("no candidates found");
        }
        let page = parseInt(req.query.page) || 1;
        let perPage = parseInt(req.query.perPage) || 6;
        let startIndex = (page - 1) * perPage;
        let endIndex = page * perPage;

        let candidatesData = [];
        let candidatesScores = [];

        // let invitationsSent = [];
        // await Promise.all(
        // companyProjects.map(async (project) => {

        let candidates = await CandidateEvaluation.find({
          projectId: { $in: companyProjects.map((project) => project._id) },
          status: "active",
          $or: [{ passed: true }, { passed: { $exists: false } }],
        })
          .skip(startIndex)
          .limit(perPage);

        // let candidates = await CandidateEvaluation.find({
        //   projectId: project._id,
        //   status: "active",
        // })
        //   .skip(startIndex)
        //   .limit(perPage);
        await Promise.all(
          candidates.map((candidate) => {
            const candidateObject = candidate.toObject();
            // candidateObject.projectId = project._id;
            const candidateScoreExists = candidatesScores.filter(
              (existingCandidate) => {
                return (
                  existingCandidate.candidate == candidateObject.candidate &&
                  existingCandidate.projectId == candidateObject.projectId
                );
              }
            );

            if (candidateScoreExists.length === 0) {
              candidatesScores.push(candidateObject);
            }
          })
        );
        await Promise.all(
          candidates.map(async (candidate) => {
            let candidateFound = await Candidate.findOne({
              Email: candidate.candidate,
            });
            if (candidateFound) {
              const duplicateCandidates = candidatesData.filter(
                (existingCandidate) => {
                  return existingCandidate.Email === candidateFound.Email;
                }
              );

              if (duplicateCandidates.length === 0) {
                candidatesData.push(candidateFound);
              }
            }
          })
        );
        // })
        // );
        await Promise.all(
          candidatesData.map(async (candidate) => {
            if (candidate.Avatar) {
              let avatar = await Photo_Files.findOne({
                _id: candidate.Avatar,
              });
              candidate.Avatar = "";

              if (avatar) {
                let imgChunks = await Photo_chunks.find({
                  files_id: avatar._id,
                });
                imgChunks.map((chunk) => {
                  candidate.Avatar += chunk.data.toString("base64");
                });
              }
            }
          })
        );

        res.send({ candidatesData, candidatesScores });
      } catch (error) {
        console.log({ error });
        res.status(400).send("there was an error");
      }
    });
  }
};

const calculateAveragePoints = async (candidateResults) => {
  const assessments = [];

  const assessmentMap = new Map(); // To store intermediate data for each assessment

  candidateResults.forEach((candidate) => {
    candidate.results.forEach((result) => {
      const { assessmentName, totalPoints, quesionsNbr, rangesPoint } = result;
      if (!assessmentMap.has(assessmentName)) {
        assessmentMap.set(assessmentName, {
          totalPoints: totalPoints,
          rangesPoints: rangesPoint ? rangesPoint : 0,
          questionsNbr: quesionsNbr,
          count: 1,
        });
      } else {
        const current = assessmentMap.get(assessmentName);
        if (rangesPoint !== undefined) {
          current.rangesPoints += rangesPoint;
        } else {
          current.totalPoints += totalPoints;
        }
        // current.questionsNbr += quesionsNbr;
        current.count++;
        assessmentMap.set(assessmentName, current);
      }
    });
  });

  assessmentMap.forEach((value, key) => {
    const { totalPoints, questionsNbr, count, rangesPoints } = value;
    const averageTotalPoints = totalPoints / count;
    const rangesPoint = rangesPoints / count;
    const averageQuestions = questionsNbr / count;
    assessments.push({
      assessmentName: key,
      totalPoints: averageTotalPoints,
      rangesPoint,
      questionsNbr,
    });
  });
  return assessments;
};

const canlculateCandidatesScore = async (candidates, project) => {
  candidatesScores = [];
  await Promise.all(
    candidates.map(async (candidate) => {
      let score = 0;
      let candidateOBJ = candidate.toObject();
      // const validResults = candidate.results?.filter(
      //   (element) => element?.totalPoints !== undefined
      // );
      // validResults.forEach((element) => {
      //   if (element?.totalPoints !== undefined) {
      //     score += (element?.totalPoints * 100) / element?.quesionsNbr;
      //   }
      // });
      // const averageScore = score / validResults?.length;

      // const roundedScore = averageScore.toFixed(2);
      // candidate.results?.forEach((element) => {
      //   if (element.rangesPoint || element.totalPoints) {
      //     score += element.rangesPoint
      //       ? (element.rangesPoint * 100) / (element.quesionsNbr * 5)
      //       : (element.totalPoints * 100) / element.quesionsNbr;
      //   }
      // });

      const averageScore = calculateOverAllScore(candidate.results, project);
      const roundedScore = parseFloat(averageScore);

      let candidateInfo = await Candidate.findOne({
        Email: candidate.candidate,
      });

      candidatesScores.push({
        label: candidateInfo ? candidateInfo.First_name : "Unknown",
        value: roundedScore,
      });
    })
  );
  candidatesScores.sort((a, b) => b.value - a.value);
  if (candidatesScores.length > 5) {
    const top5Candidates = candidatesScores.slice(0, 5);
    return top5Candidates;
  } else return candidatesScores;
};

exports.getBestCandidate = async (req, res) => {
  let projectId = req.params.projectId;

  try {
    let candidates = await CandidateEvaluation.find({
      projectId: projectId,
      $or: [{ passed: true }, { passed: { $exists: false } }],
    });
    const project = await companyProject.findById(projectId);

    if (candidates.length === 0) {
      return res.status(400).send("no candidates found");
    }
    const averageData = await calculateAveragePoints(candidates);
    const candidatesScore = await canlculateCandidatesScore(
      candidates,
      project
    );
    let bestCandidate = candidates[0];
    candidates.map((candidate) => {
      if (
        calculateScore(candidate.results) >
        calculateScore(bestCandidate.results)
      ) {
        bestCandidate = candidate;
      }
    });
    const candidateInfo = await Candidate.findOne({
      Email: bestCandidate.candidate,
    });

    if (candidateInfo?.Avatar) {
      let avatar = await Photo_Files.findOne({
        _id: candidateInfo?.Avatar,
      });
      candidateInfo.Avatar = "";

      if (avatar) {
        let imgChunks = await Photo_chunks.find({
          files_id: avatar._id,
        });
        await Promise.all(
          imgChunks.map((chunk) => {
            candidateInfo.Avatar += chunk.data.toString("base64");
          })
        );
      }
    }
    res.send({ bestCandidate, candidateInfo, averageData, candidatesScore });
  } catch (error) {
    console.log({ error });
    res.status(400).send("there was an error");
  }
};

// const calculateScore = (points) => {
//   let score = 0;
//   points.map((point) => {
//     score = (point?.totalPoints * 100) / point?.quesionsNbr;
//   });
//   return score;
// };

const calculateOverAllScore = (results, project) => {
  if (!Array.isArray(results) || results.length === 0) return 0;

  // Ensure project.assessments is an array
  const assessmentsArray = Array.isArray(project?.assessments) ? project.assessments : [];

  // Filter out results with 'personalityResults'
  const filteredResults = results.filter(
    (result) => !result.personalityResults && !result.customResults
  );
  let score = 0;
  filteredResults.forEach((result) => {
    const assessment = assessmentsArray.find(
      (assessment) => assessment.name === result.assessmentName
    ) || {};

    if (result.totalPoints) {
      score +=
        ((result.totalPoints * 100) / result.quesionsNbr) *
        (assessment.weight || 1);
    } else if (result.rangesPoint) {
      score +=
        (result.rangesPoint / (result.quesionsNbr * 5)) *
        100 *
        (assessment.weight ? assessment.weight : 1);
    }
  });
  let totalWeights = 0;
  assessmentsArray
    .filter(
      (assess) =>
        assess.category != "Custom" && assess.category != "Personality"
    )
    .forEach((assess) => (totalWeights += 1 * (assess.weight || 1)));

  // Avoid division by zero
  let finalScore = totalWeights === 0 ? 0 : score / totalWeights;

  return finalScore.toFixed();
};

const calculateScore = (points) => {
  let score = 0;
  if (points.length === 0) {
    return 0;
  }
  points.forEach((element) => {
    score += (element.totalPoints * 100) / element.quesionsNbr;
  });
  return score / points.length;
};

function isDiscountExpired(expiresAt) {
  const currentDate = new Date();
  const expirationDate = new Date(expiresAt);
  return currentDate > expirationDate;
}

exports.getCredit = async (req, res) => {
  if (req.cookies.user && req.cookies.userLogged != "false") {
    let token = req.cookies.user;

    if (token) {
      jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
        if (err) {
          console.log({ err });
          return res.status(401).json({
            title: "unauthorized",
          });
        }

        try {
          let company = await Company.findOne({
            name: decoded.company_name,
          });

          if (!company) {
            return res.status(400).send("Company not found");
          }
          if (company.logo) {
            let logo = await Photo_Files.findOne({
              _id: company.logo,
            });
            company.logo = "";

            if (logo) {
              let imgChunks = await Photo_chunks.find({
                files_id: logo._id,
              });
              await Promise.all(
                imgChunks.map((chunk) => {
                  company.logo += chunk.data.toString("base64");
                })
              );
            }
          }

          for (const discount of company.discounts) {
            if (
              isDiscountExpired(discount.expiresAt) &&
              discount.status === "active"
            ) {
              company.freeCredit = 0;
              discount.status = "expired";
              await company.save();
            }
          }

          res.send({
            credit: company.credit + company.freeCredit,
            company_name: decoded.company_name,
            company_logo: company.logo,
            _id: company._id,
            plan: company.plan,
            recruiterEmail: decoded.email,
          });
        } catch (error) {
          console.log({ error });
          res.status(400).send("there was an error");
        }
      });
    }
  } else if (req.cookies.admin) {
    let token = req.cookies.admin;

    if (token) {
      jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
        if (err) {
          console.log({ err });
          return res.status(401).json({
            title: "unauthorized",
          });
        }
        try {
          res.send({
            admin_name: decoded.admin_name,
          });
        } catch (error) {
          console.log({ error });
          res.status(400).send("there was an error");
        }
      });
    }
  }
};

exports.inviteTeammate = async (req, res) => {
  try {
    const { email, company_name } = req.body;
    const company = await Company.findOne({ name: company_name });
    const recruiters = await User.find({
      company_name: { $in: [company_name] },
    });
    // if (company.recruiters_count <= recruiters.length) {
    //   console.log({ recruiters });
    //   return res.send({
    //     message:
    //       "Please upgrade your plan to invite more colleagues to your workplace!",
    //   });
    // }
    await companyService.sendInvitationEmail(email, company_name);
    // company.recruiters_count -= 1;
    // company.save();
    res.status(200).json({ message: "Email sent successfully!" });
  } catch (err) {
    console.log({ err });
    res.status(500).json({ message: "Internal server error!", err });
  }
};

exports.getCompanyTeamates = async (req, res) => {
  try {
    const { company_name, company_id } = req.body;
    const company = await Company.findOne({ name: company_name });
    const recruiters = await User.find({
      company_name: { $in: [company_name] },
    });
    const roles = await Role.find({ company: { $in: [company_id] } });

    console.log("THESES ARE THE TEAMATES");
    console.log(recruiters);

    console.log("THESES ARE THE TEAMATES");

    console.log("THESES ARE THE ROLES");
    console.log(roles);
    console.log("THESES ARE THE ROLES");
    // Combine the users and their roles
    const usersWithRoles = recruiters.map((user) => {
      const role = roles.find(
        (role) => role.user.toString() === user._id.toString()
      );
      return {
        ...user.toObject(),
        role: role ? role.type : "No role assigned", // Include role type or a default value
      };
    });
    console.log("THESES ARE THE users with ROLES");

    console.log(usersWithRoles);
    console.log("THESES ARE THE users with ROLES");

    res.send({ usersWithRoles });
    // if (company.recruiters_count <= recruiters.length) {
    //   console.log({ recruiters });
    //   return res.send({
    //     message:
    //       "Please upgrade your plan to invite more colleagues to your workplace!",
    //   });
    // }
    // company.recruiters_count -= 1;
    // company.save();
    //res.status(200).json({ message: "Email sent successfully!" });
  } catch (err) {
    console.log({ err });
    //res.status(500).json({ message: "Internal server error!", err });
  }
};

exports.redirectTeammate = async (req, res) => {
  const token = req.query.token;
  try {
    const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);

    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      return res.send("Link has expired.");
    }

    // Set additional data as query parameters
    const queryParams = `?company_name=${decoded.company_name}&email=${decoded.email}`;

    // Use template literals to include query parameters in the redirect URL
    const existingUser = await User.findOne({ email: decoded.email });
    if (existingUser) {
      const companyExists = existingUser.company_name.includes(
        decoded.company_name
      );
      if (companyExists) {
        return res.json("You're already part of this company,");
      }
      const newUser = await User.findOneAndUpdate(
        { _id: existingUser._id },
        { $push: { company_name: decoded.company_name } },
        { new: true }
      );
      const token = jwt.sign(
        {
          email: newUser.email,
          id: newUser._id,
          company_name: decoded.company_name,
        },
        process.env.SECRET_WORD,
        {
          expiresIn: "1h",
        }
      );
      if (process.env.NODE_ENV === "production") {
        const options = {
          domain: "server.go-platform.com",
          sameSite: "none",
          secure: true,
          path: "/",
          maxAge: 1000 * 60 * 60, // Cookie expires in 1 hour
          // httpOnly: true, // Set to true in production for HTTPS
        };

        res.cookie("user", token, options);

        return res
          .status(200)
          .redirect(
            `https://www.go-platform.com/dashboard?company_name=${decoded.company_name}`
          );
      }
      if (process.env.NODE_ENV === "dev") {
        const options = {
          maxAge: 1000 * 60 * 60, // Cookie expires in 1 hour
          // httpOnly: true, // Set to true in production for HTTPS
        };

        res.cookie("user", token, options);
        return res
          .status(200)
          .redirect(
            `http://localhost:8080/dashboard?company_name=${decoded.company_name}`
          );
      }
    }
    let redirectURL = "";
    if (process.env.NODE_ENV === "production") {
      redirectURL = `https://www.go-platform.com/InvitedCoworker${queryParams}`;
    }
    if (process.env.NODE_ENV === "dev") {
      redirectURL = `http://localhost:8080/InvitedCoworker${queryParams}`;
    }

    res.redirect(redirectURL);
  } catch (err) {
    console.error({ err });
    res.status(401).json({ message: "Invalid token!", err });
  }
};

exports.switchCompany = async (req, res) => {
  const company_name = req.query.company_name;
  let token = req.cookies.user;
  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        decoded.company_name = company_name;
        let company = await Company.findOne({
          name: company_name,
        });
        if (!company) {
          return res.status(400).send("Company not found");
        }

        var currentTimestamp = Date.now();
        // Check if three months have passed
        if (currentTimestamp >= company.endOfSubscription) {
          company.plan = "free";
          company.save();
        }

        const token = jwt.sign(
          {
            email: decoded.email,
            id: decoded.id,
            company_name: decoded.company_name,
            first_name: decoded.first_name,
            last_name: decoded.last_name,
          },
          process.env.SECRET_WORD,
          {
            expiresIn: "24h",
          }
        );
        let options = {};
        if (process.env.NODE_ENV === "production") {
          options = {
            domain: "server.go-platform.com",
            sameSite: "none",
            secure: true,
            path: "/",
            maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
            // httpOnly: true, // Set to true in production for HTTPS
          };
        }
        if (process.env.NODE_ENV === "dev") {
          options = {
            maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
          };
        }
        res.cookie("user", token, options);
        res.status(200).json({ company_name: company_name });
      } catch (error) {
        console.log({ error });
        res.status(400).send("there was an error");
      }
    });
  }
};

exports.companyLogo = async (req, res) => {
  const company_name = req.query.company_name;
  try {
    let company = await Company.findOne({
      name: company_name,
    });
    if (!company) {
      return res.status(400).send("Company not found");
    }
    if (company.logo) {
      let logo = await Photo_Files.findOne({
        _id: company.logo,
      });
      company.logo = "";

      if (logo) {
        let imgChunks = await Photo_chunks.find({
          files_id: logo._id,
        });
        imgChunks.map((chunk) => {
          company.logo += chunk.data.toString("base64");
        });
      }
    }
    res.send({ company });
  } catch (error) {
    console.log({ error });
    res.status(400).send("there was an error");
  }
};

const sendFeedBack = async (body) => {
  const { category, email, title, text } = body;

  const mailOptions = {
    from: process.env.COMPANY,
    to: "<EMAIL>",
    subject: category,
    text: `Title: ${title}\nEmail: ${email}\nDetails: ${text}`,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};

exports.companyFeedback = async (req, res) => {
  // const {category, email, title, text} = req.body
  try {
    await sendFeedBack(req.body);
    res.send({ msg: "Email sent successfully!" });
  } catch (error) {
    console.log(error);
    res.status(400).send("error: " + error.message);
  }
};

exports.getArchivedCandidates = async (req, res) => {
  let token = req.cookies.user;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {
        let companyProjects = await companyProject.find({
          company_name: decoded.company_name,
        });
        if (companyProjects.length === 0) {
          return res.status(400).send("no candidates found");
        }
        let candidatesData = [];
        let candidatesScores = [];
        // let invitationsSent = [];
        await Promise.all(
          companyProjects.map(async (project) => {
            let candidates = await CandidateEvaluation.find({
              projectId: project._id,
              status: "archived",
              $or: [{ passed: true }, { passed: { $exists: false } }],
            });

            await Promise.all(
              candidates.map((candidate) => {
                const candidateObject = candidate.toObject();
                candidateObject.projectId = project._id;
                const candidateScoreExists = candidatesScores.filter(
                  (existingCandidate) => {
                    return (
                      existingCandidate.candidate ==
                        candidateObject.candidate &&
                      existingCandidate.projectId == candidateObject.projectId
                    );
                  }
                );

                if (candidateScoreExists.length === 0) {
                  candidatesScores.push(candidateObject);
                }
              })
            );
            await Promise.all(
              candidates.map(async (candidate) => {
                let candidateFound = await Candidate.findOne({
                  Email: candidate.candidate,
                });
                if (candidateFound) {
                  const duplicateCandidates = candidatesData.filter(
                    (existingCandidate) => {
                      return existingCandidate.Email === candidateFound.Email;
                    }
                  );
                  if (duplicateCandidates.length === 0) {
                    candidatesData.push(candidateFound);
                  }
                }
              })
            );
          })
        );

        res.send({ candidatesData, candidatesScores });
      } catch (err) {
        console.log({ err });
      }
    });
  } else {
    res.status(400).send("no token found");
  }
};

exports.deleteTeamate = async (req, res) => {
  const { idU, CompanyName } = req.body;

  // Validate input
  if (!mongoose.Types.ObjectId.isValid(idU)) {
    return res.status(400).json({ error: "Invalid user ID" });
  }

  try {
    let company = await Company.findOne({
      name: CompanyName,
    });

    const user = await User.findById(idU);

    const index = user.company_name.indexOf(CompanyName);

    if (
      user.company_name.length === 1 &&
      index !== -1 &&
      user.company_name[0] === CompanyName
    ) {
      user.company_name.splice(index, 1); // Remove the item at the found index
      await user.save(); // Save the updated user
      // If yes, remove the user
      await User.findByIdAndDelete(idU);
      console.log("Deleted All user:");
    } else {
      user.company_name.splice(index, 1); // Remove the item at the found index
      await user.save(); // Save the updated user
      console.log("Updated User and deleted from company:", user);
    }

    return res
      .status(200)
      .json({ message: "Company deleted from user successfully" });
  } catch (error) {
    console.error("Error deleting company from user:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.UpdateTeamate = async (req, res) => {
  const { idU, CompanyID, Type } = req.body;
  // Validate input
  if (!mongoose.Types.ObjectId.isValid(idU)) {
    return res.status(400).json({ error: "Invalid user ID" });
  }

  if (!mongoose.Types.ObjectId.isValid(CompanyID)) {
    return res.status(400).json({ error: "Invalid company ID" });
  }

  if (!Type || typeof Type !== "string") {
    return res.status(400).json({ error: "Invalid role type" });
  }
  try {
    const user = await User.findById(idU);
    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }
    const role = await Role.findOneAndUpdate(
      { user: idU, company: CompanyID }, // Search criteria
      {
        user: mongoose.Types.ObjectId(idU),
        company: mongoose.Types.ObjectId(CompanyID),
        type: Type,
      }, // Data to update or insert
      { new: true, upsert: true } // Create if not found, return updated or created document
    ).populate("company user"); // Optional: Populate referenced fields if needed

    console.log("Role updated or created:", role);
    return role;
  } catch (error) {
    console.error("Error upserting role:", error);
    return res.status(500).json({ error: "Internal server error" });
  }
};

exports.getInvitedCandidates = async (req, res) => {
  let token = req.cookies.user;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      try {
        let companyProjects = await companyProject.find({
          company_name: decoded.company_name,
        });
        if (companyProjects.length === 0) {
          return res.status(400).send("no candidates found");
        }
        let candidatesData = [];
        let candidatesScores = [];
        let candidates;
        // let invitationsSent = [];
        await Promise.all(
          companyProjects.map(async (project) => {
            candidates = await CandidateEvaluation.find({
              projectId: project._id,
            });

            await Promise.all(
              candidates.map((candidate) => {
                const candidateObject = candidate.toObject();
                candidateObject.projectId = project._id;
                /*const candidateScoreExists = candidatesScores.filter(
                  (existingCandidate) => {
                    return (
                      existingCandidate.candidate ==
                        candidateObject.candidate &&
                      existingCandidate.projectId == candidateObject.projectId
                    );
                  }
                );*/

                /*if (candidateScoreExists.length === 0) {
                  candidatesScores.push(candidateObject);
                }*/
              })
            );
            /*await Promise.all(
              candidates.map(async (candidate) => {
                let candidateFound = await Candidate.findOne({
                  Email: candidate.candidate,
                });
                if (candidateFound) {
                  const duplicateCandidates = candidatesData.filter(
                    (existingCandidate) => {
                      return existingCandidate.Email === candidateFound.Email;
                    }
                  );
                  if (duplicateCandidates.length === 0) {
                    candidatesData.push(candidateFound);
                  }
                }
              })
            );*/
          })
        );
      } catch (err) {
        console.log({ err });
      }
    });
  } else {
    res.status(400).send("no token found");
  }
};

exports.updateCredit = async (req, res) => {
  const validDiscountCodes = ["JOMAG5", "SPECIAL10", "SUMMER20", "4EVER"]; // Add your valid discount codes
  const token = req.cookies.user;
  const discount = req.body.discount;
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    // Check if the discount is not already in the discounts array

    if (validDiscountCodes.includes(discount)) {
      let company = await Company.findOne({
        name: decoded.company_name,
      });

      if (company) {
        const appliedDiscount = company.discounts.find(
          (d) => d.name === discount
        );
        if (discount === "4EVER") {
          company.plan = "premium";
          await company.save();
        }
        const currentDate = new Date();
        // Check if the discount is not already in the discounts array
        if (!appliedDiscount) {
          // Add or update the discount with the current timestamp and expiration timestamp

          company.discounts.push({
            name: discount,
            appliedAt: currentDate,
            status: "active",
          });

          // Update the company credit by adding 5
          company.freeCredit += 5;

          // Save the updated company document
          await company.save();

          console.log(
            "Discount added and company credit updated successfully."
          );
          return res
            .status(200)
            .send({ message: "Free credit applied successfully!" });
        } else return res.status(400).send("Discount already applied!");
      } else return res.status(400).send("Company doesn't exist!");
    } else {
      let isValidCode = await companyService.isValidCode(discount);
      if (isValidCode) {
        console.log("code exists");

        let company = await Company.findOne({
          name: decoded.company_name,
        });
        company.plan = "lifetime";
        await company.save();
        await companyService.updateArray(discount);
        return res
          .status(200)
          .send({ message: "Lifetime code applied successfully!" });
      } else {
        console.log("Discount Not Available.");
        return res.status(400).send("Discount Not Available!");
      }
    }
  });
};

exports.fetchcompanies = async (req, res) => {
  const token = req.cookies.admin;
  const decoded = await verifyToken(token);
  try {
    if (decoded !== "Not Authorized") {
      let companies = await Company.find();
      if (!companies) {
        return res.status(204).json({ message: "No company found." });
      }
      const updatedCompanies = await Promise.all(
        companies.map(async (company) => {
          const totalProjects = await companyProject.countDocuments({
            company_name: company.name,
          });
          let companyObj = company.toObject(); // Convert Mongoose document to a plain object
          const users = await User.find({
            company_name: { $elemMatch: { $eq: company.name } },
          });

          // Create an array containing only user emails
          const usersEmails = [];
          users.map((user) => {
            usersEmails.push({
              name: user.first_name + " " + user.last_name,
              email: user.email,
            });
          });

          companyObj.totalProjects = totalProjects;
          companyObj.users = usersEmails;

          let latestProject = await companyProject
            .findOne({ company_name: company.name })
            .sort({ createdAt: -1 });

          if (latestProject) {
            companyObj.recentProjectDate = latestProject.createdAt; // Add the new field
            return companyObj; // Return the modified object
          }

          return company.toObject(); // If no latest project, return company as a plain object
        })
      );

      res.send(updatedCompanies);
    }
  } catch (err) {
    console.log(err);
    res.status(401).send("Not authorized");
  }
};

exports.deleteCompany = async (req, res) => {
  const token = req.cookies.admin;
  const decoded = await verifyToken(token);

  const ID = req.params.companyID;
  console.log("id to delete", ID);
  if (decoded === "Not Authorized") {
    res.status(401).json({ message: "Not authorized" });
  }
  try {
    const company = await Company.findById(ID);
    if (!company) return res.status(404).json({ message: "company not found" });
    const users = await User.find({ company_name: company.name });
    console.log({ users });
    const userPromises = users.map(async (user) => {
      // Check if the company_name array contains only that company's name
      if (
        user.company_name.length === 1 &&
        user.company_name[0] === company.name
      ) {
        // If yes, remove the user
        await User.findByIdAndDelete(user._id);
      } else {
        // If not, remove the company name from the array
        user.company_name = user.company_name.filter(
          (name) => name !== company.name
        );
        await user.save();
      }
    });

    // Wait for all user updates to complete
    await Promise.all(userPromises);
    await company.remove();
    await res.send({ deleted: true });
    console.log("company was deleted");
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ deleted: false, message: "Error deleting the company", err });
  }
};

exports.changeLogo = async (req, res) => {
  const token = req.cookies.user;
  const decoded = await verifyToken(token, process.env.SPECIAL_LINK_KEY);

  if (decoded === "Not Authorized") {
    res.status(401).json({ message: "Not authorized" });
  }
  let company = await Company.findOne({ name: decoded.company_name });
   // console.log(decoded.company_name );
    //console.log(decoded.email );
    //console.log(decoded.id);
    company.createdBy=decoded.id;
    company.email=decoded.email;
     
  let oldLogo = "";
  if (company.logo && !company.logo.toLowerCase().includes(".jpg")) {
    oldLogo = await Photo_Files.findOne({
      _id: company.logo,
    });
  }
  
  if (oldLogo) {
    company.logo = "";

    await Photo_chunks.deleteMany({
      files_id: oldLogo._id,
    });
  }

  try {
    if (req.file) {
      company.logo = req.file.id;
      // company.cover = req.files.cover[0]
      //   ? request.files.cover[0].id
      //   : "";
    }
    await company.save();

    res.status(200).send({ message: "logo saved successfully" });
  } catch (err) {
    console.log(err);
    res.status(500).json({ message: "Error Saving logo", err });
  }
};

exports.changeCover = async (req, res) => {
  const token = req.cookies.user;
  const decoded = await verifyToken(token, process.env.SPECIAL_LINK_KEY);

  if (decoded === "Not Authorized") {
    return res.status(401).json({ message: "Not authorized" });
  }
  let company = await Company.findOne({ name: decoded.company_name });
    company.createdBy=decoded.id;
    company.email=decoded.email;
  let oldCover = "";
  if (company.cover && !company.cover.toLowerCase().includes(".jpg")) {
    oldCover = await Photo_Files.findOne({
      _id: company.cover,
    });
  }

  if (oldCover) {
    company.cover = "";
    await Photo_chunks.deleteMany({
      files_id: oldCover._id,
    });
  }

  try {
    if (req.file) {
      company.cover = req.file.id;
    }
    await company.save();

    res.status(200).send({ message: "cover saved successfully" });
  } catch (err) {
    console.log(err);
    res.status(500).json({ message: "Error Saving logo", err });
  }
};

