const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const jobPositionSchema = new Schema({
  title: { type: String },
  recommended: [{ type: Schema.Types.ObjectId, ref: "allAssessments" }],
  description: {
    type: String,
  },
  category: {
    type: String,
  },
  requiredSkills: {
    type: [String],
  },
});
const JobPosition = mongoose.model("JobPosition", jobPositionSchema);
module.exports = JobPosition;
