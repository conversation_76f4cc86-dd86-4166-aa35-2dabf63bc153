const express = require("express");
const {
    getCaseStudies,
    postCaseStudy,
    updateCaseStudy,
    deleteCaseStudy,
    getCaseStudiesById,
    getCaseStudiesAdmin
} = require("../controllers/CaseStudyController");
const upload = require("../middleware/imageUpload");
const router = express.Router();


router.get("/", getCaseStudies);
router.get("/admin", getCaseStudiesAdmin);
router.get("/:caseStudyId", getCaseStudiesById);
router.post("/post", upload.single("image"), postCaseStudy);
router.put("/update/:caseStudyId", upload.single("image"), updateCaseStudy);
router.delete("/delete/:caseStudyId", deleteCaseStudy);

module.exports = router;
