const Portfolio = require('../models/Portfolio');



exports.createPortfolio = async (req, res) => {
    const { url, screenshots } = req.body;
    let user_token = req.cookies.candidate;
    if (user_token) {
        jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            try {
                const newPortfolio = new Portfolio({
                    candidate: decoded.id,
                    url,
                    screenshots
                });

                const savedPortfolio = await newPortfolio.save();
                res.status(201).json({ message: 'Portfolio entry created successfully', savedPortfolio });

            } catch (error) {
                console.error('Error adding portfolio:', error);
                res.status(500).json({ error: 'Internal server error' });
            }
        })
    }
};

// Get all portfolio entries
exports.getPortfolios = async (req, res) => {
    try {
        const portfolios = await Portfolio.find();
        res.status(200).json(portfolios);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

exports.getPortfolioByCandidate = async (req, res) => {
    try {
        let token = req.cookies.candidate;
        console.log("jn");

        jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            const portfolio = await Portfolio.findOne({ candidate: mongoose.Types.ObjectId(decoded.id) });

            if (!portfolio) {
                return res.status(204).json({ message: "No portfolio found." });
            }

            res.status(200).json(portfolio);
        })
    } catch (error) {
        console.error("Error retrieving preferences:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

// Get a single portfolio entry by ID
exports.getPortfolioById = async (req, res) => {
    try {
        const portfolio = await Portfolio.findById(req.params.id);
        if (!portfolio) return res.status(404).json({ error: "Portfolio not found" });
        res.status(200).json(portfolio);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Update a portfolio entry
exports.updatePortfolio = async (req, res) => {
    try {
        const updatedPortfolio = await Portfolio.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!updatedPortfolio) return res.status(404).json({ error: "Portfolio not found" });
        res.status(200).json(updatedPortfolio);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};

// Delete a portfolio entry
exports.deletePortfolio = async (req, res) => {
    try {
        const deletedPortfolio = await Portfolio.findByIdAndDelete(req.params.id);
        if (!deletedPortfolio) return res.status(404).json({ error: "Portfolio not found" });
        res.status(200).json({ message: "Portfolio deleted successfully" });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};
