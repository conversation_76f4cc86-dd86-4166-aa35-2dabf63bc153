const mongoose = require("mongoose");

const userSchema = mongoose.Schema({
  first_name: {
    type: String,
    //required: true,
    trim: true,
  },
  last_name: {
    type: String,
    //required: true,
    trim: true,
  },
  email: {
    type: String,
    required: true,
    trim: true,
    unique: true,
  },
  password: {
    type: String,
    required: true,
    trim: true,
  },
  register: {
    type: Date,
    default: Date.now(),
  },
  company_name: {
    type: Array,
    //required: true,
  },
  phone_nbr: {
    type: String,
  },
  tooltip: {
    type: Number,
    default: 0,
  },

  companies: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Company",
    },
  ],
  isProfileComplete: {
    type: Boolean,
    default: false,
  },

});

module.exports = mongoose.model("User", userSchema);
