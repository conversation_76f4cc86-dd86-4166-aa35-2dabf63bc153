const JobPosition = require("../models/JobPosition");
const Assessment = require("../models/allAssessments");

const getJobsByCategories = async (req, res) => {
  const { category } = req.query;
  console.log({ query: req.query });
  console.log({ category });
  const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
  const limit = parseInt(req.query.limit) || 10; // Default to 10 items per page if not provided
  const skip = (page - 1) * limit;

  try {
    if (category === "All") {
      const jobPositions = await JobPosition.find({})
        .sort({ title: 1 })
        .skip(skip)
        .limit(limit)
        .lean();

      const totalCount = await JobPosition.countDocuments();
      const totalPages = Math.ceil(totalCount / limit);
      console.log({ jobPositions: jobPositions[0] });
      await Promise.all(
        jobPositions.map(async (position) => {
          position.recommendations = (
            await Promise.all(
              position.recommended.map(async (recommended) => {
                let assessment = await Assessment.findOne({ _id: recommended });
                if (assessment) {
                  return assessment.name;
                }
                return null; // Explicitly return null if assessment is not found
              })
            )
          ).filter(Boolean);
        })
      );
      return res.status(200).json({ success: true, jobPositions, totalPages });
    }
    const jobPositions = await JobPosition.find({
      category: { $regex: new RegExp(category, "i") },
    })
      .sort({ title: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
    const query = category ? { category: category } : {};
    const totalCount = await JobPosition.countDocuments(query);
    const totalPages = Math.ceil(totalCount / limit);

    await Promise.all(
      jobPositions.map(async (position) => {
        position.recommendations = await Promise.all(
          position.recommended.map(async (recommended) => {
            let assessment = await Assessment.findOne({ _id: recommended });
            if (assessment) {
              return assessment.name;
            }
          })
        );
      })
    );
    return res.status(200).json({ success: true, jobPositions, totalPages });
  } catch (error) {
    console.log({ error });
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getJobPreview = async (req, res) => {
  const { jobTitle } = req.query;
  console.log({ jobTitle });
  try {
    const jobPosition = await JobPosition.findOne({ title: jobTitle }).lean();
    const assessments = [];
    let totalQst = 0;
    await Promise.all(
      jobPosition.recommended.map(async (assessment) => {
        let newAssessment = {};

        let assessmentData = await Assessment.findOne({ _id: assessment });
        newAssessment.name = assessmentData.name;
        newAssessment.description = assessmentData.description_test;
        newAssessment.sampleQst = assessmentData.questions_list[0].question;
        newAssessment.questionsNbr =
          assessmentData.questions_list.length > 25
            ? 20
            : assessmentData.questions_list.length;
        assessments.push(newAssessment);
        totalQst +=
          assessmentData.questions_list.length > 25
            ? 20
            : assessmentData.questions_list.length;
        if (assessmentData.questions_list.length > 25) {
        }
      })
    );
    console.log({ assessments });
    jobPosition.assessments = assessments;
    if (!jobPosition) {
      return res.status(404).json({ success: false, message: "Job not found" });
    }
    return res.status(200).json({ jobPosition, totalQst });
  } catch (error) {
    console.log({ error });
    return res.status(500).json({ success: false, message: error.message });
  }
};

const searchJobPos = async (req, res) => {
  const { search } = req.query;
  console.log({ search });
  try {
    const jobPositions = await JobPosition.find({
      title: { $regex: new RegExp(search, "i") },
    }).lean();
    console.log({ jobPositions });
    return res.status(200).json({ success: true, jobPositions });
  } catch (error) {
    console.log({ error });
    return res.status(500).json({ success: false, message: error.message });
  }
};

module.exports = { getJobsByCategories, getJobPreview, searchJobPos };
