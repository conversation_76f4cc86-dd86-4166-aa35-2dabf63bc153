const allAssessments = require("../models/allAssessments");
const validator = require("validator");
const jwt = require("jsonwebtoken");

const uploadAssessment = async (req, res) => {
  const assessment = req.body;
  let token = req.cookies.user;

  console.log({ token });

  if (token) {
    jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
      try {
        const uploadAssessment = new allAssessments({
          company: decoded.company_name,
          ...assessment,
        });

        const saveAs = await uploadAssessment.save();
        // assessmentToUpload.create(assessment);
        res
          .status(201)
          .json({ message: "Assessment created successfully!", newAs: saveAs });

        console.log({
          message: "Assessment created successfully!",
          newAs: saveAs,
        });
      } catch (err) {
        res.status(400).json({ message: err });
      }
    });
  } else {
    console.log("Assessment received:", assessment);
    try {
      const uploadAssessment = new allAssessments(assessment);

      // uploadAssessment.save();
      // assessmentToUpload.create(assessment);
      const saveAs = await uploadAssessment.save();
      res
        .status(201)
        .json({ message: "Assessment created successfully!", newAs: saveAs });
    } catch (err) {
      res.status(400).json({ message: err.message });
    }
  }
};

const updateAssessment = async (req, res) => {
  const assessment = req.body;
  const assessmentId = req.params.id;

  try {
    const assessmentToUpdate = await allAssessments.findOneAndUpdate(
      { _id: assessmentId },
      { $set: { ...assessment } },
      { returnOriginal: false, new: true }
    );

    res.status(201).json({
      message: "Assessment updated!",
      assessment: assessmentToUpdate,
    });
  } catch (err) {
    console.log("error------------================------------------", err);
    res.status(400).json({ message: err });
  }
};

const getAssessmentById = async (req, res) => {
  const id = req.params.id;
  try {
    const assessment = await allAssessments.findById(id);

    res.status(201).json({
      assessment,
    });
  } catch (err) {
    console.log("error------------================------------------", err);
    res.status(400).json({ message: err });
  }
};

const deleteAssessment = async (req, res) => {
  const id = req.params.id;
  try {
    const assessment = await allAssessments.findOneAndDelete(id);

    res.status(201).json({
      message: "Assessment deleted!",
      assessment,
    });
  } catch (err) {
    console.log("error------------================------------------", err);
    res.status(400).json({ message: err });
  }
};

const setDuration = async (req, res) => {
  const assessmentId = req.params.id; // Assuming you have the assessment ID in the URL
  const { duration } = req.body; // Extracting the duration from the request body

  try {
    // Check if the assessment exists
    const existingAssessment = await allAssessments.findById(assessmentId);

    if (!existingAssessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }

    // Update the duration field
    existingAssessment.duration = duration;

    // Save the updated assessment
    await existingAssessment.save();

    res.status(200).json({
      message: "Assessment updated successfully",
      updatedAssessment: existingAssessment,
    });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

const getEmptyAssessments = async (req, res) => {
  try {
    const assessments = await allAssessments.find({});
    let emptyAssessments = [];
    let assessWithNoOptions = [];
    await Promise.all(
      assessments.map((assessment) => {
        assessment.questions_list.map((question) => {
          if (
            (!question.options ||
              (!question.question && !question.description)) &&
            assessment.category !== "Custom" &&
            assessment.category !== "custom question" &&
            assessment.category !== "Essay"
          ) {
            console.log({
              assessment: assessment.name,
              caegory: assessment.category,
              questionNBR: question.question_number,
            });
            assessWithNoOptions.push({
              name: assessment.name,
              category: assessment.category,
              question: question,
            });
          }
        });
        if (
          !assessment.ranges &&
          !assessment.answers &&
          assessment.category !== "Personality"
        ) {
          emptyAssessments.push({
            name: assessment.name,
            category: assessment.category,
          });
        }
      })
    );
    // console.log({ assessWithNoOptions });
    console.log({
      assessWithNoOptions: assessWithNoOptions.length,
    });
    console.log({ emptyAssessments: emptyAssessments.length });
    res.status(200).json({ emptyAssessments });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

const getAllAssess = async (req, res) => {
  try {
    const assessments = await allAssessments.find({
      // category: { $ne: "Custom" },
      category: "Soft Skills",
    });

    const assessCount = await allAssessments.countDocuments({
      category: { $nin: ["Custom", "custom question"] },
    });

    console.log({ assessments: assessments.length, assessCount });
    res.status(200).json({ assessments });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

module.exports = {
  uploadAssessment,
  updateAssessment,
  getEmptyAssessments,
  setDuration,
  getAllAssess,
  getAssessmentById,
  deleteAssessment,
};
