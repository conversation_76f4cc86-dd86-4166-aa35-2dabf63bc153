const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const jobSchema = new Schema({
  imgFileId: { type: String },
  company_name: { type: String },
  contract: { type: String },
  title: { type: String },
  seniority: { type: String },
  description: { type: String },
  compensation: { type: String },
  department: { type: String },
  skills: [{ type: Array }],
  salary: { type: Number, default: 0 },
  hide_salary: { type: Boolean },
});
const Job = mongoose.model("Job", jobSchema);
module.exports = Job;
