const express = require("express");
const router = express.Router();
const CheaterController = require("../controllers/CheaterController");
//const { check } = require("express-validator");
const authentication = require("../middleware/candidateAuth");
//const upload = require("../middleware/imageUpload");

// Routes for the 'cheaters' resource
router.get('/cheaters/:id', CheaterController.getAllCheaters);
router.get('/', CheaterController.getAll);
router.get('/cheater/:id', CheaterController.getCheater);
router.get('/cheater/:email/:project_id', CheaterController.getCheaterByCandidateAndProject);
router.get('/potential-cheaters/:id', CheaterController.getAllPotentialCheaters);
router.post('/create-photo/:idCandidate', CheaterController.createCapturedPhoto);
router.post('/create-cheater/:id', CheaterController.createCheater);
router.get('/photos/:id', CheaterController.getAllPhotosbyCandidate);
router.put('/changeToCheater/:id', CheaterController.changeToCheater);
router.delete('/delete-cheater/:id', CheaterController.deleteCheater);
router.delete('/delete-photos/:id', CheaterController.deletePhotos);



module.exports = router;
