const geoip = require('geoip-lite');

const domainRedirect = (req, res, next) => {
  const currentHost = req.hostname;

  // Only apply redirection logic if the domain is go-platform.com
  if (currentHost === 'go-platform.com' || currentHost === 'www.go-platform.com') {
    // For testing, allow overriding IP via 'x-forwarded-for' header.
    // In production, a proxy/load balancer would set the real IP here.
    const ip = req.headers['x-forwarded-for'] || req.ip;
    let geo = geoip.lookup(ip);

    // Check if the user's country is Algeria (DZ)
    if (geo && geo.country === 'DZ') {
      const targetUrl = `https://go-profiling.dz${req.originalUrl}`;
      // Use a 301 redirect for permanent redirection
      return res.redirect(301, targetUrl);
    }
  }

  // If no redirection is needed, proceed to the next middleware
  next();
};

module.exports = domainRedirect;


/*
const geoip = require('geoip-lite');

const domainRedirect = (req, res, next) => {
  const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress || (req.connection.socket ? req.connection.socket.remoteAddress : null);
  let geo = geoip.lookup(ip);

  // If geolocation data exists and user is from Algeria
  if (geo && geo.country === 'DZ') {
    const host = req.get('host');

    // Only redirect if not already on the Algerian domain
    if(!host.includes('go-profiling.dz')) {
      const newUrl = `https://go-profiling.dz${req.originalUrl}`;
      return res.redirect(301, newUrl);
    }
  }

  // If no redirection is needed, proceed to the next middleware
  next();
};

module.exports = domainRedirect;
*/
