const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const companyAssessment = new Schema({
  // assessmentId: {
  //   type: String,
  // },
  company_name: {
    type: String,
  },
  jobTitle: {
    type: String,
  },
  assessments: {
    type: Array,
  },
  name: {
    type: String,
  },
  recommanded_score: {
    type: Number,
    default: 70,
  },
  min_score: {
    type: Number,
    default: 0,
  },
  duration: {
    type: Number,
  },
  qst_num: {
    type: Number,
  },
  archived: {
    type: Boolean,
    default: false,
  },
});
const CompanyAssessment = mongoose.model(
  "CompanyAssessment",
  companyAssessment
);
module.exports = CompanyAssessment;
