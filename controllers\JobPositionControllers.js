const JobPosition = require("../models/JobPosition");
const Assessment = require("../models/allAssessments");
const axios = require("axios");
const Company = require("../models/Company");
const OpenAI = require("openai");
const { verifyToken } = require("../helper/verifyToken.js");

const client = new OpenAI({
  apiKey: process.env.OPEN_AI_KEY, // This is the default and can be omitted
});

const createJobPosition = async (req, res) => {
  const recommendations = req.body;
  console.log({ recommendations });
  try {
    const recommendedAssessmentNames = req.body.recommended || [];
    // console.log({ recommendedAssessmentNames });
    recommendations.map(async (recommendation) => {
      // Construct an array of case-insensitive regex queries for each recommended name
      const regexQueries = recommendation.recommended.map(
        (name) => new RegExp(name, "i")
      );

      // Find assessments with names that match any of the recommended names
      const assessments = await Assessment.find({
        name: { $in: regexQueries },
      });

      const assessmentIds = await assessments.map(
        (assessment) => assessment._id
      );
      const newJobPosition = new JobPosition({
        title: recommendation.title,
        recommended: assessmentIds,
      });
      console.log({ newJobPosition });
      await newJobPosition.save();
    });
    return res
      .status(201)
      .json({ success: true, message: "Job position created successfully" });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

const updateJobPosition = async (req, res) => {
  try {
    const jobs = req.body;
    await Promise.all(
      jobs.map(async (job) => {
        let position = await JobPosition.findOne({ title: job.title });
        if (!position) {
          return;
        }
        // position.category = job.category;
        // position.description = job.description;
        position.requiredSkills = job.requiredSkills;

        await position.save();
      })
    );
    // const assessments = await Assessment.find({
    //   name: { $in: recommendedAssessmentNames },
    // });
    // const assessmentIds = assessments.map((assessment) => assessment._id);

    // const updatedJobPosition = await JobPosition.findByIdAndUpdate(
    //   jobId,
    //   {
    //     title: req.body.title,
    //     recommended: assessmentIds,
    //   },
    //   { new: true }
    // );

    // if (!updatedJobPosition) {
    //   return res
    //     .status(404)
    //     .json({ success: false, message: "Job position not found" });
    // }

    return res
      .status(200)
      .json({ success: true, message: "Job position updated successfully" });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

const deleteJobPosition = async (req, res) => {
  try {
    const jobId = req.params.id;

    const deletedJobPosition = await JobPosition.findByIdAndDelete(jobId);

    if (!deletedJobPosition) {
      return res
        .status(404)
        .json({ success: false, message: "Job position not found" });
    }

    return res
      .status(200)
      .json({ success: true, message: "Job position deleted successfully" });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getAllJobPositions = async (req, res) => {
  try {
    const jobPositions = await JobPosition.find().lean();
    // Assuming jobPositions is your array of objects
    jobPositions.sort((a, b) => {
      const titleA = a.title?.toUpperCase(); // ignore upper and lowercase
      const titleB = b.title?.toUpperCase(); // ignore upper and lowercase

      if (titleA < titleB) {
        return -1;
      }
      if (titleA > titleB) {
        return 1;
      }
      // titles must be equal
      return 0;
    });

    await Promise.all(
      jobPositions.map(async (job) => {
        delete job.recommended;
      })
    );

    return res.status(200).json({ success: true, data: jobPositions });
  } catch (error) {
    console.log({ error });
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getJobPositionById = async (req, res) => {
  try {
    const jobId = req.params.id;
    const jobPosition = await JobPosition.findById(jobId).populate(
      "recommended"
    );

    if (!jobPosition) {
      return res
        .status(404)
        .json({ success: false, message: "Job position not found" });
    }

    return res.status(200).json({ success: true, data: jobPosition });
  } catch (error) {
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getTitles = async (req, res) => {
  try {
    const jobPositions = await JobPosition.find({}, "title");
    let jobsTitles = [];
    await Promise.all(
      jobPositions.map(async (job) => {
        jobsTitles.push(job.title);
      })
    );
    return res.status(200).json({ jobsTitles });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ success: false, message: error.message });
  }
};

async function fetchAllJobPositions(start = 1, end = 1000) {
  try {
    const ONET_BASE_URL =
      "https://services.onetcenter.org/ws/online/occupations";
    const apiKey = "c2hvcHNwaGVyZTo0NTQydWZn";

    // Send GET request to O*NET API
    const response = await axios.get(ONET_BASE_URL, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${apiKey}`,
      },
      params: {
        start,
        end,
        api_key: apiKey,
      },
    });

    // Return the job positions data
    return response.data;
  } catch (error) {
    console.error("Error fetching job positions:", error.message);
    throw error;
  }
}

const getSkillsRecommendations = async (req, res) => {
  // Create a client, specifying your API key
  try {
    const positions = await fetchAllJobPositions();
    return res.status(200).json({ success: true, data: positions });
  } catch (error) {
    console.error(
      "Error:",
      error.response ? error.response.data : error.message
    );
    return res.status(500).json({ success: false, message: error.message });
  }
};

const getRecommended = async (req, res) => {
  const token = req.cookies.user;
  console.log({ tokenFROMJOBRECOMMENDATIONS: token });
  try {
    const { title, essentials } = req.query;
    console.log({ essentials, title });

    const decoded = await verifyToken(token);
    if (decoded === "Not Authorized") {
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    let company = await Company.findOne({
      name: decoded.company_name,
    });

    if (company.plan === "free" || essentials === "essentials") {
      const essentialAssessments = await Assessment.find({
        rating: "essential",
      });
      console.log({ essentialAssessments });
      let assessmentsIds = essentialAssessments.map(
        (assessment) => assessment._id
      );
      return res
        .status(200)
        .json({ success: true, recommendedIds: assessmentsIds });
    }

    const jobPosition = await JobPosition.findOne({ title });

    const requirements = jobPosition.requiredSkills.map((skill) =>
      skill.replace("-", " ")
    );

    const recommendedAssessments = await Assessment.find({
      name: {
        $in: requirements.map(
          (skill) => new RegExp(escapeRegex(skill), "i") // Match skill as part of the name
        ),
      },
    });
    let recommendedIds = recommendedAssessments.map(
      (assessment) => assessment._id
    );
    if (recommendedIds.length == 0) {
      console.log("WE'RE HEREE>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
      const essentialAssessments = await Assessment.find({
        rating: "essential",
      });
      recommendedIds = essentialAssessments.map((assessment) => assessment._id);
    }
    console.log({ recommendedIds });
    return res
      .status(200)
      .json({ success: true, recommendedIds: recommendedIds });
  } catch (error) {
    console.error(
      "Error:",
      error.response ? error.response.data : error.message
    );
    return res.status(500).json({ success: false, message: error.message });
  }
};

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // Escape special characters
}

const deletePosition = async (req, res) => {
  const token = req.cookies.admin;
  const decoded = await verifyToken(token);

  const ID = req.params.jobId;
  console.log("id to delete", ID);
  if (decoded === "Not Authorized") {
    res.status(401).json({ message: "Not authorized" });
  }
  try {
    const jobPosition = await JobPosition.findOne({ _id: ID });
    console.log("jobPosition to delete", jobPosition);
    await jobPosition.remove();
    res.status(200).send({ deleted: true });
  } catch (err) {
    console.log("Error deleting job", err);
    res.status(500).send("error deleting job");
  }
};

const getAllJobPositionsPerPage = async (req, res) => {
  try {
    console.log({ QUERY: req.query });
    const page = parseInt(req.query.page) || 1; // Default to page 1 if not provided
    const limit = parseInt(req.query.limit) || 300; // Default to 10 items per page

    const skip = (page - 1) * limit;

    const jobPositions = await JobPosition.find()
      .sort({ title: 1 })
      .skip(skip)
      .limit(limit); // Assuming jobPositions is your array of objects
    const totalCount = await JobPosition.countDocuments();

    // Calculate the total number of pages
    const totalPages = Math.ceil(totalCount / limit);

    // jobPositions.sort((a, b) => {
    //   const titleA = a.title?.toUpperCase(); // ignore upper and lowercase
    //   const titleB = b.title?.toUpperCase(); // ignore upper and lowercase

    //   if (titleA < titleB) {
    //     return -1;
    //   }
    //   if (titleA > titleB) {
    //     return 1;
    //   }
    //   // titles must be equal
    //   return 0;
    // });

    return res
      .status(200)
      .json({ success: true, data: jobPositions, totalPages });
  } catch (error) {
    console.log({ error });
    return res.status(500).json({ success: false, message: error.message });
  }
};

const editJobPositions = async (req, res) => {
  let token = req.cookies.admin;
  const decoded = await verifyToken(token);

  // console.log("id to delete", ID);
  if (decoded === "Not Authorized") {
    res.status(401).json({ message: "Not authorized" });
  }
  // const ID = req.params.jobId;
  try {
    let newAssessment = req.body;
    let updatedJob = await JobPosition.findOneAndUpdate(
      { _id: newAssessment._id },
      { $set: newAssessment },
      { new: true } // To return the updated document
    );
    console.log({ updatedJob });
    res.status(200).send(newAssessment);
  } catch (err) {
    console.log(err);
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  createJobPosition,
  updateJobPosition,
  deleteJobPosition,
  getAllJobPositions,
  getJobPositionById,
  getTitles,
  getSkillsRecommendations,
  getRecommended,
  deletePosition,
  getAllJobPositionsPerPage,
  editJobPositions,
};
