const mongoose = require("mongoose");
const autopopulate = require('mongoose-autopopulate');

const capturedPhotosSchema = mongoose.Schema({
    idCandidate: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Cheater',
        required: true,
        autopopulate: true,
    },
    image: {
        type: String
    },
    reason: {
        type: String,
        enum: [
            'multiple_faces',
            'non_matching_face',
            'face_disappeared',
            'face_reappeared',
            'looking_away',
            'mobile_device_detected',
            'reading_detected',
            'unknown'
        ],
        default: 'unknown'
    },
    Date: {
        type: Date,
        default: Date.now,
        //  required: true,
    },
});
capturedPhotosSchema.plugin(autopopulate);
const CapturedPhotos = mongoose.model('CapturedPhotos', capturedPhotosSchema);
module.exports = CapturedPhotos