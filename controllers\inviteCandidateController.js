const jwt = require("jsonwebtoken");
const nodemailer = require("nodemailer");
const CandidateEvaluation = require("../models/CandidateEvaluation");
const ejs = require("ejs");
const Invitation = require("../models/Invitations");
const Company = require("../models/Company");
const validator = require("validator");
const companyProject = require("../models/companyProject");
const InvitedCandidate = require("../models/InvitedCandidate");

const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.COMPANY,
    pass: process.env.PASS,
  },
});

const sendAssessmentEmail = async (
  candidateEmail,
  projectId,
  company_name,
  jobTitle,
  seniority
) => {
  const expirationTime = Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60; // 3 days
  let candidatEmail = validator.normalizeEmail(candidateEmail, {
    gmail_remove_dots: false,
  });
  const projectd = validator.escape(projectId);
  let specialToken = jwt.sign(
    {
      email: candidatEmail,
      projectId: projectd,
      exp: expirationTime,
    },
    process.env.SPECIAL_LINK_KEY
  );
  let link = "";
  if (process.env.NODE_ENV === "dev") {
    link = `http://localhost:3000/inviteCandidate/?token=${specialToken}`;
  }
  if (process.env.NODE_ENV === "production") {
    link = `https://server.go-platform.com/inviteCandidate/?token=${specialToken}`;
  }

  // const assessmentLink = `http://localhost:3000/assessment/${specialToken}`;
  const template = await ejs.renderFile(
    "./views/newTalentInvitationTemplate.ejs",
    {
      data: {
        link: link,
        email: candidateEmail,
        companyName: company_name,
        jobTitle: jobTitle,
        seniority: seniority,
      },
    }
  );

  const mailOptions = {
    from: process.env.COMPANY,
    to: candidateEmail,
    subject: "Assessment Link",
    html: template,
  };

  await transporter.sendMail(mailOptions);
  console.log("Email sent successfully!");
};

const multiInviteLink = async (req, res) => {
  let token = req.cookies.user;
  let projectId = req.params.projectId;
  console.log({ projectId: projectId });
  try {
    const project = await companyProject.findOne({ _id: projectId });
    // const expdate = Math.floor(project?.createdAt / 1000) + 3 * 24 * 60 * 60;
    if (!project) {
      return res.status(400).json({ message: "Project not found" });
    }

    const companyname = project.company_name;


    let link = "";
    let options = {};

    if (process.env.NODE_ENV === "dev") {
      link = `http://localhost:8080/invite?company=${companyname}&project=${projectId}`;
      options = {
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }
    if (process.env.NODE_ENV === "production") {
      link = `https://www.go-platform.com/invite?company=${companyname}&project=${projectId}`;
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: 1000 * 60 * 60 * 24, // Cookie expires in 1 hour
        // httpOnly: true, // Set to true in production for HTTPS
      };
    }

    res.cookie("invitationLink", link, options);

    res.status(200).json({ generatedLink: link });
  } catch (err) {
    console.log({ err });
    res.status(500).json({ message: "Internal server error!", err });
  }
};

const selfInvite = async (req, res) => {
  const companyName = req.query.companyName;
  const projectId = req.query.project;
  const { email, first_name, last_name } = req.body;

  try {
    const company = await Company.findOne({ name: companyName });
    if (!company) {
      return res
        .status(404)
        .json({ message: "Company not found", err: err.message });
    }
    const project = await companyProject.findOne({ _id: projectId });

    if (!project) {
      return res.status(404).json({ message: "Project not found" });
    }

    await sendAssessmentEmail(
      email,
      projectId,
      companyName,
      project.jobTitle,
      project.seniority
    );
    let invitation = new Invitation({
      first_name: first_name,
      last_name: last_name,
      Email: email,
      projectId: projectId,
    });
    console.log({ invitation });
    await invitation.save();

    res.status(200).json({
      message:
        "Email sent successfully! Check your inbox to pass the assessment.",
      success: true,
    });
  } catch (error) {
    console.log(error);
    res.status(404).json({ message: "Invalid URL" });
  }
};

const generateLink = async (req, res) => {
  let token = req.cookies.user;
  console.log(req.body);
  const { candidates, projectId, company_name } = req.body;
  console.log({ token });
  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      let company = await Company.findOne({
        name: decoded.company_name,
      });
      const project = await companyProject.findOne({ _id: projectId });
      console.log({ project });
      const projectd = validator.escape(projectId);
      if (company.plan == "pay-per-use") {
        if (candidates.length > company.credit) {
          return res.json({
            message: "You don't have enough credits to send all invitations!",
          });
        }
      }
      // Use a lock mechanism to prevent parallel saves
      for (const candidate of candidates) {
        console.log({ candidate, CREDIT: company.credit });
        if (company.credit > 0 && company.plan == "pay-per-use") {
          company.credit -= 1;
          await company.save();
        }
        let email = validator.normalizeEmail(candidate.email, {
          gmail_remove_dots: false,
        });
        await sendAssessmentEmail(
          email,
          projectd,
          decoded.company_name,
          project.jobTitle,
          project.seniority
        );
        let invitation = new Invitation({
          first_name: candidate.first_name,
          last_name: candidate.last_name,
          Email: email,
          projectId: projectd,
        });
        console.log({ invitation });
        await invitation.save();

        // Also save to InvitedCandidate collection for the new "Invited Candidates" tab
        try {
          const existingInvitedCandidate = await InvitedCandidate.findOne({
            email: email,
            UserId: decoded.id,
            projectId: projectd
          });

          if (existingInvitedCandidate) {
            // If candidate already exists for this project, increment the counter
            existingInvitedCandidate.invitedCounter += 1;
            await existingInvitedCandidate.save();
            console.log(`Updated invited candidate counter for ${email} in project ${projectd}`);
          } else {
            // Create new invited candidate record for this project
            const newInvitedCandidate = new InvitedCandidate({
              UserId: decoded.id,
              first_name: candidate.first_name,
              last_name: candidate.last_name,
              email: email,
              projectId: projectd,
              invitedCounter: 1
            });
            await newInvitedCandidate.save();
            console.log(`Created new invited candidate record for ${email} in project ${projectd}`);
          }
        } catch (invitedCandidateError) {
          console.error("Error saving to InvitedCandidate collection:", invitedCandidateError);
          // Don't fail the main invitation process if this fails
        }
      }

      res.status(200).json({ message: "Email sent successfully!" });
    } catch (err) {
      console.log({ err });
      res.status(500).json({ message: "Internal server error!", err });
    }
  });
};

const redirectLink = async (req, res) => {
  const token = req.query.token;
  console.log({ bodyTTTT: token });
  try {
    const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);

    if (decoded.exp < Math.floor(Date.now() / 1000)) {
      return res.send("Link has expired.");
    }
    //console.log({ decoded });
    //console.log("lmfsqml");
    const fiveDaysInMilliseconds = 5 * 24 * 60 * 60 * 1000;
    let options = {};

    if (process.env.NODE_ENV === "production") {
      options = {
        domain: "server.go-platform.com",
        sameSite: "none",
        secure: true,
        path: "/",
        maxAge: fiveDaysInMilliseconds, // Set an appropriate max age in milliseconds
        httpOnly: true, // Makes the cookie accessible only via HTTP (not JavaScript)
      };
      res.cookie("candidatecookie", token, options);
      /*function getOrdinalSuffix(day) {
        if (day > 3 && day < 21) return 'th'; // special case for 11th-13th
        switch (day % 10) {
            case 1:  return "st";
            case 2:  return "nd";
            case 3:  return "rd";
            default: return "th";
        }
    }

    function formatDate(date) {
      const options = { month: 'long', day: 'numeric', year: 'numeric' };
      const dateString = date.toLocaleDateString('en-US', options);
      
      const parts = dateString.split(' ');
      const day = parseInt(parts[1].replace(',', ''));
      const suffix = getOrdinalSuffix(day);
  
      return `${parts[0].toUpperCase()} ${day}${suffix} ${parts[2]}`;
  }

      const candidate = await Invitation.findOne({projectId:decoded.projectId,Email:decoded.email});
      if (!candidate) {
          return res.status(404).send('Candidate not found');
      }

      const date = formatDate(candidate.createdAt);
      const data = { email: decoded.email,date:date };
      const queryString = new URLSearchParams(data).toString();*/
      //assessment.go-platform.com
      res.redirect(`https://assessment.go-platform.com`);
    }
    if (process.env.NODE_ENV === "dev") {
      options = {
        maxAge: fiveDaysInMilliseconds, // Set an appropriate max age in milliseconds
      };
      res.cookie("candidatecookie", token, options);
      res.redirect(`http://localhost:8081`);
    }
  } catch (err) {
    console.log({ err }, "Invalid token!");
    res.status(401).json({ message: "Invalid token!" });
  }
};

const createTalent = async (req, res) => {
  try {
    const { email } = req.body;
    let projectId = "656dd5e2a26850987a567af8";
    companyName = "Go Platform";
    let emaiil = validator.normalizeEmail(email, { gmail_remove_dots: false });
    await sendAssessmentEmail(emaiil, projectId, companyName);
    const invitation = new Invitation({
      Email: emaiil,
      projectId: "656dd5e2a26850987a567af8",
    });
    await invitation.save();
    res.status(200).json({
      message:
        "Email sent successfully!, Please check your email to pass the test",
    });
  } catch (err) {
    console.log({ err });
    res.status(500).json({ message: "Internal server error!", err });
  }
};

const getCandidate = async (req, res) => {
  const token = req.cookies.candidatecookie;

  
  //console.log('Token:', token);
  const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);

  if (!decoded.projectId) {
    return;
  }

  function getOrdinalSuffix(day) {
    if (day > 3 && day < 21) return "th"; // special case for 11th-13th
    switch (day % 10) {
      case 1:
        return "st";
      case 2:
        return "nd";
      case 3:
        return "rd";
      default:
        return "th";
    }
  }

  function formatDate(date) {
    const options = { month: "long", day: "numeric", year: "numeric" };
    const dateString = date.toLocaleDateString("en-US", options);

    const parts = dateString.split(" ");
    const day = parseInt(parts[1].replace(",", ""));
    const suffix = getOrdinalSuffix(day);

    return `${parts[0].toUpperCase()} ${day}${suffix} ${parts[2]}`;
  }

  const inivitation = await Invitation.findOne({
    projectId: decoded.projectId,
    Email: decoded.email,
  });
  if (!inivitation) {
    return res.status(404).send("inivitation not found");
  }

  //console.log(inivitation.createdAt);
  const date = formatDate(inivitation.createdAt);
  const exp = decoded.exp;
  // exp date
  const timestamp = parseInt(exp, 10);
  const date2 = new Date(timestamp * 1000); // Convert from seconds to milliseconds

  // Define options for formatting
  const options = {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZoneName: "short",
    timeZone: "America/New_York",
  };

  // Format the date using Intl.DateTimeFormat
  const formatter = new Intl.DateTimeFormat("en-US", options);
  const parts = formatter.formatToParts(date2);

  // Manually assemble the formatted date string
  const month = parts.find((part) => part.type === "month").value.toUpperCase();
  const day = parts.find((part) => part.type === "day").value;
  const year = parts.find((part) => part.type === "year").value;
  const hour = parts.find((part) => part.type === "hour").value;
  const minute = parts.find((part) => part.type === "minute").value;
  const timeZoneName = parts.find((part) => part.type === "timeZoneName").value;

  // Format the date into a readable string
  const formattedDate = `${month} ${day} ${year} AT ${hour}:${minute} ${timeZoneName}`;

  //deadline
  // Get current time
  const now = new Date();
  // Define the deadline
  const deadline = new Date(decoded.exp * 1000); // ISO 8601 format
  // Calculate the difference in milliseconds

  //console.log(now);

  const diffInMs = deadline - now;

  // Convert milliseconds to days, hours, minutes, seconds
  const diffInSeconds = Math.floor(diffInMs / 1000);
  const seconds = diffInSeconds % 60;
  const minutes = Math.floor(diffInSeconds / 60) % 60;
  const hours = Math.floor(diffInSeconds / 3600) % 24;
  const days = Math.floor(diffInSeconds / 86400);

  // Create the remaining time message
  let message = "Your deadline is in ";
  if (days > 0) message += `${days} day${days > 1 ? "s" : ""} `;
  if (hours > 0 || days > 0) message += `${hours} hour${hours > 1 ? "s" : ""} `;
  // console.log(days);

  //assesment
  const project = await companyProject.findOne({ _id: decoded.projectId });
  const number = project.assessments.length;

  //pass test or no
  const pass = await CandidateEvaluation.findOne({
    projectId: decoded.projectId,
    candidate: decoded.email,
  });
  if (pass) {
    const creat = pass.createdAt;
    var passed = true;
    var data1 = {
      email: decoded.email,
      date: date,
      exp_date: formattedDate,
      deadline: message,
      number_of_assessement: number,
      pass: true,
      creatAt: creat,
      jobPosition: project.jobTitle,
      company: project.company_name,
    };
  } else {
    var passed = false;
    var data1 = {
      email: decoded.email,
      date: date,
      exp_date: formattedDate,
      deadline: message,
      number_of_assessement: number,
      pass: false,
      jobPosition: project.jobTitle,
      company: project.company_name,
    };
  }
  //const data1 = { email: decoded.email,date:date, exp:formattedDate, deadline:message,ass:number,pass:true };
  res.status(200).json(data1);
};

const getEmail = async (req, res) => {
  const token = req.cookies.candidatecookie;

  //console.log('Token:', token);
  const decoded = jwt.verify(token, process.env.SPECIAL_LINK_KEY);
  if (decoded.email) {
    res.status(200).json({ resoonse: "1" });
  } else {
    res.status(200).json({ resoonse: "2" });
  }
};

module.exports = {
  generateLink,
  redirectLink,
  createTalent,
  getCandidate,
  getEmail,
  selfInvite,
  multiInviteLink,
};
