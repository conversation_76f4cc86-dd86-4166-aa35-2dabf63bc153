require("dotenv").config();
const mongoose = require("mongoose");
const connectDB = require("./config/dbConn");

const app = require("./app.js");
const server = require("http").Server(app);
const io = (module.exports.io = require("socket.io")(server, {
  cors: true,
}));

const socketManager = require("./config/socketManager");

const PORT = process.env.PORT || 3000;

// Connect to mongoDB
connectDB();

io.on("connection", socketManager);

mongoose.connection.once("open", () => {
  console.log("Connected to MongoDB");
  server.listen(PORT, "0.0.0.0", () => {
    console.log(`server running on  http://localhost:${PORT}`);
    console.log("From socket express server");
  });
});
