// Assessment TEST for the Company
const AssessmentTest = require("../models/AssessmentTest.js");
const Assessment = require("../models/allAssessments.js");
const CompanyAssessment = require("../models/companyAssessment.js");
const CodingChallenge = require("../models/Challenge.js");

const jwt = require("jsonwebtoken");
const User = require("../models/user");
const allAssessments = require("../models/allAssessments");
const CandidateEvaluation = require("../models/CandidateEvaluation.js");
const CandidateRating = require("../models/candidateRating.js");
const companyProject = require("../models/companyProject");
const validator = require("validator");
const Company = require("../models/Company");
const {
  getAssessmentsForTalent,
  evaluateTalent,
} = require("./talentAssessmentController.js");

const { sendEmail } = require('../utils/email');
const ejs = require('ejs');
const path = require('path');
const fs = require('fs');
        
const InvitedCandidate = require('../models/InvitedCandidate');
const Candidate = require('../models/Candidate');

const getAllAssessmentTest = async (req, res) => {
  const assessmentTest = await AssessmentTest.find();
  if (!assessmentTest)
    return res.status(204).json({ message: "No AssessmentTest found." });
  res.json(assessmentTest);
};
const getAssessmentTest = async (req, res) => {
  if (!req?.params?.id)
    return res.status(400).json({ message: "AssessmentTest ID required." });

  const assessmentTest = await AssessmentTest.findOne({
    _id: validator.escape(req.params.id),
  }).exec();
  if (!assessmentTest) {
    return res
      .status(204)
      .json({ message: `No AssessmentTest matches ID ${req.params.id}.` });
  }
  res.json(assessmentTest);
};

const createAssessmentTest = async (req, res) => {
  const assessmentTest = req.body;
  try {
    await AssessmentTest.create(assessmentTest);
    res.status(201).json({ message: "AssessmentTest created successfully!" });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

const deleteAssessmentTest = async (req, res) => {
  try {
    await Assessment.findByIdAndDelete(req.params.id);
    res.status(201).json({ message: "AssessmentTest Deleted successfully!" });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

const getCompanyAssessments = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    const recruiter = await User.findOne({ _id: validator.escape(decoded.id) });
    const company_name = recruiter.company_name;

    try {
      if (!company_name)
        return res.status(400).json({ message: "Company id required." });

      const assessmentTest = await CompanyAssessment.find({
        company_name: validator.escape(decoded.company_name),
      });

      if (!assessmentTest) {
        return res
          .status(404)
          .json({ message: `No AssessmentTest matches company id.` });
      }

      // Return the found assessmentTest
      res.status(200).json(assessmentTest);
    } catch (error) {
      // Handle any unexpected errors
      console.error(error);
      res.status(500).json({ message: "Internal server error." });
    }
  });
};

const createCompanyAssessments = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  const assessmentTest = req.body;

  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      const assessment = await CompanyAssessment.create(assessmentTest);
      await assessment.save();

      res.status(201).json({ message: "AssessmentTest created successfully!" });
    } catch (err) {
      res.status(400).json({ message: err.message });
    }
  });
};

const updateAssessmentScore = async (req, res) => {
  const { assessmentName } = req.params;
  const { fieldToUpdate, newValue } = req.body;
  const setObj = {};
  setObj[fieldToUpdate] = newValue;
  try {
    // Find the assessment by assessmentId and update the field
    const updatedAssessment = await CompanyAssessment.findOneAndUpdate(
      { _id: assessmentName },
      { $set: setObj },
      { new: true } // To return the updated document
    );

    if (!updatedAssessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    await updatedAssessment.save();
    return res.json(updatedAssessment);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "Internal server error" });
  }
};

const getHardSkillsFromDB = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });

        console.log({ companyfromhardskills: company });
        let premium = false;
        let hardSkills = await allAssessments.aggregate([
          {
            $match: {
              category: "Hard Skills",
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          hardSkills.map((assessment, index) => {
            // if (index < 3) {
            //   assessment.label = "free";
            // } else
            assessment.label = "premium";
          })
        );

        if (company?.plan == "free") {
          // hardSkills = hardSkills.slice(0, 8);
          premium = false;
        } else premium = true;
        return res.send({
          hardSkills,
          premium,
        });
      } catch (error) {
        console.log({ error });
        res.send("not Authorized");
      }
    });
  }
};

const getPsychometrics = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
    if (err) {
      console.log({ err });
      return res.status(401).json({
        title: "unauthorized",
      });
    }
    try {
      let company = await Company.findOne({
        name: decoded.company_name,
      });

      let psychometrics = await allAssessments.aggregate([
        {
          $match: {
            category: "Psychometrics",
            $expr: { $gt: [{ $size: "$questions_list" }, 14] },
            rating: { $ne: "top" }, // Add a condition to filter out assessments with rating: top
            $or: [
              { company: { $exists: false } }, // company is undefined
              { company: decoded.company_name }, // company matches decoded.id
            ],
          },
        },
        {
          $project: {
            company: 1,
            name: 1,
            category: 1,
            duration: 1,
            description_test: 1,
            _id: 1,
            questions_nbr: { $size: "$questions_list" },
          },
        },
      ]);
      await Promise.all(
        psychometrics.map((assessment, index) => {
          assessment.label = "premium";
        })
      );
      // if (company?.plan == "free") {
      //   psychometrics = psychometrics.slice(0, 8);
      // }

      return res.send(psychometrics);
    } catch (error) {
      console.log({ error });
    }
  });
};

const getSoftSkills = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });
        let softSkills = await allAssessments.aggregate([
          {
            $match: {
              category: "Soft Skills",
              $expr: { $gt: [{ $size: "$questions_list" }, 14] },
              rating: { $ne: "top" }, // Add a condition to filter out assessments with rating: top
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              rating: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          softSkills.map((assessment, index) => {
            assessment.label = "premium";
          })
        );
        // if (company?.plan == "free") {
        //   softSkills = softSkills.slice(0, 8);
        // }
        return res.send(softSkills);
      } catch (error) {
        console.log({ error });
      }
    });
  }
};


const getProgrammingSkills = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });
        let progSkills = await allAssessments.aggregate([
          {
            $match: {
              category: "Programming Skills",
              rating: { $ne: "top" }, // Add a condition to filter out assessments with rating: top
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              rating: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          progSkills.map((assessment, index) => {
            assessment.label = "premium";
          })
        );
        // if (company?.plan == "free") {
        //   softSkills = softSkills.slice(0, 8);
        // }
                console.log({ progSkills });
        return res.send(progSkills);
      } catch (error) {
        console.log({ error });
      }
    });
  }
};


const getPersonality = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });
        let personalityTest = await allAssessments.aggregate([
          {
            $match: {
              category: "Personality",
              $expr: { $gt: [{ $size: "$questions_list" }, 14] },
              rating: { $ne: "top" }, // Add a condition to filter out assessments with rating: top
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          personalityTest.map((assessment, index) => {
            assessment.label = "premium";
          })
        );
        console.log({ personalityTest });
        return res.send(personalityTest);
      } catch (error) {
        console.log({ error });
      }
    });
  }
};

const getTopAssessments = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });
        let topAssessments = await allAssessments.aggregate([
          {
            $match: {
              rating: "top",
              $expr: { $gt: [{ $size: "$questions_list" }, 14] },
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          topAssessments.map((assessment, index) => {
            // if (index < 3) {
            //   assessment.label = "free";
            // } else
            assessment.label = "premium";
          })
        );
        // if (company?.plan == "free") {
        //   topAssessments = topAssessments.slice(0, 3);
        // }
        return res.send(topAssessments);
      } catch (error) {
        console.log({ error });
      }
    });
  }
};


const getAllSkills = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });
        let AllSkills = await allAssessments.aggregate([
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              duration: 1,
              description_test: 1,
              rating: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          AllSkills.map((assessment, index) => {
            assessment.label = "premium";
          })
        );
        // if (company?.plan == "free") {
        //   softSkills = softSkills.slice(0, 8);
        // }
                console.log({ AllSkills });
        return res.send(AllSkills);
      } catch (error) {
        console.log({ error });
      }
    });
  }
};

const getCandidatesAssessments = async (req, res) => {
  if (req.cookies.candidatecookie) {
    try {
      let candidate = req.cookies.candidatecookie;
      jwt.verify(candidate, process.env.SECRET_WORD, async (err, decoded) => {
        if (err)
          return res.status(401).json({
            title: "unauthorized",
          });
        if (!decoded.projectId) {
          console.log({ decodedFROMCANDASSESS: decoded });
          const assessments = await getAssessmentsForTalent(decoded);
          console.log({ assessmentsFROMFFFF: assessments });
          let response = {
            email: decoded.email,
            assessmentId: decoded.assessmentId,
            assessments,
            isTalent: true,
            screeners: [],
            // here add custom
          };

          return res.send(response);
        }
        // Token is valid
        const projectId = decoded.projectId;
        const jobDetails = await companyProject.findOne({
          _id: validator.escape(projectId),
        });

        let assessments = [];
        let customAssessment = null;

        for (const assessment of jobDetails.assessments) {
          const fetchedAssessment = await allAssessments.findOne({
            _id: validator.escape(assessment._id),
          });

          if (
            fetchedAssessment &&
            fetchedAssessment.questions_list &&
            fetchedAssessment.questions_list.length > 25
          ) {
            // Shuffle the questions using the Fisher-Yates algorithm
            for (
              let i = fetchedAssessment.questions_list.length - 1;
              i > 0;
              i--
            ) {
              const j = Math.floor(Math.random() * (i + 1));
              [
                fetchedAssessment.questions_list[i],
                fetchedAssessment.questions_list[j],
              ] = [
                fetchedAssessment.questions_list[j],
                fetchedAssessment.questions_list[i],
              ];
            }

            // Select the first 20 questions
            fetchedAssessment.questions_list =
              fetchedAssessment.questions_list.slice(0, 20);
          }

          fetchedAssessment.answers = [];
          fetchedAssessment.ranges = [];

          if (fetchedAssessment.category === "Custom") {
            customAssessment = fetchedAssessment;
          } else {
            assessments.push(fetchedAssessment);
          }
        }

        // Append the custom assessment at the end if it exists
        if (customAssessment) {
          assessments.push(customAssessment);
        }

// Initialize an empty object for coding challenge details
let codingChallengeDetails = null;

if (jobDetails.is_coding_required) {

  try {

    const codingChallenge = await CodingChallenge.findOne({
            idProject: projectId,
          });
// 3. Randomly select one assessment
          const randomAssessment = codingChallenge.assessments[
      Math.floor(Math.random() * codingChallenge.assessments.length)
    ];

 // 4. Get full assessment with instructions
    const assessment = await Assessment.findById(randomAssessment._id);

// Select a random question
    const randomIndex = Math.floor(Math.random() * assessment.questions_list.length);
    const question = assessment.questions_list[randomIndex];
    // Get the corresponding answer (if needed for evaluation)
    const answer = assessment.answers ? assessment.answers[question.question_number.toString()] : null;


    // Prepare coding challenge details
        codingChallengeDetails = {
          assessmentId: assessment._id,
          assessmentName: assessment.name,
          assessmentDescription: assessment.description_test,
          question: {
            id: question._id,
            number: question.question_number,
            description: question.description,
            text: question.question,
            answer: answer, // Include the answer if needed
            
            //timeLimit: question.time || assessment.duration
          },
          // Include duration from either question or assessment
          //duration: question.time || assessment.duration
        };




  }
  
  catch (error) {
    console.error('Error fetching coding challenge:', error);
    // You might want to handle this error differently
  }

          

 
 

        }

        console.log(codingChallengeDetails)
        let response = {
          email: decoded.email,
          projectId,
          assessments,
          screeners: jobDetails.screeners,
          isCoding: jobDetails.is_coding_required,
          codingChallenge: codingChallengeDetails,
           //...(codingChallengeDetails && { codingChallenge: codingChallengeDetails })
  // The spread above will only add codingChallenge if it's not null

          // here add custom
        };

        res.send(response);
      });
    } catch (error) {
      console.log({ error });
    }
  }
};

const getEvaluations = async (req, res) => {
  try {
    const evaluationExists = await CandidateEvaluation.find();
    res.json(evaluationExists);
  } catch (error) {
    res.json({ error: error });
  }
};

// Function to send assessment completion email
const sendAssessmentCompletionEmail = async (candidateEmail) => {
  try {
    // Try to get candidate name from Candidate model first
    let candidateName = '';
    const candidateInfo = await Candidate.findOne({ Email: candidateEmail });
    if (candidateInfo) {
      candidateName = `${candidateInfo.First_name} ${candidateInfo.Last_name}`;
    } else {
      // Try InvitedCandidate model if not found in Candidate model
      const invitedCandidateInfo = await InvitedCandidate.findOne({ email: candidateEmail });
      if (invitedCandidateInfo) {
        candidateName = `${invitedCandidateInfo.first_name} ${invitedCandidateInfo.last_name}`;
      } else {
        // Use email as fallback if name not found
        candidateName = candidateEmail.split('@')[0];
      }
    }
    
    // Render email template
    const templatePath = path.join(__dirname, '../views/AssessmentCompletionEmail.ejs');
    const template = fs.readFileSync(templatePath, 'utf-8');
    const html = ejs.render(template, {
      candidateName,
      candidateEmail
    });
    
    // Send email
    await sendEmail(
      candidateEmail,
      'Assessment Completed Successfully!',
      html
    );
    
    console.log('Assessment completion email sent to:', candidateEmail);
    return { success: true, message: 'Email sent successfully' };
  } catch (emailError) {
    console.error('Error sending assessment completion email:', emailError);
    return { success: false, error: emailError.message };
  }
};

const postCandidatesAnswers = async (req, res) => {
  const {
    answers,
    exitedTab,
    exitCount,
    screens,
    fullScreenExited,
    mouseExited,
    isTalent,
  } = req.body;

  try {
    let candidate = req.cookies.candidatecookie;
    jwt.verify(candidate, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }

      if (isTalent) {
        console.log({ answers, isTalent, ANSWERS: answers.answers });
        let isSaved = await evaluateTalent(answers);
        if (isSaved) {
          return res.status(200).json({ isSaved });
        }
        return res.status(400).json({ isSaved });
      }

      const projectId = validator.escape(decoded.projectId);
      const candidate = validator.normalizeEmail(decoded.email, {
        gmail_remove_dots: false,
      });

      const evaluationExists = await CandidateEvaluation.findOne({
        candidate,
        projectId,
      });
      if (evaluationExists) {
        return res.status(400).json({ message: "Candidate already evaluated" });
      }

      let results = [];

      for (const assessmentName in answers.answers) {
        // answers.map(async (answer) => {
        const assessment = await allAssessments.findOne({
          name: assessmentName,
        });

        let totalPoints = 0;
        let rangesPoint = 0;
        let quesionsNbr = 0;
        let personalityResults = {};
        let customResults = [];

        // Compare candidate answers with correct answers and calculate points

        // for (const questionNumber in answers.answers[assessmentName]) {
        //   console.log({ totalPointInSide: totalPoints });
        //   if (
        //     !assessment.ranges &&
        //     parseInt(answers.answers[assessmentName][questionNumber]) ===
        //       assessment.answers[questionNumber]
        //   ) {
        //     totalPoints++;
        //   }
        // }

        if (assessment.category === "Custom") {
          customResults = answers.customAnswer;
        }
        if (assessment.category === "Personality") {
          const answersList = answers.answers[assessmentName];
          const traits = answers.traits[assessmentName];
          const traitScores = {};
          const categories = {
            "Very Low": 21,
            Low: 40,
            Medium: 60,
            High: 80,
          };
          // Calculate sum of scores for each trait
          for (const questionNumber in traits) {
            const trait = traits[questionNumber];
            const score = parseInt(answersList[questionNumber]);
            if (!traitScores[trait]) {
              traitScores[trait] = score;
            } else {
              traitScores[trait] += score;
            }
          }

          // Calculate total number of questions for each trait
          const traitQuestionCounts = {};
          for (const questionNumber in traits) {
            const trait = traits[questionNumber];
            if (!traitQuestionCounts[trait]) {
              traitQuestionCounts[trait] = 1;
            } else {
              traitQuestionCounts[trait]++;
            }
          }

          const finalScores = {};
          for (const trait in traitScores) {
            const totalQuestions = traitQuestionCounts[trait];
            const percentage =
              (traitScores[trait] / (totalQuestions * 5)) * 100; // Assuming each question has a maximum score of 5

            // Assign category based on percentage
            let category;
            for (const cat in categories) {
              if (percentage < categories[cat]) {
                category = cat;
                break;
              }
            }

            finalScores[trait] = category || "Very High"; // If percentage exceeds 80%
          }
          personalityResults = finalScores;
        }

        if (assessment.ranges) {
          for (const questionNumber in answers.answers[assessmentName]) {
            rangesPoint += parseInt(
              answers.answers[assessmentName][questionNumber]
            );
          }
        }
        if (!assessment.ranges && assessment.answers) {
          for (const questionNumber in answers.answers[assessmentName]) {
            if (
              parseInt(answers.answers[assessmentName][questionNumber]) ===
              assessment.answers[questionNumber]
            ) {
              totalPoints++;
            }
          }
        }
        if (assessment.questions_list.length > 25) {
          quesionsNbr = 20;
        } else {
          quesionsNbr = assessment.questions_list.length;
        }
        if (assessment.ranges) {
          results.push({
            assessmentName: assessmentName,
            rangesPoint,
            quesionsNbr,
          });
        } else if (assessment.category === "Personality") {
          results.push({
            assessmentName: assessmentName,
            personalityResults,
            quesionsNbr,
          });
        } else if (assessment.category === "Custom") {
          results.push({
            assessmentName: assessmentName,
            customResults,
            quesionsNbr,
          });
        } else {
          results.push({
            assessmentName: assessmentName,
            totalPoints,
            quesionsNbr,
          });
        }
      }
      const EvaluationResult = new CandidateEvaluation({
        candidate: answers.candidate,
        projectId: answers.projectId,
        exitedTab,
        mouseExited,
        fullScreenExited,
        exitCount,
        candidateTime: answers.candidateTime,
        results,
        screens,
      });

      let score = 0;
      EvaluationResult.results.map((result) => {
        score += (result.totalPoints * 100) / result.quesionsNbr;
      });
      console.log({
        Score: (score / EvaluationResult.results.length).toFixed(2),
      });

      await EvaluationResult.save();
      console.log({
        EvaluationResult: EvaluationResult.results,
        score: (score / EvaluationResult.results.length).toFixed(2),
      });
      
      // Send confirmation email to candidate 
      await sendAssessmentCompletionEmail(candidate);
      
      return res.json({ results });
    });
  } catch (error) {
    console.log({ error });
  }
};

const createCandidateRating = async (req, res) => {
  // console.log(req.body);
  try {
    const candidateRating = await CandidateRating.create(req.body);
    await candidateRating.save();
    console.log({ candidateRating });
    res.status(201).json({ message: "Candidate Rating created successfully!" });
  } catch (error) {
    console.log(error);
    res.json({ error });
  }
};

const getCandidateRating = async (req, res) => {
  const projectId = req.params.projectId;

  let candidatesRating = [];
  try {
    const candidateRating = await CandidateRating.find({
      projectId: validator.escape(projectId),
    });
    candidateRating.map((candidate) => {
      ratingFound = candidatesRating.find(
        (rating) =>
          rating.candidateEmail === candidate.candidateEmail &&
          rating.projectId === candidate.projectId
      );
      if (!ratingFound) {
        candidatesRating.push(candidate);
      }
    });

    res.json(candidatesRating);
  } catch (error) {
    console.log(error);
    res.json({ error });
  }
};
const getSampleQuestions = async (req, res) => {
  const { assessmentId } = req.query;
  try {
    const assessment = await allAssessments.findOne({
      _id: validator.escape(assessmentId),
    });
    if (!assessment) {
      return res.status(404).json({ message: "Assessment not found" });
    }
    const questions = assessment.questions_list.slice(0, 5);
    console.log({ questions });
    return res.json(questions);
  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: "Internal server error" });
  }
};
const getAllResults = async (req, res) => {
  try {
    const candidate = req.query.candidate;
    const projectId = req.query.projectId;
    const results = await CandidateEvaluation.find({
      // projectId:"6564ce68d5bfede461cf8cbc"
      projectId,
    });
    const candidateRes = results.map((result) => {
      let AssessmentScores = {};
      const Scores = result.results.map((result) => {
        AssessmentScores[result.assessmentName] =
          result.totalPoints / result.quesionsNbr;
        return {
          assessmentName: result.assessmentName,
          score: result.totalPoints / result.quesionsNbr,
        };
      });
      const AvgScore =
        Scores.reduce((prev, curr) => prev + curr.score, 0) / Scores.length;
      return {
        candidate: result.candidate,
        AssessmentScores,
        AvgScore,
      };
    });
    const sortedCandidateRes = candidateRes.sort(
      (a, b) => b.AvgScore - a.AvgScore
    );
    const candidateRanking = {
      totalCount: candidateRes.length,
      globalRank:
        sortedCandidateRes.findIndex((cand) => cand.candidate === candidate) +
        1,
    };

    const assessArray = Object.keys(candidateRes[0].AssessmentScores);
    assessArray.forEach((assess) => {
      const theAssessment = candidateRes.sort(
        (a, b) => b.AssessmentScores[assess] - a.AssessmentScores[assess]
      );
      candidateRanking[assess] =
        theAssessment.findIndex((cand) => cand.candidate === candidate) + 1;
    });
    /*
    <EMAIL>
    65a7a9fdcc36435c9a451c38
    -------
    <EMAIL>
    65f99b057a229c8335a08aa2
    */
    res.json(candidateRanking);
  } catch (error) {
    console.log(error);
    res.status(400).send("Was there an error");
  }
};

const getCustomQuestion = async (req, res) => {
  let token = req.cookies.user || req.cookies.admin;

  if (token) {
    jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      try {
        let company = await Company.findOne({
          name: decoded.company_name,
        });

        console.log({ companyfromhardskills: company });
        let premium = false;
        let customQuestion = await allAssessments.aggregate([
          {
            $match: {
              category: "Custom",
              $or: [
                { company: { $exists: false } }, // company is undefined
                { company: decoded.company_name }, // company matches decoded.id
              ],
            },
          },
          {
            $project: {
              company: 1,
              name: 1,
              category: 1,
              description_test: 1,
              _id: 1,
              questions_nbr: { $size: "$questions_list" },
            },
          },
        ]);
        await Promise.all(
          customQuestion.map((assessment, index) => {
            // if (index < 3) {
            //   assessment.label = "free";
            // } else
            assessment.label = "premium";
          })
        );

        if (company?.plan == "free") {
          // hardSkills = hardSkills.slice(0, 8);
          premium = false;
        } else premium = true;
        console.log({ customQuestion });
        return res.send({
          customQuestion,
          premium,
        });
      } catch (error) {
        console.log({ error });
        res.send("not Authorized");
      }
    });
  }
};

const submitScreeners = async (req, res) => {
  let { answers } = req.body;
  console.log({ answers });
  let candidate = req.cookies.candidatecookie;
  try {
    jwt.verify(candidate, process.env.SECRET_WORD, async (err, decoded) => {
      if (err) {
        console.log({ err });
        return res.status(401).json({
          title: "unauthorized",
        });
      }
      const projectId = validator.escape(decoded.projectId);
      const candidate = validator.normalizeEmail(decoded.email, {
        gmail_remove_dots: false,
      });
      const evaluationExists = await CandidateEvaluation.findOne({
        candidate,
        projectId,
      });
      if (evaluationExists) {
        return res.status(400).json({ message: "Candidate already evaluated" });
      }
      const project = await companyProject.findById(projectId);
      console.log({ project });
      let passed = true;

      await Promise.all(
        project.screeners.map(async (screener, index) => {
          let answer = answers[index];
          // console.log({ answer, screener });
          if (screener.type == "single") {
            for (const option of screener.options) {
              if (option.answer == answer?.answer) {
                console.log("answer match");
                if (!option.pass) {
                  console.log("candidate Fail");
                  const EvaluationResult = new CandidateEvaluation({
                    candidate: candidate,
                    projectId: projectId,
                    status: "Failed",
                    passed: false,
                    screeners: answers,
                  });
                  await EvaluationResult.save();
                  passed = false;
                  break; // Exit the loop once failure is detected
                }
              }
            }
          }
        })
      );

      if (passed) {
        console.log({ passed });
        return res.json({ message: "Candidate passed", passed: true });
      } else {
        console.log({ passed });
        return res.json({ message: "Candidate Failed", passed: false });
      }
    });
  } catch (err) {
    console.log(err);
    res.status(400).send("Was there an error");
  }
};

module.exports = {
  getAllAssessmentTest,
  getAssessmentTest,
  createAssessmentTest,
  deleteAssessmentTest,
  getCompanyAssessments,
  createCompanyAssessments,
  updateAssessmentScore,
  getPsychometrics,
  getSoftSkills,
  getProgrammingSkills,
  getTopAssessments,
  getAllSkills,
  getCandidatesAssessments,
  getHardSkillsFromDB,
  postCandidatesAnswers,
  createCandidateRating,
  getCandidateRating,
  getSampleQuestions,
  getPersonality,
  getEvaluations,
  getAllResults,
  getCustomQuestion,
  submitScreeners,
};
