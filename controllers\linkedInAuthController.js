const qs = require("querystring");
const axios = require("axios");
const jwt = require("jsonwebtoken");
const authorization = (req, res) => {
  console.log(
    `https://www.linkedin.com/oauth/v2/authorization?client_id=${process.env.CLIENT_ID}&response_type=code&scope=${process.env.SCOPE}&redirect_uri=${process.env.REDIRECT_URI}`
  );
  return encodeURI(
    `https://www.linkedin.com/oauth/v2/authorization?client_id=${process.env.CLIENT_ID}&response_type=code&scope=${process.env.SCOPE}&redirect_uri=${process.env.REDIRECT_URI}`
  );
};

const redirect = async (req) => {
  const code = req.query.code;
  let token = req.cookies.candidatecookie;
  console.log({ TTOOOOOOOOKKEEEENNNNNNN: token });
  const payload = qs.stringify({
    client_id: process.env.CLIENT_ID,
    client_secret: process.env.CLIENT_SECRET,
    redirect_uri: process.env.REDIRECT_URI,
    grant_type: "authorization_code",
    code: code,
  });
  console.log({ payload });

  const data = await axios({
    method: "POST",
    url: "https://www.linkedin.com/oauth/v2/accessToken",
    data: payload,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  })
    .then(async (res) => {
      const userInfo = await axios.get("https://api.linkedin.com/v2/userinfo", {
        headers: {
          Authorization: `Bearer ${res.data.access_token}`,
          "Content-Type": "application/json",
        },
      });

      console.log("User Info:", userInfo.data);

      let config2 = {
        method: "get",
        maxBodyLength: Infinity,
        url: `http://localhost:3000/certificate/candidateCertificateImg?fullName=${userInfo.data.name}`,
        // headers: {
        //   Cookie: token,
        // },
        data: {
          token: token,
          // Other data fields...
        },
        withCredentials: true,
      };

      const img = await axios
        .request(config2)
        .then((response) => {
          console.log(JSON.stringify(response.data));
        })
        .catch((error) => {
          console.log(error);
        });
      console.log({ img });
      const imgPath = `http://localhost:3000/images/certificates/${userInfo.data.name}.png`;

      jwt.verify(token, process.env.SECRET_WORD, async (err, decoded) => {
        if (err) {
          console.log({ err });
          return res.status(401).json({
            title: "unauthorized",
          });
        }
        decoded.imgPath = imgPath;

        token = jwt.sign(decoded, process.env.SECRET_WORD);
      });

      const postData = {
        author: `urn:li:person:${userInfo.data.sub}`,
        lifecycleState: "PUBLISHED",
        specificContent: {
          "com.linkedin.ugc.ShareContent": {
            shareCommentary: {
              text: "Hello from LinkedIn API! Check out this article.",
            },
            shareMediaCategory: "ARTICLE",
            mediaDescription: {
              text: "Description for the article",
            },
            media: [
              {
                originalUrl: `https://go-platform.com/talent/${token}`,
                thumbnailUrl:
                  "https://images.pexels.com/photos/268533/pexels-photo-268533.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500",

                title: {
                  text: "Certificate",
                },
                description: {
                  text: "Description for your image",
                },
                status: "READY",
              },
            ],
            mediaTitle: {
              text: "Title of the article",
            },
          },
        },
        visibility: {
          "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
        },
      };

      const config = {
        headers: {
          Authorization: `Bearer ${res.data.access_token}`,
          "Content-Type": "application/json",
        },
      };

      axios
        .post("https://api.linkedin.com/v2/ugcPosts", postData, config)
        .then((response) => {
          console.log("UGC Post created successfully:", response.data);
        })
        .catch((error) => {
          console.error("Error creating UGC Post:", error);
        });

      return res;
    })
    .catch((err) => {
      console.error({ err });
      return err;
    });
  console.log({ data: data });
  return data;
};

module.exports = {
  authorization,
  redirect,
};
