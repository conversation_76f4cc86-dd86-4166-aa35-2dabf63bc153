const Education = require("../models/Education.js");
const mongoose = require('mongoose');
const jwt = require("jsonwebtoken");

// Add a new education entry
const addEducation = async (req, res) => {
    const { degree, fieldOfStudy, currentlyStudyHere, school, degreeIssuanceCountry, startingDate, endingDate, fullDetails } = req.body;
    let user_token = req.cookies.candidate;
    if (user_token) {
        jwt.verify(user_token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            try {
                const newEducation = new Education({
                    candidate: decoded.id,
                    degree,
                    fieldOfStudy,
                    school,
                    degreeIssuanceCountry,
                    startingDate,
                    currentlyStudyHere,
                    endingDate,
                    fullDetails
                });

                const savedEducation = await newEducation.save();
                res.status(201).json({ message: 'Education entry created successfully', savedEducation });

            } catch (error) {
                console.error('Error adding education:', error);
                res.status(500).json({ error: 'Internal server error' });
            }
        })
    }
};

// Get all education entries for a user
const getEducationByCandidate = async (req, res) => {
    try {
        let token = req.cookies.candidate;

        jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
            if (err) {
                console.log({ err });
                return res.status(401).json({
                    title: "unauthorized",
                });
            }
            const educationEntries = await Education.find({ candidate: mongoose.Types.ObjectId(decoded.id) });

            if (!educationEntries || educationEntries.length === 0) {
                return res.status(204).json({ message: "No education entries found." });
            }

            res.status(200).json(educationEntries);
        })
    } catch (error) {
        console.error('Error retrieving education:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Get education entry by ID
const getEducationById = async (req, res) => {
    const educationId = req.params.id;

    try {
        const educationEntry = await Education.findById(educationId);

        if (!educationEntry) {
            return res.status(204).json({ message: "No education entry found." });
        }

        res.status(200).json(educationEntry);
    } catch (error) {
        console.error('Error retrieving education entry:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Update an education entry by ID
const updateEducationById = async (req, res) => {
    const educationId = req.params.id;

    try {
        const educationEntry = await Education.findById(educationId);

        if (!educationEntry) {
            return res.status(404).json({ message: "Education entry not found." });
        }

        // Update fields if provided
        const { degree, currentlyStudyHere, fieldOfStudy, school, degreeIssuanceCountry, startingDate, endingDate, fullDetails } = req.body;

        if (degree) educationEntry.degree = degree;
        if (fieldOfStudy) educationEntry.fieldOfStudy = fieldOfStudy;
        if (school) educationEntry.school = school;
        if (degreeIssuanceCountry) educationEntry.degreeIssuanceCountry = degreeIssuanceCountry;
        if (startingDate) educationEntry.startingDate = startingDate;
        if (endingDate) educationEntry.endingDate = endingDate;
        if (currentlyStudyHere) educationEntry.currentlyStudyHere = currentlyStudyHere;
        if (fullDetails) educationEntry.fullDetails = fullDetails;

        const updatedEntry = await educationEntry.save();
        res.status(200).json({ message: "Education entry updated successfully", updatedEntry });
    } catch (error) {
        console.error('Error updating education entry:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

// Delete an education entry by ID
const deleteEducationById = async (req, res) => {
    const educationId = req.params.id;

    try {
        const educationEntry = await Education.findByIdAndDelete(educationId);

        if (!educationEntry) {
            return res.status(204).json({ message: "No education entry found." });
        }

        res.status(200).json({ message: "Education entry deleted successfully", deletedEntry: educationEntry });
    } catch (error) {
        console.error('Error deleting education entry:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
};

module.exports = {
    addEducation,
    getEducationByCandidate,
    getEducationById,
    updateEducationById,
    deleteEducationById,
};
