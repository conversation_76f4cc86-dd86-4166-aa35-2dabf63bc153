require("dotenv").config();
const nodemailer = require("nodemailer");
const { sendEmail } = require("../utils/email");

const sendEmail = async (email, subject, html) => {
  try {
    const transporter = nodemailer.createTransport({
      service: process.env.SERVICE,
      auth: {
        user: process.env.COMPANY,
        pass: process.env.PASS,
      },
    });

    const emailSent = await transporter.sendMail({
      from: process.env.COMPANY,
      to: email,
      subject,
      html,
    });
  } catch (error) {
    const err = new ProjectError("email not sent");
    err.statusCode = 401;
    throw err;
  }
};

module.exports = { sendEmail };
