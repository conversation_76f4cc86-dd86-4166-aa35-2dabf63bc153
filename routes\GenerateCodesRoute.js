const express = require("express");
const router = express.Router();

const fs = require("fs");

// Function to generate a random code
function generateCode(length) {
  let result = "";
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

// Function to generate an array of codes
function generateCodeArray(count, length) {
  const codes = [];
  for (let i = 0; i < count; i++) {
    codes.push(generateCode(length));
  }
  return codes;
}

router.get("/", (req, res) => {
  // const codesArray = generateCodeArray(10003, 8);
  // // Convert array to JSON format
  // const codesJSON = JSON.stringify(codesArray);
  // console.log(codesJSON);
  // // Write JSON to file
  // fs.writeFile(
  //   "./controllers/promoCodes/lifeTimeCodes.json",
  //   codesJSON,
  //   "utf8",
  //   (err) => {
  //     if (err) {
  //       console.error("Error writing file:", err);
  //       res.send("Error writing file");
  //     } else {
  //       console.log("Codes have been saved to codes.json");
  //       res.send("Codes have been saved to codes.json");
  //     }
  //   }
  // );
});

router.get("/all", (req, res) => {
  // Function to delete a value from the JSON array

  // File name
  const filename = "./controllers/promoCodes/lifeTimeCodes.json";
  // Value to check and delete if found
  const valueToDelete = "k2lK5mdh";

  // Read the JSON file
});

module.exports = router;
