const interpretation = require("../models/interpretation");

const uploadInterpretation = async (req, res) => {
  const interpretations = req.body.interpretations;
  const assessmentName = req.body.assessmentName;
  try {

    const newInterpretation = new interpretation({
      assessmentName,
      interpretations,
    });
    await newInterpretation.save();


    res.status(200).json({ message: "interpretations uploaded successfully", interpretation: newInterpretation });
  } catch (error) {
    console.log({ error });
  }
};

const editInterpretation = async (req, res) => {
  const { id } = req.params;
  const { assessmentName, interpretations } = req.body;
  try {
    const updatedInterpretation = await interpretation.findByIdAndUpdate(
      id,
      { assessmentName, interpretations },
      { new: true }
    );
    if (!updatedInterpretation) {
      return res.status(404).json({ message: "Interpretation not found" });
    }
    res.status(200).json({ message: "Interpretation updated successfully", interpretation: updatedInterpretation });
  } catch (error) {
    res.status(500).json({ message: "Error updating interpretation", error });
  }
};

const fetchInterceptionByAssessmentName = async (req, res) => {

  const AssessmentName = req.body.assessmentName;

  console.log({ AssessmentName });

  try {
    const interpretationFetched = await interpretation.findOne({ assessmentName: AssessmentName });
    res.status(200).json(interpretationFetched);
  } catch (error) {
    res.json(error)
  }

};

module.exports = {
  uploadInterpretation,
  fetchInterceptionByAssessmentName,
  editInterpretation
};
